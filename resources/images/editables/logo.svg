<?xml version="1.0" encoding="UTF-8" standalone="no"?>
<!-- Created with Inkscape (http://www.inkscape.org/) -->

<svg
   xmlns:dc="http://purl.org/dc/elements/1.1/"
   xmlns:cc="http://creativecommons.org/ns#"
   xmlns:rdf="http://www.w3.org/1999/02/22-rdf-syntax-ns#"
   xmlns:svg="http://www.w3.org/2000/svg"
   xmlns="http://www.w3.org/2000/svg"
   xmlns:xlink="http://www.w3.org/1999/xlink"
   xmlns:sodipodi="http://sodipodi.sourceforge.net/DTD/sodipodi-0.dtd"
   xmlns:inkscape="http://www.inkscape.org/namespaces/inkscape"
   width="550"
   height="200"
   id="svg2"
   version="1.1"
   inkscape:version="0.48.3.1 r9886"
   sodipodi:docname="logo.svg"
   inkscape:export-filename="/home/<USER>/workspace/mercurial/mc2ui/resources/images/logo.png"
   inkscape:export-xdpi="90"
   inkscape:export-ydpi="90">
  <defs
     id="defs4">
    <linearGradient
       id="linearGradient3811">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop3813" />
      <stop
         style="stop-color:#ffffff;stop-opacity:0;"
         offset="1"
         id="stop3815" />
    </linearGradient>
    <linearGradient
       id="linearGradient3780">
      <stop
         style="stop-color:#ffffff;stop-opacity:1;"
         offset="0"
         id="stop3782" />
      <stop
         id="stop3788"
         offset="1"
         style="stop-color:#ffffff;stop-opacity:0;" />
    </linearGradient>
    <linearGradient
       inkscape:collect="always"
       xlink:href="#linearGradient3780"
       id="linearGradient3786"
       x1="300.02545"
       y1="213.62685"
       x2="300.02545"
       y2="298.07828"
       gradientUnits="userSpaceOnUse" />
    <filter
       inkscape:collect="always"
       id="filter5283"
       x="-0.036295621"
       width="1.0725912"
       y="-0.17295652"
       height="1.345913">
      <feGaussianBlur
         inkscape:collect="always"
         stdDeviation="5.1685773"
         id="feGaussianBlur5285" />
    </filter>
  </defs>
  <sodipodi:namedview
     id="base"
     pagecolor="#ffffff"
     bordercolor="#666666"
     borderopacity="1.0"
     inkscape:pageopacity="0.0"
     inkscape:pageshadow="2"
     inkscape:zoom="0.98994949"
     inkscape:cx="182.89554"
     inkscape:cy="144.62442"
     inkscape:document-units="px"
     inkscape:current-layer="layer1"
     showgrid="false"
     inkscape:window-width="1391"
     inkscape:window-height="876"
     inkscape:window-x="49"
     inkscape:window-y="24"
     inkscape:window-maximized="1"
     fit-margin-top="0"
     fit-margin-left="0"
     fit-margin-right="0"
     fit-margin-bottom="0" />
  <metadata
     id="metadata7">
    <rdf:RDF>
      <cc:Work
         rdf:about="">
        <dc:format>image/svg+xml</dc:format>
        <dc:type
           rdf:resource="http://purl.org/dc/dcmitype/StillImage" />
        <dc:title></dc:title>
      </cc:Work>
    </rdf:RDF>
  </metadata>
  <g
     inkscape:label="Livello 1"
     inkscape:groupmode="layer"
     id="layer1"
     transform="translate(-8.5258682,-691.04643)">
    <rect
       style="fill:#ffffff;fill-opacity:1;stroke:none;filter:url(#filter5283)"
       id="rect3809"
       width="341.76535"
       height="71.720833"
       x="106.06602"
       y="758.151"
       rx="0.99907607"
       ry="0.012878428" />
    <text
       xml:space="preserve"
       style="font-size:55.02446747px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:125%;letter-spacing:0px;word-spacing:0px;writing-mode:lr-tb;text-anchor:start;fill:#000000;fill-opacity:1;stroke:#4b4b4b;stroke-width:5.41340494;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;font-family:Sawasdee;-inkscape-font-specification:Sawasdee"
       x="178.10483"
       y="752.39661"
       id="text2985"
       sodipodi:linespacing="125%"
       transform="scale(0.9168729,1.0906637)"><tspan
         sodipodi:role="line"
         id="tspan2987"
         x="178.10483"
         y="752.39661"
         style="fill:#000000;fill-opacity:1;stroke:#4b4b4b;stroke-width:5.41340494;stroke-opacity:1">MasterCom</tspan></text>
    <text
       transform="scale(1.0547216,0.94811749)"
       sodipodi:linespacing="125%"
       id="text2997"
       y="834.74109"
       x="396.06058"
       style="font-size:45.26259995px;font-style:normal;font-variant:normal;font-weight:normal;font-stretch:normal;text-align:start;line-height:125%;letter-spacing:0px;word-spacing:0px;writing-mode:lr-tb;text-anchor:start;fill:#238fec;fill-opacity:1;stroke:#238fec;stroke-width:1.80687034;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none;font-family:Sawasdee;-inkscape-font-specification:Sawasdee"
       xml:space="preserve"><tspan
         y="834.74109"
         x="396.06058"
         id="tspan2999"
         sodipodi:role="line"
         style="fill:#238fec;fill-opacity:1;stroke:#238fec;stroke-width:1.80687034;stroke-miterlimit:4;stroke-opacity:1;stroke-dasharray:none">2</tspan></text>
    <path
       style="fill:#238fec;fill-opacity:1;stroke:none"
       d="m 108.12894,798.94764 0,24.90093 c 0,0.0105 0.65817,0.0282 1.46809,0.0282 l 48.02324,0 c 0.80991,0 1.46807,-0.0177 1.46807,-0.0282 l 0,-48.67258 c 0,-0.0105 -0.65816,0 -1.46807,0 l -25.69145,0 -23.79988,23.77165 z"
       id="path3774"
       inkscape:connector-curvature="0" />
    <path
       style="fill:#307bba;fill-opacity:1;stroke:none"
       d="m 131.92882,775.17136 -23.79988,23.77164 22.3318,0 c 0.80992,0 1.46808,0.0105 1.46808,0 l 0,-23.77164 z"
       id="rect3769"
       inkscape:connector-curvature="0" />
    <path
       sodipodi:type="arc"
       style="opacity:0.2;fill:url(#linearGradient3786);fill-opacity:1;stroke:none"
       id="path3776"
       sodipodi:cx="311.12698"
       sodipodi:cy="256.05325"
       sodipodi:rx="218.19295"
       sodipodi:ry="41.921329"
       d="m 529.31993,256.05325 a 218.19295,41.921329 0 1 1 -436.385894,0 218.19295,41.921329 0 1 1 436.385894,0 z"
       transform="matrix(0.7821407,0,0,0.39185141,33.378534,713.11)" />
  </g>
</svg>
