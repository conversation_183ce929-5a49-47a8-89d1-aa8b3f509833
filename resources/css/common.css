/*
    Document   : common.css
    Created on : 5-dic-2012, 16.08.01
    Author     : marco
    Description:
        Common style for mc2 user interface
*/



.icon-home {
    background-image: url(../images/home.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-employee {
    background-image: url(../images/user.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-ccp {
    background-image: url(../images/ccp.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-mail_account {
    background-image: url(../images/mail_account.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-warehouse {
    background-image: url(../images/warehouse.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-archive {
    background-image: url(../images/archive.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-protocol {
    background-image: url(../images/protocol.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-albo {
    background-image: url(../images/albo.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-trasparenza {
    background-image: url(../images/trasparenza.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-settings {
    background-image: url(../images/settings.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-exit {
    background-image: url(../images/exit.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-accept_warning {
    background-image: url(../images/archiveIcons/accept_warning.png);
    background-repeat: no-repeat;
    background-position: center;
}

.icon-menu-active {
    background-color: rgba(255,255,255,0.5);
}

.cancelled-protocol-row .x-grid-cell {
    background-color: rgba(180,180,180,0.5) !important;
    color: #999;
}

.cancelled-albo-row .x-grid-cell {
    background-color: rgba(180,180,180,0.5) !important;
    color: #999;
}

.icon-btn-menu {
    background-color: transparent;
    border: 0px;
}

.menu-cnt {
    background-image: url(../images/menu_background.jpg);
    color: #FFF;
    text-shadow: 1px 1px 2px #000;
    font-size: 13px;
    letter-spacing: 1px;
    text-align: center;
}

.bck-content {
    background-color: rgb(223, 232, 246);
}

.positive {
    color: green;
}

.negative {
    color: red;
}

.neutral {
    color: blue;
}

/* Notification section */

.ux-notification-window .x-window-body {
    text-align: center;
    padding: 15px 5px 15px 5px;
    width: 200px;
}

.ux-notification-icon-information {
    background-image: url('../images/ux-notification/icon16_info.png');
}

.ux-notification-icon-error {
    background-image: url('../images/ux-notification/icon16_error.png');
}

.ux-notification-light .x-window-header {
    background-color: transparent;
}

body .ux-notification-light {
    background-image: url('../images/ux-notification/fader.png');
}

.ux-notification-light .x-window-body {
    text-align: center;
    padding: 15px 5px 18px 5px;
    width: 200px;
    background-color: transparent;
    border: 0px solid white;
}

/* End notification section */

/* Release notes */

ul.rn_added {
    list-style-type: disc;
}

ul.rn_fixed {
    list-style-type: square;
}

ul.rn_removed {
    list-style-type: circle;
}

/* End Release note */

/* ExtJS Fixes */

.x-tab-inner-left {
    text-align: left;
}


/* Rimuove l'iconcina +/- solo nella griglia ProtocolCorrespondentsGrid */
.protocol-correspondents-grid .x-grid-group-title {
    background-image: none !important;
    padding-left: 4px !important; /* evita spazio lasciato dall’icona */
    cursor: default;
}

