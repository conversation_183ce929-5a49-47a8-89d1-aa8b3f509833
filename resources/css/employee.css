/*
    Document   : employee
    Created on : 24-gen-2013, 11.59.02
    Author     : <PERSON><PERSON><PERSON>:
        Style for all employee sections
*/

div.day-box {
    margin:1px;
    float:left;
    width:90px;
    height:80px;
    border-width: 2px;
    border-style: solid;
    border-color: silver;
    padding:2px;
    font-size:10px;
    background-color:  #FFF;
}

div.day-box-disabled {
    background-color: #E0E0E0;
    color: #444;
}

div.day-box-active {
    cursor: pointer;
}

div.day-box-weekend{
    border-color: orange;
}

div.day-box-festivity{
    border-color: red;
}

.day-header{
    text-align: right;
    background-color: lightBlue;
    padding: 2px;
    margin: 0px;
}

.day-header-disabled{
    background-color: #C0C0C0;
}

.day-hour-box{
    text-align: center;
    margin-top: 10px;
    padding: 2px;
    background-color: #093;
    color: white;
    font-weight: bold;
    border-radius: 2px;
}

.day-hour-box-break{
    border: 2px dotted #444;
}

.day-hour-box-hide{
    display: none;
}

div.week-day-style{
    height: 25px;
    background-image: -webkit-linear-gradient(top,#F9F9F9,#E3E4E6);
    background-image: -moz-linear-gradient(top,#F9F9F9,#E3E4E6);
    background-image: -o-linear-gradient(top,#F9F9F9,#E3E4E6);
    background-image: -ms-linear-gradient(top,#F9F9F9,#E3E4E6);
    background-image: linear-gradient(top,#F9F9F9,#E3E4E6);
}

div.week-day-style .day-hour-box{
    display: none;
}

div.week-day-style .day-header {
    background-color: transparent;
    text-align: center;
}

.employee-inactive {
    color:#AAA;
}

.stack-recover{
    color: #AAA;
}

.kind-disable-linked{
    color: #AAA;
}