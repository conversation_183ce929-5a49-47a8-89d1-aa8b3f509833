
#!/bin/bash
# filepath: /home/<USER>/workspace/mc2-ui/deploy_tmp.sh

# Check if remote server parameter is provided
if [ -z "$1" ]; then
  echo "Usage: $0 user@server"
  exit 1
fi

REMOTE_SERVER="$1"

# Execute npm run build
echo "Running 'npm run build'..."
npm run build
if [ $? -ne 0 ]; then
  echo "Error: 'npm run build' failed."
  exit 1
fi

# Zip the folder 'dist' into /tmp/dist.zip
ZIPFILE="/tmp/dist.zip"
# remove any existing zip file
if [ -f "$ZIPFILE" ]; then
  echo "Removing existing zip file: $ZIPFILE"
  rm "$ZIPFILE"
fi
echo "Creating zip archive of 'dist' folder -> $ZIPFILE"
(cd dist && zip -r "$ZIPFILE" .)
if [ $? -ne 0 ]; then
  echo "Error: failed to create zip archive."
  exit 1
fi

# Copy the zip file to the remote server's /tmp directory
echo "Copying zip file to remote server: $REMOTE_SERVER:/tmp/"
scp "$ZIPFILE" "$REMOTE_SERVER:/tmp/"
if [ $? -ne 0 ]; then
  echo "Error: SCP failed."
  exit 1
fi

# On the remote server, move previous deployment and unpack the new zip into /var/www/mc2-ui
echo "Deploying on remote server..."
ssh "$REMOTE_SERVER" "\
  if [ -d /var/www/mc2-ui ]; then \
    sudo rm -rf /var/www/mc2-ui-bck; \
    sudo mv /var/www/mc2-ui /var/www/mc2-ui-bck; \
    sudo rm -rf /var/www/mc2-ui; \
  fi && \
  sudo unzip -o /tmp/dist.zip -d /var/www/mc2-ui && \
  rm /tmp/dist.zip && \
  sudo chown -R www-data:www-data /var/www/mc2-ui && \
  sudo sed -i 's/index\.php/index.html/g' /etc/apache2/sites-available/master-mc2-ui.conf && \
  sudo a2dissite master-mc2-ui.conf && \
  sudo a2ensite master-mc2-ui.conf && \
  sudo service apache2 reload"
if [ $? -ne 0 ]; then
  echo "Error: remote deployment failed."
  exit 1
fi

echo "Deployment completed successfully."