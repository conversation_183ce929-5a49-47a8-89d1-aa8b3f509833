/*
 * File: app/model/ArchiveClass.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ArchiveClass', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'code',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'format',
            type: 'string'
        },
        {
            name: 'action',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                var action = '',
                    code = rec.get('action');

                if (code === 'A') {
                    action = 'Archiviazione';
                } else if (code === 'C') {
                    action = 'Conservazione';
                } else if (code === 'N') {
                    action = 'Nulla';
                }

                return action;
            },
            name: 'action_description',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                var extension = rec.get('format') === 'ALL' ? '' : ' (' + rec.get('format') + ')';

                return rec.get('name') + extension;
            },
            name: 'display_field',
            type: 'string'
        },
        {
            name: 'editable',
            type: 'boolean'
        }
    ]
});