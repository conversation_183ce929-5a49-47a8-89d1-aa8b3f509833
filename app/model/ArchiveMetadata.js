/*
 * File: app/model/ArchiveMetadata.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ArchiveMetadata', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'code',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'optional',
            type: 'boolean'
        },
        {
            name: 'sequential',
            type: 'boolean'
        },
        {
            name: 'preservation_date',
            type: 'boolean'
        },
        {
            name: 'class_id',
            type: 'int'
        },
        {
            name: 'kind',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                var kind = '',
                    code = rec.get('kind');

                if (code === 'D') {
                    action = 'Data';
                } else if (code === 'I') {
                    action = 'Numerico';
                } else if (code === 'S') {
                    action = 'Testo';
                }

                return action;
            },
            name: 'kind_description',
            type: 'string'
        },
        {
            name: 'value'
        }
    ]
});