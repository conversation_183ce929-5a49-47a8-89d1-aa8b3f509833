/*
 * File: app/model/CcpStudent.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpStudent', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id'
        },
        {
            name: 'text',
            type: 'string'
        },
        {
            name: 'db_id',
            type: 'int'
        },
        {
            name: 'seat_id',
            type: 'int'
        },
        {
            name: 'class',
            type: 'string'
        },
        {
            name: 'section',
            type: 'string'
        },
        {
            name: 'school_address_code',
            type: 'string'
        },
        {
            name: 'school_address',
            type: 'string'
        },
        {
            name: 'school_address_id',
            type: 'string'
        },
        {
            name: 'text_complete',
            type: 'string'
        },
        {
            name: 'movements'
        },
        {
            name: 'subject_school_year'
        }
    ]
});