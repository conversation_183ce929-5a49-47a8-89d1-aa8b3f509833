/*
 * File: app/model/ArchiveMailAccount.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ArchiveMailAccount', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id'
        },
        {
            name: 'users',
            type: 'int'
        },
        {
            name: 'name'
        },
        {
            name: 'protocol',
            type: 'int'
        },
        {
            name: 'host'
        },
        {
            name: 'port'
        },
        {
            name: 'security',
            type: 'int'
        },
        {
            name: 'username'
        },
        {
            name: 'password'
        },
        {
            name: 'active',
            type: 'boolean'
        },
        {
            name: 'protocol_text'
        },
        {
            name: 'security_text'
        },
        {
            name: 'out',
            type: 'boolean'
        },
        {
            name: 'sender'
        },
        {
            name: 'email'
        }
    ]
});