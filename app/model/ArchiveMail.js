/*
 * File: app/model/ArchiveMail.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ArchiveMail', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id'
        },
        {
            name: 'account',
            type: 'int'
        },
        {
            name: 'account_text'
        },
        {
            name: 'date',
            type: 'date'
        },
        {
            name: 'from'
        },
        {
            name: 'to'
        },
        {
            name: 'cc'
        },
        {
            name: 'ccn'
        },
        {
            name: 'subject'
        },
        {
            name: 'message'
        },
        {
            name: 'deleted',
            type: 'boolean'
        },
        {
            name: 'attachments',
            type: 'int'
        },
        {
            name: 'out'
        },
        {
            name: 'documents',
            type: 'int'
        },
        {
            name: 'message_text'
        },
        {
            name: 'assign_from'
        },
        {
            name: 'sent',
            type: 'date'
        }
    ]
});