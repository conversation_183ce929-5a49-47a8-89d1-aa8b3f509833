/*
 * File: app/model/CcpMovement.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpMovement', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'locked',
            type: 'boolean'
        },
        {
            name: 'number',
            type: 'int',
            useNull: true
        },
        {
            name: 'school_year',
            type: 'string'
        },
        {
            name: 'creation_date',
            type: 'date'
        },
        {
            name: 'expiration_date',
            type: 'date'
        },
        {
            name: 'type_id',
            type: 'int'
        },
        {
            name: 'type_text',
            type: 'string'
        },
        {
            name: 'incoming',
            type: 'boolean'
        },
        {
            name: 'remaining',
            type: 'float'
        },
        {
            defaultValue: 0.00,
            name: 'amount',
            type: 'float'
        },
        {
            defaultValue: 0.00,
            name: 'total',
            type: 'float'
        },
        {
            name: 'miscellaneous',
            type: 'string'
        },
        {
            name: 'note',
            type: 'string'
        },
        {
            name: 'subject_type',
            type: 'string'
        },
        {
            name: 'subject_id',
            type: 'int',
            useNull: true
        },
        {
            name: 'subject_data',
            type: 'string',
            useNull: true
        },
        {
            name: 'subject_seat',
            type: 'int',
            useNull: true
        },
        {
            name: 'subject_class',
            type: 'string',
            useNull: true
        },
        {
            name: 'subject_school_address_code',
            type: 'string',
            useNull: true
        },
        {
            name: 'subject_school_address',
            type: 'string',
            useNull: true
        },
        {
            name: 'subject_school_year',
            type: 'string',
            useNull: true
        },
        {
            name: 'linked_additionals'
        },
        {
            name: 'linked_payments'
        },
        {
            name: 'expired',
            type: 'boolean'
        },
        {
            name: 'count_additionals',
            type: 'int'
        },
        {
            name: 'count_payments',
            type: 'int'
        },
        {
            name: 'positive_additionals_euro',
            type: 'float'
        },
        {
            name: 'negative_additionals_euro',
            type: 'float'
        },
        {
            name: 'expirations'
        },
        {
            name: 'subjects'
        },
        {
            name: 'category_text'
        },
        {
            convert: function(v, rec) {
                if (v) return v.join(', ');
            },
            name: 'accountholder'
        },
        {
            name: 'total_payments'
        },
        {
            name: 'invoice_code'
        },
        {
            name: 'last_payment',
            type: 'date'
        },
        {
            name: 'invoice_number'
        },
        {
            name: 'linked_receipts'
        },
        {
            name: 'invoice_id',
            type: 'int'
        },
        {
            name: 'description'
        },
        {
            name: 'da_ratei',
            type: 'date'
        },
        {
            name: 'a_ratei',
            type: 'date'
        },
        {
            name: 'last_payment_method_text'
        },
        {
            name: 'consolidamento',
            type: 'boolean'
        },
        {
            name: 'easy_select',
            type: 'boolean'
        },
        {
            name: 'movement_description'
        },
        {
            name: 'covered_invoices'
        },
        {
            name: 'total_additionals'
        },
        {
            name: 'vat',
            type: 'float'
        },
        {
            name: 'gross',
            type: 'float'
        },
        {
            name: 'matthaeus_year'
        },
        {
            name: 'date_published',
            type: 'date'
        },
        {
            name: 'conto_risconti'
        },
        {
            name: 'conto_crediti'
        },
        {
            name: 'conto_ricavi'
        }
    ]
});