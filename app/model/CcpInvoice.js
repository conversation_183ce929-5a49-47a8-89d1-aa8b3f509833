/*
 * File: app/model/CcpInvoice.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpInvoice', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id'
        },
        {
            name: 'number'
        },
        {
            name: 'date',
            type: 'date'
        },
        {
            name: 'total'
        },
        {
            convert: function(v, rec) {

                var accountHolder = [],
                    name;

                Ext.each(v, function(val){
                    name = val.surname + ' ' + val.name + ' (' + val.fiscal_code	 + ')';
                    accountHolder.push(name);
                });


                return accountHolder.join(', ');
            },
            name: 'accountholder'
        },
        {
            convert: function(v, rec) {
                var debitors = [];

                Ext.each(v, function(val){
                    if (val.subject_data && debitors.indexOf(val.subject_data) < 0) debitors.push(val.subject_data);
                });

                return debitors.join(', ');
            },
            mapping: 'rows',
            name: 'debitors'
        },
        {
            name: 'fpa_status'
        },
        {
            name: 'ds_status'
        },
        {
            name: 'editable',
            type: 'boolean'
        },
        {
            name: 'credit_note'
        },
        {
            name: 'expiration_date',
            type: 'date'
        },
        {
            name: 'publication_path'
        },
        {
            name: 'expirations',
            type: 'string'
        },
        {
            name: 'total_expirations',
            type: 'string'
        },
        {
            name: 'remain_to_pay'
        },
        {
            name: 'error_expirations',
            type: 'string'
        },
        {
            name: 'suffix'
        }
    ]
});