/*
 * File: app/model/EmployeeTree.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.EmployeeTree', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'string'
        },
        {
            name: 'employee_id',
            type: 'int'
        },
        {
            name: 'surname',
            type: 'string'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                return rec.get('surname') + '&nbsp;&nbsp;&nbsp;' + rec.get('name');
            },
            name: 'denomination'
        },
        {
            name: 'active',
            type: 'boolean'
        }
    ]
});