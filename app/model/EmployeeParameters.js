/*
 * File: app/model/EmployeeParameters.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.EmployeeParameters', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'employee_id',

    fields: [
        {
            name: 'employee_id',
            type: 'int'
        },
        {
            name: 'max_cont_work',
            type: 'int'
        },
        {
            name: 'step_in',
            type: 'int'
        },
        {
            name: 'step_out',
            type: 'int'
        },
        {
            name: 'step_in_und',
            type: 'int'
        },
        {
            name: 'step_out_und',
            type: 'int'
        },
        {
            name: 'step_total_undefined',
            type: 'int'
        },
        {
            name: 'step_total_extraordinary',
            type: 'int'
        },
        {
            name: 'max_extraordinary_in',
            type: 'int'
        },
        {
            name: 'max_extraordinary_out',
            type: 'int'
        },
        {
            name: 'min_extraordinary_in',
            type: 'int'
        },
        {
            name: 'min_extraordinary_out',
            type: 'int'
        },
        {
            name: 'max_undefined_in',
            type: 'int'
        },
        {
            name: 'max_undefined_out',
            type: 'int'
        },
        {
            name: 'min_undefined_in',
            type: 'int'
        },
        {
            name: 'min_undefined_out',
            type: 'int'
        },
        {
            name: 'min_extraordinary_total',
            type: 'int'
        },
        {
            name: 'max_extraordinary_total',
            type: 'int'
        },
        {
            name: 'min_undefined_total',
            type: 'int'
        },
        {
            name: 'max_undefined_total',
            type: 'int'
        },
        {
            name: 'unit_recover_hours',
            type: 'string'
        },
        {
            name: 'break_after_max_work',
            type: 'int'
        },
        {
            name: 'recover_hours',
            type: 'int'
        }
    ]
});