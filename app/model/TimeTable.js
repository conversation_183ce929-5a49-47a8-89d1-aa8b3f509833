/*
 * File: app/model/TimeTable.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.TimeTable', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'personnel_timetable_id',

    fields: [
        {
            name: 'personnel_timetable_id'
        },
        {
            name: 'employee_id'
        },
        {
            dateFormat: 'Y-m-d H:i:s',
            name: 'date_s',
            type: 'date'
        },
        {
            dateFormat: 'Y-m-d H:i:s',
            name: 'date_e',
            type: 'date'
        },
        {
            dateFormat: 'Y-m-d H:i:s',
            name: 'date_s_p',
            type: 'date'
        },
        {
            dateFormat: 'Y-m-d H:i:s',
            name: 'date_e_p',
            type: 'date'
        },
        {
            dateFormat: 'Y-m-d',
            name: 'date_day',
            type: 'date'
        },
        {
            name: 'locked',
            type: 'boolean'
        }
    ]
});