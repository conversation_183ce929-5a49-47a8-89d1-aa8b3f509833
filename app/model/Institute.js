/*
 * File: app/model/Institute.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.Institute', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'institute_id',

    fields: [
        {
            name: 'institute_id',
            type: 'int'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'mechan_code',
            type: 'string'
        },
        {
            name: 'fiscal_code',
            type: 'string'
        },
        {
            name: 'def',
            type: 'boolean'
        },
        {
            name: 'postal_account',
            type: 'string'
        },
        {
            name: 'city',
            type: 'string'
        },
        {
            name: 'city_id',
            type: 'int'
        },
        {
            name: 'address',
            type: 'string'
        }
    ]
});