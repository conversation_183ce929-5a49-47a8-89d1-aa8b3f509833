/*
 * File: app/model/CcpPayment.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpPayment', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'movement_id',
            type: 'int'
        },
        {
            name: 'movement_number',
            type: 'string'
        },
        {
            name: 'operation_date',
            type: 'date'
        },
        {
            name: 'accountable_date',
            type: 'date'
        },
        {
            name: 'amount',
            type: 'float'
        },
        {
            name: 'payment_method_id',
            type: 'int'
        },
        {
            name: 'payment_method_text',
            type: 'string'
        },
        {
            name: 'account_id',
            type: 'int'
        },
        {
            name: 'account_text',
            type: 'string'
        },
        {
            name: 'bollettino',
            type: 'string'
        },
        {
            name: 'account_reference',
            type: 'string'
        },
        {
            name: 'payer_type',
            type: 'string'
        },
        {
            name: 'payer_id',
            type: 'int'
        },
        {
            name: 'payer_name',
            type: 'string'
        },
        {
            name: 'payer_surname',
            type: 'string'
        },
        {
            name: 'payer_fiscal_code',
            type: 'string'
        },
        {
            name: 'payer_address',
            type: 'string'
        },
        {
            name: 'payer_city',
            type: 'string'
        },
        {
            name: 'payer_province',
            type: 'string'
        },
        {
            name: 'payer_zip_code',
            type: 'string'
        },
        {
            name: 'payer_data',
            type: 'string'
        },
        {
            name: 'receipt_id',
            type: 'int'
        },
        {
            name: 'receipt_number',
            type: 'int'
        },
        {
            name: 'receipt_date',
            type: 'date'
        },
        {
            name: 'subject_type',
            type: 'string'
        },
        {
            name: 'subject_id',
            type: 'int'
        },
        {
            name: 'subject_data',
            type: 'string'
        },
        {
            name: 'subject_class'
        },
        {
            name: 'type_id',
            type: 'int'
        },
        {
            name: 'type_text',
            type: 'string'
        },
        {
            name: 'category_id',
            type: 'int'
        },
        {
            name: 'category_text',
            type: 'string'
        },
        {
            name: 'incoming',
            type: 'boolean'
        },
        {
            name: 'count_additionals',
            type: 'int'
        },
        {
            name: 'linked_additionals'
        },
        {
            name: 'positive_additionals_euro',
            type: 'float'
        },
        {
            name: 'negative_additionals_euro',
            type: 'float'
        },
        {
            name: 'positive_additionals_perc',
            type: 'float'
        },
        {
            name: 'negative_additionals_perc',
            type: 'float'
        },
        {
            name: 'total',
            type: 'float'
        },
        {
            name: 'ccp_credit'
        },
        {
            name: 'covered_movement_id'
        },
        {
            name: 'covered_movement_text'
        },
        {
            name: 'receipt',
            type: 'boolean'
        },
        {
            name: 'ccp_deposit_slip',
            type: 'int'
        },
        {
            name: 'payment_group',
            type: 'int',
            useNull: true
        }
    ]
});