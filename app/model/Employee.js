/*
 * File: app/model/Employee.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.Employee', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'employee_id',

    fields: [
        {
            name: 'employee_id',
            type: 'int'
        },
        {
            name: 'surname',
            type: 'string'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'gender',
            type: 'string'
        },
        {
            dateFormat: 'd/m/Y',
            name: 'birthdate',
            type: 'date'
        },
        {
            name: 'fiscal_code',
            type: 'string'
        },
        {
            name: 'birthplace',
            type: 'int'
        },
        {
            convert: function(v, rec) {
                return v < 1 ? '' : v;
            },
            name: 'badge_number'
        },
        {
            name: 'liquid_group',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                return rec.get('surname') + ' ' + rec.get('name');
            },
            name: 'denomination',
            type: 'string'
        },
        {
            name: 'state_birth'
        },
        {
            name: 'social_position',
            type: 'string'
        },
        {
            name: 'res_address',
            type: 'string'
        },
        {
            name: 'res_phone_num',
            type: 'string'
        },
        {
            name: 'res_fax',
            type: 'string'
        },
        {
            name: 'res_mobile',
            type: 'string'
        },
        {
            name: 'res_cap',
            type: 'string'
        },
        {
            name: 'res_city_id',
            type: 'int'
        },
        {
            name: 'res_email',
            type: 'string'
        },
        {
            name: 'add_address',
            type: 'string'
        },
        {
            name: 'add_phone_num',
            type: 'string'
        },
        {
            name: 'add_fax',
            type: 'string'
        },
        {
            name: 'add_mobile',
            type: 'string'
        },
        {
            name: 'add_cap',
            type: 'string'
        },
        {
            name: 'add_city_id',
            type: 'int'
        },
        {
            name: 'add_email',
            type: 'string'
        },
        {
            name: 'address_id',
            type: 'int'
        },
        {
            name: 'residence_id',
            type: 'int'
        },
        {
            name: 'max_cont_work',
            type: 'int'
        },
        {
            name: 'tolerance_in',
            type: 'int'
        },
        {
            name: 'tolerance_in_und',
            type: 'int'
        },
        {
            name: 'tolerance_out',
            type: 'int'
        },
        {
            name: 'tolerance_out_und',
            type: 'int'
        },
        {
            name: 'step_in',
            type: 'int'
        },
        {
            name: 'step_out',
            type: 'int'
        },
        {
            name: 'step_in_und',
            type: 'int'
        },
        {
            name: 'step_out_und',
            type: 'int'
        },
        {
            name: 'step_total_undefined',
            type: 'int'
        },
        {
            name: 'step_total_extraordinary',
            type: 'int'
        },
        {
            name: 'max_extraordinary_in',
            type: 'int'
        },
        {
            name: 'max_extraordinary_out',
            type: 'int'
        },
        {
            name: 'min_extraordinary_in',
            type: 'int'
        },
        {
            name: 'min_extraordinary_out',
            type: 'int'
        },
        {
            name: 'max_undefined_in',
            type: 'int'
        },
        {
            name: 'max_undefined_out',
            type: 'int'
        },
        {
            name: 'min_undefined_in',
            type: 'int'
        },
        {
            name: 'min_undefined_out',
            type: 'int'
        },
        {
            name: 'min_extraordinary_total',
            type: 'int'
        },
        {
            name: 'max_extraordinary_total',
            type: 'int'
        },
        {
            name: 'min_undefined_total',
            type: 'int'
        },
        {
            name: 'max_undefined_total',
            type: 'int'
        },
        {
            name: 'unit_recover_hours',
            type: 'string'
        },
        {
            name: 'break_after_max_work',
            type: 'int'
        },
        {
            name: 'recover_hours',
            type: 'int'
        },
        {
            name: 'active',
            type: 'boolean'
        },
        {
            name: 'job',
            type: 'string',
            useNull: true
        },
        {
            name: 'job_description',
            type: 'string',
            useNull: true
        },
        {
            name: 'max_work',
            type: 'int'
        }
    ]
});