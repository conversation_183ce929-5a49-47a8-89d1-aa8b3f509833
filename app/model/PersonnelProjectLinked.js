/*
 * File: app/model/PersonnelProjectLinked.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.PersonnelProjectLinked', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'project_id',
            type: 'int'
        },
        {
            name: 'employee_id',
            type: 'int'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'aggregate_code',
            type: 'string'
        },
        {
            name: 'aggregate_number',
            type: 'int'
        },
        {
            name: 'start_date',
            type: 'date'
        },
        {
            name: 'end_date',
            type: 'date'
        },
        {
            name: 'hour_insertions_end_date',
            type: 'date'
        },
        {
            name: 'year',
            type: 'int'
        },
        {
            name: 'suspended',
            type: 'boolean'
        },
        {
            name: 'expired',
            type: 'boolean'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'objectives',
            type: 'string'
        },
        {
            name: 'responsibles',
            type: 'string'
        },
        {
            name: 'goods_services',
            type: 'string'
        },
        {
            name: 'human_resources',
            type: 'string'
        },
        {
            name: 'count_personnel',
            type: 'int'
        },
        {
            name: 'count_hour_types',
            type: 'int'
        },
        {
            name: 'linked_personnel',
            type: 'string'
        },
        {
            name: 'linked_hour_types',
            type: 'string'
        },
        {
            name: 'locked',
            type: 'boolean'
        },
        {
            name: 'closed',
            type: 'boolean'
        }
    ]
});