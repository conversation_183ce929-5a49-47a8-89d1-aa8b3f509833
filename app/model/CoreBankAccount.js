/*
 * File: app/model/CoreBankAccount.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CoreBankAccount', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'country_code',
            type: 'string'
        },
        {
            name: 'check_code',
            type: 'string'
        },
        {
            name: 'bban',
            type: 'string'
        },
        {
            name: 'denomination',
            type: 'string'
        },
        {
            name: 'initial_balance',
            type: 'float'
        },
        {
            name: 'type',
            type: 'string'
        },
        {
            name: 'ise_id',
            type: 'int'
        },
        {
            name: 'ise_type',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                return rec.get('country_code') + rec.get('check_code') + rec.get('bban');
            },
            name: 'iban',
            type: 'string'
        },
        {
            name: 'invoice_default',
            type: 'boolean'
        },
        {
            name: 'cuc'
        },
        {
            name: 'creditor_identifier'
        }
    ]
});