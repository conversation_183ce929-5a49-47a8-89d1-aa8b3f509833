/*
 * File: app/model/Supplier.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.Supplier', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'fiscal_code',

    fields: [
        {
            name: 'fiscal_code'
        },
        {
            name: 'vat_number'
        },
        {
            name: 'name'
        },
        {
            name: 'address'
        },
        {
            name: 'city_id'
        },
        {
            name: 'city_name'
        },
        {
            name: 'city_province'
        },
        {
            name: 'city_postal_code'
        },
        {
            convert: function(v, rec) {
                var descr = rec.get('name');
                if(rec.get('fiscal_code')) descr += ' (' + rec.get('fiscal_code') +')';
                return descr;
            },
            name: 'description'
        }
    ]
});