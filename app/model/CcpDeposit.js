/*
 * File: app/model/CcpDeposit.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpDeposit', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id'
        },
        {
            name: 'operation_date'
        },
        {
            name: 'accountable_date'
        },
        {
            name: 'amount'
        },
        {
            name: 'payment_method_id'
        },
        {
            name: 'payment_method_text'
        },
        {
            name: 'payer_type'
        },
        {
            name: 'payer_id'
        },
        {
            name: 'payer_surname'
        },
        {
            name: 'payer_name'
        },
        {
            name: 'payer_fiscal_code'
        },
        {
            name: 'payer_address'
        },
        {
            name: 'payer_city'
        },
        {
            name: 'payer_province'
        },
        {
            name: 'payer_zip_code'
        },
        {
            name: 'credits_id'
        },
        {
            name: 'locked',
            type: 'boolean'
        }
    ]
});