/*
 * File: app/model/Presence.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.Presence', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'personnel_presence_id',

    fields: [
        {
            name: 'personnel_presence_id',
            type: 'int'
        },
        {
            name: 'employee_id',
            type: 'int'
        },
        {
            name: 'project_id',
            type: 'int'
        },
        {
            name: 'project_edit_id',
            type: 'int'
        },
        {
            name: 'date',
            type: 'int'
        },
        {
            dateFormat: 'Y-m-d',
            name: 'date_day',
            type: 'date'
        },
        {
            name: 'date_hour'
        },
        {
            name: 'type',
            type: 'int'
        },
        {
            name: 'type_edit',
            type: 'int'
        },
        {
            name: 'original_inout',
            type: 'int'
        },
        {
            name: 'original_inout_edit',
            type: 'int'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'hour_type_id',
            type: 'int'
        },
        {
            name: 'hour_type_edit_id',
            type: 'int'
        },
        {
            name: 'insertion_mode'
        },
        {
            name: 'error',
            type: 'int'
        },
        {
            name: 'locked',
            type: 'boolean'
        }
    ]
});