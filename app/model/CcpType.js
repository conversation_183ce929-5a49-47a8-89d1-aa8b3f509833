/*
 * File: app/model/CcpType.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpType', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'incoming',
            type: 'boolean'
        },
        {
            defaultValue: 0.00,
            name: 'amount',
            type: 'float'
        },
        {
            name: 'category_id',
            type: 'int'
        },
        {
            name: 'category_text',
            type: 'string'
        },
        {
            name: 'locked',
            type: 'boolean'
        },
        {
            name: 'school_year',
            type: 'string'
        },
        {
            name: 'linked_additionals'
        },
        {
            name: 'count_additionals',
            type: 'int'
        },
        {
            name: 'online_payment'
        },
        {
            name: 'invoice_code'
        },
        {
            name: 'easy_code'
        },
        {
            name: 'da_ratei',
            type: 'date'
        },
        {
            name: 'a_ratei',
            type: 'date'
        },
        {
            name: 'include_vat'
        },
        {
            name: 'vat'
        },
        {
            name: 'bollo',
            type: 'boolean'
        },
        {
            name: 'vat_code_id',
            type: 'int'
        },
        {
            convert: function(v, rec) {
                return rec.get('name') + ' ('+rec.get('school_year')+')';
            },
            name: 'name_school_year'
        },
        {
            name: 'payment_mail'
        },
        {
            name: 'section'
        },
        {
            name: 'online_payment_status',
            type: 'int'
        },
        {
            name: 'exclude_corrispettivi',
            type: 'boolean'
        },
        {
            name: 'ccp_credits_type'
        },
        {
            name: 'ae_category'
        },
        {
            name: 'ccp_ae_category_id'
        },
        {
            name: 'include_bollo',
            type: 'boolean'
        },
        {
            convert: function(v, rec) {
                html = '<div style="line-height: 17px;font-size:11px">'+rec.get('name')+'</div>';
                html += '<div style="margin:-7px 0 0 0;font-style:italic;font-size:9px;color:#333">' + rec.get('school_year') + '</div>';

                return html;
            },
            name: 'html'
        },
        {
            name: 'easy_code_contropartita'
        }
    ]
});