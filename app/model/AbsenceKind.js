/*
 * File: app/model/AbsenceKind.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.AbsenceKind', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'code',

    fields: [
        {
            name: 'code',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                return rec.get('code') + " - " + rec.get('description');
            },
            name: 'description_code',
            type: 'string'
        },
        {
            name: 'absence_stack',
            type: 'int'
        },
        {
            name: 'unit',
            type: 'string'
        },
        {
            name: 'calc_festivities',
            type: 'boolean'
        },
        {
            name: 'calc_ferials',
            type: 'boolean'
        },
        {
            name: 'stack_text'
        }
    ]
});