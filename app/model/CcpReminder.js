/*
 * File: app/model/CcpReminder.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpReminder', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'creation',
            type: 'date'
        },
        {
            name: 'reminder_type'
        },
        {
            name: 'mail'
        },
        {
            name: 'message'
        },
        {
            name: 'sent',
            type: 'date'
        },
        {
            name: 'tried',
            type: 'boolean'
        },
        {
            name: 'total_count',
            type: 'int'
        },
        {
            name: 'error_count',
            type: 'int'
        },
        {
            name: 'sent_count',
            type: 'int'
        },
        {
            name: 'confirmed',
            type: 'date'
        },
        {
            convert: function(v, rec) {
                //return '<span style="color:blue;cursor:pointer">Mostra/Nascondi il messaggio del sollecito</span>';
                return '<span style="color:blue;cursor:pointer">Per visionare il messaggio cliccare sul "+"</span>';
            },
            name: 'truncate_message'
        },
        {
            name: 'student_text'
        },
        {
            name: 'accountholder_text'
        }
    ]
});