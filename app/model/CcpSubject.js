/*
 * File: app/model/CcpSubject.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpSubject', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            convert: function(v, rec) {
                return rec.get('type') + '_' + rec.get('db_id') + (rec.get('seat_id') ? '_' + rec.get('seat_id') : '');
            },
            name: 'id',
            type: 'string'
        },
        {
            name: 'type',
            type: 'string'
        },
        {
            name: 'db_id',
            type: 'int'
        },
        {
            name: 'seat_id',
            type: 'int'
        },
        {
            name: 'seat_text',
            type: 'string'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'surname',
            type: 'string'
        },
        {
            name: 'class_id',
            type: 'int'
        },
        {
            name: 'class',
            type: 'int'
        },
        {
            name: 'section',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                var b = rec.get('surname') + ' ' + rec.get('name'),
                    t = rec.get('type') === 'S' ? '(S) - ' : '(P) - ',
                    s = rec.get('type') === 'S' ? rec.get('school_address') + ' - ' : '',
                    c = rec.get('type') === 'S' ? ' (' + rec.get('class') + rec.get('section') + ' ' + rec.get('school_address_code') + ')' : '';

                return t + s + b + c;
            },
            name: 'display',
            type: 'string'
        },
        {
            name: 'fiscal_code'
        },
        {
            name: 'sex'
        },
        {
            name: 'school_address'
        },
        {
            name: 'school_address_code'
        },
        {
            name: 'stato_studente_personalizzato'
        },
        {
            name: 'genitori'
        },
        {
            name: 'subject_school_year'
        }
    ]
});