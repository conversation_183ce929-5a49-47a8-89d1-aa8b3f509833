/*
 * File: app/model/CcpStudentMarket.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpStudentMarket', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id_acquisto'
        },
        {
            name: 'id_marketplace'
        },
        {
            name: 'id_studente'
        },
        {
            name: 'tipo'
        },
        {
            name: 'descrizione'
        },
        {
            name: 'contabilizzato'
        },
        {
            name: 'validita_inizio',
            type: 'date'
        },
        {
            name: 'validita_fine',
            type: 'date'
        },
        {
            name: 'chi_inserisce'
        },
        {
            convert: function(v, rec) {
                var dd = new Date(v*1000);
                return dd;
            },
            name: 'data_inserimento',
            type: 'date'
        },
        {
            name: 'id_tipo_movimento'
        },
        {
            name: 'subject_school_year'
        },
        {
            name: 'caratteristiche'
        }
    ]
});