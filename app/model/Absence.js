/*
 * File: app/model/Absence.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.Absence', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'absence_id',

    fields: [
        {
            name: 'absence_id',
            type: 'int'
        },
        {
            name: 'employee_id',
            type: 'int'
        },
        {
            name: 'start_date'
        },
        {
            name: 'end_date'
        },
        {
            name: 'ab_kind',
            type: 'string'
        },
        {
            name: 'description_code',
            type: 'string'
        },
        {
            name: 'date_of_req'
        },
        {
            name: 'note',
            type: 'string'
        },
        {
            name: 'start_time'
        },
        {
            name: 'end_time'
        },
        {
            name: 'locked',
            type: 'boolean'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'stack',
            type: 'int'
        },
        {
            name: 'denomination',
            type: 'string'
        },
        {
            name: 'unit',
            type: 'string'
        },
        {
            name: 'calc_ferials',
            type: 'boolean'
        },
        {
            name: 'calc_festivities',
            type: 'boolean'
        },
        {
            name: 'decreto',
            type: 'int'
        },
        {
            name: 'multiples',
            type: 'int'
        }
    ]
});