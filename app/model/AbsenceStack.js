/*
 * File: app/model/AbsenceStack.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.AbsenceStack', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'unit',
            type: 'string'
        },
        {
            name: 'denomination',
            type: 'string'
        },
        {
            name: 'recover',
            type: 'boolean'
        },
        {
            name: 'reset_type',
            type: 'int'
        },
        {
            name: 'reset_date',
            type: 'date',
            useNull: true
        },
        {
            name: 'reset_to_stack_id',
            type: 'int',
            useNull: true
        },
        {
            name: 'reset_to_stack_denomination',
            type: 'string'
        },
        {
            name: 'reset_default_quota',
            type: 'float'
        }
    ]
});