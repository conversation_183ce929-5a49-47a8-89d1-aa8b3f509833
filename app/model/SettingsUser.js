/*
 * File: app/model/SettingsUser.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.SettingsUser', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'user_id',

    fields: [
        {
            mapping: 'uid',
            name: 'user_id'
        },
        {
            name: 'user_name'
        },
        {
            name: 'user_type'
        },
        {
            name: 'user_type_str'
        },
        {
            name: 'name'
        },
        {
            name: 'surname'
        },
        {
            name: 'fiscal_code'
        },
        {
            name: 'email'
        },
        {
            name: 'enabled'
        },
        {
            name: 'prive<PERSON>e'
        },
        {
            name: 'modify_protocol'
        },
        {
            name: 'super_user'
        },
        {
            name: 'employee_id',
            type: 'int',
            useNull: true
        }
    ]
});