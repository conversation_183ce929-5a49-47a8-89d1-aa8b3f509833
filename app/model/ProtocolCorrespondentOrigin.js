/*
 * File: app/model/ProtocolCorrespondentOrigin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ProtocolCorrespondentOrigin', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    idProperty: 'correspondent_type_id',

    fields: [
        {
            name: 'correspondent_type_id',
            type: 'int'
        },
        {
            name: 'title',
            type: 'string'
        },
        {
            name: 'note',
            type: 'string'
        },
        {
            name: 'legal_person',
            type: 'boolean'
        },
        {
            name: 'correspondent_type',
            type: 'string'
        },
        {
            name: 'fiscal_code',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                if (!v) {
                    return '';
                }
                return ("00000" + v).slice(-5);
            },
            name: 'zipcode',
            type: 'string'
        },
        {
            name: 'city_id',
            type: 'int'
        },
        {
            name: 'city_name',
            type: 'string'
        },
        {
            name: 'address',
            type: 'string'
        },
        {
            name: 'phone',
            type: 'string'
        },
        {
            name: 'fax',
            type: 'string'
        },
        {
            name: 'mobile',
            type: 'string'
        },
        {
            name: 'email',
            type: 'string'
        },
        {
            name: 'web',
            type: 'string'
        }
    ]
});