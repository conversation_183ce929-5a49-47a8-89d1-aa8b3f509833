/*
 * File: app/model/ArchiveDossier.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ArchiveDossier', {
    extend: 'Ext.data.Model',
    alias: 'model.archivedossier',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'insert_date',
            type: 'date'
        },
        {
            name: 'modified_date',
            type: 'date'
        },
        {
            name: 'first_expiration_date',
            type: 'date',
            useNull: true
        },
        {
            name: 'last_expiration_date',
            type: 'date',
            useNull: true
        },
        {
            name: 'tot_document_files',
            type: 'int'
        },
        {
            name: 'files',
            type: 'int'
        },
        {
            name: 'completed'
        },
        {
            name: 'dossier',
            type: 'boolean'
        },
        {
            name: 'stats_sign'
        },
        {
            name: 'stats_archive'
        }
    ]
});