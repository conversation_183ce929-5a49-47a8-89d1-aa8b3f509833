/*
 * File: app/model/ArchiveDocument.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ArchiveDocument', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'filename',
            type: 'string'
        },
        {
            name: 'short_description',
            type: 'string',
            useNull: true
        },
        {
            name: 'description',
            type: 'string',
            useNull: true
        },
        {
            name: 'upload_date',
            type: 'date'
        },
        {
            name: 'user_id',
            type: 'int'
        },
        {
            name: 'filetype',
            type: 'string'
        },
        {
            name: 'archive_user_id',
            type: 'int'
        },
        {
            name: 'path',
            type: 'string',
            useNull: true
        },
        {
            name: 'metadata',
            useNull: true
        },
        {
            name: 'class_id',
            type: 'int'
        },
        {
            name: 'token',
            type: 'string',
            useNull: true
        },
        {
            convert: function(v, rec) {
                if (rec.get('token') !== null) {
                    return true;
                } else {
                    return false;
                }
            },
            name: 'cloud',
            type: 'boolean'
        },
        {
            name: 'conserved',
            type: 'boolean'
        },
        {
            name: 'class_name',
            type: 'string'
        },
        {
            name: 'class_code',
            type: 'string'
        },
        {
            name: 'action_archive'
        },
        {
            name: 'action_archive_date',
            type: 'date',
            useNull: true
        },
        {
            name: 'action_protocol',
            type: 'boolean'
        },
        {
            name: 'action_protocol_date',
            type: 'date',
            useNull: true
        },
        {
            name: 'action_albo',
            type: 'boolean'
        },
        {
            name: 'action_albo_date',
            type: 'date',
            useNull: true
        },
        {
            name: 'action_trasparenza',
            type: 'boolean'
        },
        {
            name: 'action_trasparenza_date',
            type: 'date',
            useNull: true
        },
        {
            name: 'origin_id',
            type: 'int'
        },
        {
            name: 'origin_name'
        },
        {
            name: 'origin_code'
        },
        {
            name: 'action_sign',
            type: 'boolean'
        },
        {
            dateReadFormat: 'Y-m-d H:i:s+00',
            name: 'action_sign_date',
            type: 'date'
        },
        {
            name: 'forced_protocol'
        },
        {
            name: 'forced_albo'
        },
        {
            name: 'forced_trasparenza'
        },
        {
            name: 'mail'
        },
        {
            name: 'only_pdf',
            type: 'boolean'
        },
        {
            name: 'assign_from_user_name'
        },
        {
            name: 'to_check',
            type: 'boolean'
        },
        {
            name: 'to_check_users',
            type: 'int'
        },
        {
            name: 'checked_users',
            type: 'int'
        },
        {
            name: 'completed'
        },
        {
            name: 'assign_to_user_name'
        },
        {
            name: 'assign_to_office_name'
        },
        {
            name: 'model',
            type: 'int'
        },
        {
            name: 'model_text'
        },
        {
            name: 'expiration_date',
            type: 'date'
        },
        {
            name: 'dossier'
        },
        {
            name: 'action_protocol_number',
            type: 'int'
        }
    ]
});