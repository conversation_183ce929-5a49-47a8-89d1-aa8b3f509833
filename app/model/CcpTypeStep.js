/*
 * File: app/model/CcpTypeStep.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpTypeStep', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'ccp_type',
            type: 'int'
        },
        {
            name: 'expiration_date',
            type: 'date'
        },
        {
            name: 'expiration'
        },
        {
            name: 'value',
            type: 'float'
        },
        {
            name: 'description'
        },
        {
            dateFormat: 'c',
            name: 'da_ratei',
            type: 'date'
        },
        {
            dateFormat: 'c',
            name: 'a_ratei',
            type: 'date'
        },
        {
            name: 'vat',
            type: 'float'
        },
        {
            name: 'gross',
            type: 'float'
        },
        {
            name: 'conto_risconti'
        },
        {
            name: 'conto_crediti'
        },
        {
            name: 'conto_ricavi'
        }
    ]
});