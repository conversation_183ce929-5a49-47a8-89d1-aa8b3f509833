/*
 * File: app/model/CcpDepositSlip.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpDepositSlip', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'number',
            type: 'int'
        },
        {
            name: 'date',
            type: 'date'
        },
        {
            name: 'row_number',
            type: 'int'
        },
        {
            name: 'total',
            type: 'float'
        },
        {
            name: 'unpaid',
            type: 'float'
        },
        {
            name: 'bank_account_name'
        },
        {
            name: 'unpaid_rows'
        },
        {
            name: 'invoices_count'
        },
        {
            name: 'collection_cost_total',
            type: 'float'
        },
        {
            name: 'bolli_total',
            type: 'float'
        },
        {
            name: 'movements_total',
            type: 'float'
        }
    ]
});