/*
 * File: app/model/AlboPublication.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.AlboPublication', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'number',
            type: 'string'
        },
        {
            name: 'title',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'category_id',
            type: 'int'
        },
        {
            name: 'category_text',
            type: 'string'
        },
        {
            name: 'entity_id',
            type: 'int'
        },
        {
            name: 'entity_text',
            type: 'string'
        },
        {
            name: 'area_id',
            type: 'int'
        },
        {
            name: 'area_text',
            type: 'string'
        },
        {
            name: 'start_date',
            type: 'date'
        },
        {
            name: 'expiration_date',
            type: 'date'
        },
        {
            name: 'extended_expiration_date',
            type: 'date'
        },
        {
            name: 'publication_date',
            type: 'date'
        },
        {
            name: 'extension_date',
            type: 'date'
        },
        {
            name: 'cancelation_date',
            type: 'date'
        },
        {
            name: 'expired',
            type: 'boolean'
        },
        {
            name: 'internal',
            type: 'boolean'
        },
        {
            name: 'omissis',
            type: 'boolean'
        },
        {
            name: 'linked_documents'
        },
        {
            name: 'count_documents',
            type: 'int'
        },
        {
            name: 'locked_docs',
            type: 'boolean'
        }
    ]
});