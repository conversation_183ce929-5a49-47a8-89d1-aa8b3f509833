/*
 * File: app/model/CcpPayer.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.CcpPayer', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            convert: function(v, rec) {
                return rec.get('type') + '_' + rec.get('db_id');
            },
            name: 'id',
            type: 'string'
        },
        {
            name: 'type',
            type: 'string'
        },
        {
            name: 'db_id',
            type: 'string'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'surname',
            type: 'string'
        },
        {
            name: 'fiscal_code',
            type: 'string'
        },
        {
            name: 'address',
            type: 'string'
        },
        {
            name: 'city',
            type: 'string'
        },
        {
            name: 'province',
            type: 'string'
        },
        {
            name: 'zip_code',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                return rec.get('surname') + ' ' + rec.get('name');
            },
            name: 'display'
        },
        {
            name: 'pagante'
        }
    ]
});