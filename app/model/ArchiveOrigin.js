/*
 * File: app/model/ArchiveOrigin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ArchiveOrigin', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'code',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            convert: function(v, rec) {
                var code = rec.get('code');

                if (code === 'mc') {
                    return '';
                } else if (code === 'email') {
                    return '';
                } else if (code === 'scan') {
                    return '';
                } else if (code === 'fax') {
                    return '';
                } else if (code === 'man') {
                    return '';
                } else if (code === 'other') {
                    return '';
                } else {
                    return '';
                }
            },
            name: 'icon',
            type: 'string'
        }
    ]
});