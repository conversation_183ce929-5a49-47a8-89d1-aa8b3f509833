/*
 * File: app/model/PersonnelHourTypeLinked.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.PersonnelHourTypeLinked', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'hour_type_id',
            type: 'int'
        },
        {
            name: 'project_id',
            type: 'int'
        },
        {
            name: 'name',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'price',
            type: 'float'
        },
        {
            name: 'inpdap_perc',
            type: 'float'
        },
        {
            name: 'inps_perc',
            type: 'float'
        },
        {
            name: 'irap_perc',
            type: 'float'
        },
        {
            name: 'duration',
            type: 'int'
        }
    ]
});