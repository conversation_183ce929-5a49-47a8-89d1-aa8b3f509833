/*
 * File: app/model/ProtocolType.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.model.ProtocolType', {
    extend: 'Ext.data.Model',

    requires: [
        'Ext.data.Field'
    ],

    fields: [
        {
            name: 'id',
            type: 'int'
        },
        {
            name: 'code',
            type: 'string'
        },
        {
            name: 'description',
            type: 'string'
        },
        {
            name: 'parent_type_id',
            type: 'int',
            useNull: true
        },
        {
            convert: function(v, rec) {
                return rec.get('code') + ' - ' + rec.get('description');
            },
            name: 'denomination',
            type: 'string'
        },
        {
            name: 'full_denomination',
            type: 'string'
        },
        {
            defaultValue: false,
            name: 'locked',
            type: 'boolean'
        },
        {
            name: 'id_tree'
        },
        {
            name: 'last_level',
            type: 'boolean'
        }
    ]
});