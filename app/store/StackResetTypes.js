/*
 * File: app/store/StackResetTypes.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.StackResetTypes', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Array',
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            autoLoad: true,
            storeId: 'StackResetTypes',
            data: [
                {
                    id: 0,
                    text: 'Manuale'
                },
                {
                    id: 1,
                    text: 'Reset - Annuale'
                },
                {
                    id: 2,
                    text: 'Reset - Mensile'
                }/*,{
                    id: 3,
                    text: 'Aggiunta - Annuale'
                },
                {
                    id: 4,
                    text: 'Aggiunta - Mensile'
                },
                {
                    id: 5,
                    text: 'Sottrazione - Annuale'
                },
                {
                    id: 6,
                    text: 'Sottrazione - Mensile'
                }*/
            ],
            proxy: {
                type: 'ajax',
                reader: {
                    type: 'array'
                }
            },
            fields: [
                {
                    name: 'id',
                    type: 'int'
                },
                {
                    name: 'text',
                    type: 'string'
                }
            ]
        }, cfg)]);
    }
});