/*
 * File: app/store/ArchiveServices.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ArchiveServices', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Array',
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'ArchiveServices',
            data: [
                {
                    name: 'DELETE_DOCUMENT',
                    description: 'Cancellazione documento'
                },
                {
                    name: 'DELETE_USER',
                    description: 'Cancellazione di un utente'
                },
                {
                    name: 'DOCUMENT_TYPE_CONFIGURATION_UPDATE',
                    description: 'Specifica delle modalità di spedizione di un document-type'
                },
                {
                    name: 'DOCUMENT_QUERY',
                    description: 'Ricerca di un documento'
                },
                {
                    name: 'DOWNLOAD_DOCUMENT',
                    description: 'Download di un documento'
                },
                {
                    name: 'DOWNLOAD_LOT_ISO',
                    description: 'Download di un lotto ISO'
                },
                {
                    name: 'DOWNLOAD_LOT_XML',
                    description: 'Download di un lotto XML'
                },
                {
                    name: 'GET_DOCUMENT_SIGNATURE',
                    description: 'Recupero della firma di un documento'
                },
                {
                    name: 'GET_LOT_SIGNATURE',
                    description: 'Recupero della firma di un lotto'
                },
                {
                    name: 'LOT_QUERY',
                    description: 'Ricerca di un lotto'
                },
                {
                    name: 'OVERWRITE_SINGLE_DOCUMENT',
                    description: 'Sovrascrittura di un singolo documento'
                },
                {
                    name: 'OVERWRITE_SPOOL',
                    description: 'Sovrascrittura di un file di spool'
                },
                {
                    name: 'PUBLIC_DOWNLOAD_DOCUMENT',
                    description: 'Download di un documento senza autenticazione'
                },
                {
                    name: 'RESEND_DOCUMENT',
                    description: 'Nuova spedizione di un documento'
                },
                {
                    name: 'SETUP_USER_CAPABILITIES',
                    description: 'Specifica dei permessi di un utente'
                },
                {
                    name: 'UPLOAD_SINGLE_DOCUMENT',
                    description: 'Upload di un documento'
                },
                {
                    name: 'UPLOAD_SPOOL',
                    description: 'Upload di un file di spool'
                },
                {
                    name: 'USER_AUTHENTICATION',
                    description: 'Autenticazione utente'
                },
                {
                    name: 'USER_INFORMATION',
                    description: 'Informazioni su utente'
                },
                {
                    name: 'USERS_LIST',
                    description: 'Utenti abilitati'
                }
            ],
            proxy: {
                type: 'ajax',
                reader: {
                    type: 'array'
                }
            },
            fields: [
                {
                    name: 'name',
                    type: 'string'
                },
                {
                    name: 'description',
                    type: 'string'
                }
            ]
        }, cfg)]);
    }
});