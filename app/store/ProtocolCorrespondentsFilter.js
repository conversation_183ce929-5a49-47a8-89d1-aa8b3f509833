/*
 * File: app/store/ProtocolCorrespondentsFilter.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ProtocolCorrespondentsFilter', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.ProtocolCorrespondent',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.ProtocolCorrespondent',
            remoteFilter: true,
            storeId: 'ProtocolCorrespondentsFilter',
            proxy: {
                type: 'rest',
                extraParams: {
                    is_filter: 'on'
                },
                url: '/mc2-api/protocol/correspondent',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            }
        }, cfg)]);
    }
});