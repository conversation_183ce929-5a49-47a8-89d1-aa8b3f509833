/*
 * File: app/store/ArchiveDashboard.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ArchiveDashboard', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.ArchiveDocument',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.ArchiveDocument',
            storeId: 'ArchiveDashboard',
            proxy: {
                type: 'rest',
                url: '/mc2-api/archive/document',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            listeners: {
                beforeload: {
                    fn: me.onJsonstoreBeforeLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreBeforeLoad: function(store, operation, eOpts) {
        store.getProxy().extraParams = {
            'limit':0,
            'start': 0
        };
    }

});