/*
 * File: app/store/Raccoltes.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.Raccoltes', {
    extend: 'Ext.data.Store',
    alias: 'store.raccoltes',

    requires: [
        'mc2ui.model.Raccolte',
        'Ext.data.proxy.Memory'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.Raccolte',
            storeId: 'Raccoltes',
            data: [
                {
                    id: 1,
                    parent: 0,
                    elements: 328,
                    name: '<PERSON><PERSON>',
                    type: 'R'
                },
                {
                    id: 2,
                    parent: 0,
                    elements: 352,
                    name: 'Richieste ferie e permessi',
                    type: 'R'
                },
                {
                    id: 3,
                    parent: 0,
                    elements: 581,
                    name: 'Documenti interni',
                    type: 'R'
                },
                {
                    id: 4,
                    parent: 0,
                    elements: 862,
                    name: 'Contratti',
                    type: 'R'
                },
                {
                    id: 5,
                    parent: 0,
                    elements: 484,
                    name: 'Altro',
                    type: 'R'
                },
                {
                    id: 6,
                    parent: 1,
                    elements: 299,
                    name: '2017',
                    type: 'R'
                },
                {
                    id: 26,
                    parent: 1,
                    elements: 299,
                    name: '2016',
                    type: 'R'
                },
                {
                    id: 36,
                    parent: 1,
                    elements: 299,
                    name: '2015',
                    type: 'R'
                },
                {
                    id: 46,
                    parent: 6,
                    elements: 299,
                    name: '1A',
                    type: 'F'
                },
                {
                    id: 56,
                    parent: 6,
                    elements: 299,
                    name: '1B',
                    type: 'F'
                },
                {
                    id: 66,
                    parent: 6,
                    elements: 299,
                    name: 'Mario bianchi (1A)',
                    type: 'C'
                },
                {
                    id: 7,
                    parent: 1,
                    elements: 448,
                    name: 'temporibus',
                    type: 'R'
                },
                {
                    id: 8,
                    parent: 2,
                    elements: 523,
                    name: 'suscipit',
                    type: 'R'
                },
                {
                    id: 9,
                    parent: 2,
                    elements: 975,
                    name: 'beatae',
                    type: 'R'
                },
                {
                    id: 10,
                    parent: 2,
                    elements: 1,
                    name: 'tenetur',
                    type: 'R'
                },
                {
                    id: 11,
                    parent: 4,
                    elements: 471,
                    name: 'nesciunt',
                    type: 'R'
                },
                {
                    id: 12,
                    parent: 5,
                    elements: 862,
                    name: 'esse',
                    type: 'R'
                },
                {
                    id: 13,
                    parent: 12,
                    elements: 44,
                    name: 'tenetur',
                    type: 'R'
                }
            ],
            proxy: {
                type: 'memory'
            }
        }, cfg)]);
    }
});