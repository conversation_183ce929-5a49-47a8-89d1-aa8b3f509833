/*
 * File: app/store/CcpInvoiceMovements.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpInvoiceMovements', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.CcpMovement',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.CcpMovement',
            storeId: 'CcpInvoiceMovements',
            proxy: {
                type: 'ajax',
                url: '/mc2-api/ccp/movement',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            listeners: {
                beforeload: {
                    fn: me.onJsonstoreBeforeLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreBeforeLoad: function(store, operation, eOpts) {
        var accountholder = Ext.getCmp('CcpInvoiceAccountHolderSerach').getValue(),
            start = Ext.getCmp('CcpInvoiceDateFrom').getValue(),
            end = Ext.getCmp('CcpInvoiceDateTo').getValue(),
            payment_method = Ext.getCmp('CcpInvoicePaymentMethod').getValue(),
            school_address = Ext.getCmp('CcpInvoiceSchoolAddress').getValue(),
            class_id = Ext.getCmp('CcpInvoiceClass').getValue(),
            subject_school_year=Ext.getCmp('CcpSchoolYearEmitInvoiceCmb').getValue(),
            type_ids=Ext.getCmp('CcpInvoiceTypeId').getValue(),
            paid = Ext.getCmp('CcpInvoiceMovFilterPaid').getValue()===true ? 1:0,
            not_paid = Ext.getCmp('CcpInvoiceMovFilterNotPaid').getValue()===true ? 1:0,
            student_state = Ext.getCmp('CcpInvoiceMovFilterStudentState').getValue();

        params = {
            debit: 0,
            expiration_date_start: start,
            expiration_date_end: end,
            'type_ids[]': type_ids,
            invoiced: 0,
            limit: 0,
            short_response: 1,
            subject_school_year:subject_school_year,
            not_payed: not_paid,
            payed: paid,
        };

        if(accountholder) {
            params.accountholder_or_subject_data =  accountholder;
        }


        if(student_state) {
            params.student_state =  student_state;
        }

        if(payment_method) {
            params.payment_method =  payment_method;
        }

        if(school_address) {
            params['address_id[]'] =  school_address;
        }

        if(class_id) {
            params['class_id[]'] =  class_id;
        }

        /*if(!mc2ui.app.settings.invoiceEnabled) {
            params.payed = 0;
            params.not_payed = 1;
        }*/

        if(Ext.getCmp('CcpInvoiceMovementNcBtn').pressed === true){
            params.credit = 0;
            params.debit = 1;
        }


        store.getProxy().timeout = 1000000;
        store.getProxy().extraParams = params;
    }

});