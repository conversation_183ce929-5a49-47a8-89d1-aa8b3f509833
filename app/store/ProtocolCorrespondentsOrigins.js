/*
 * File: app/store/ProtocolCorrespondentsOrigins.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ProtocolCorrespondentsOrigins', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'ProtocolCorrespondentsOrigins',
            data: [
                {
                    id: 'M',
                    text: 'Generici'
                },
                {
                    id: 'P',
                    text: 'Personale'
                },
                {
                    id: 'S',
                    text: 'Studenti'
                },
                {
                    id: 'F',
                    text: 'Fornitori'
                },
                {
                    id: 'I',
                    text: 'Istituti'
                },
                {
                    id: 'T',
                    text: 'Tutori'
                }
            ],
            fields: [
                {
                    name: 'id',
                    type: 'string'
                },
                {
                    name: 'text',
                    type: 'string'
                }
            ]
        }, cfg)]);
    }
});