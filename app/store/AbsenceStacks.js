/*
 * File: app/store/AbsenceStacks.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.AbsenceStacks', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.AbsenceStack',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Json',
        'Ext.data.writer.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.AbsenceStack',
            storeId: 'AbsenceStacks',
            groupField: 'unit',
            proxy: {
                type: 'ajax',
                api: {
                    read: '/mc2/applications/employees/absence_stacks/read.php',
                    create: '/mc2/applications/employees/absence_stacks/write.php',
                    update: '/mc2/applications/employees/absence_stacks/write.php',
                    destroy: '/mc2/applications/employees/absence_stacks/destroy.php'
                },
                reader: {
                    type: 'json',
                    root: 'results'
                },
                writer: {
                    type: 'json'
                }
            }
        }, cfg)]);
    },

    getGroupString: function(instance) {
        if (instance.get('unit') == 'h'){
            return 'ORE';
        } else {
            return 'GIORNI';
        }
    }

});