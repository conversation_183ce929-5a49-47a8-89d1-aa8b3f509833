/*
 * File: app/store/CcpPaymentMethodsFilter.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpPaymentMethodsFilter', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.CcpPaymentMethod',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.CcpPaymentMethod',
            storeId: 'CcpPaymentMethodsFilter',
            proxy: {
                type: 'rest',
                extraParams: {
                    filter: 'on'
                },
                url: '/mc2-api/ccp/payment_method',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            }
        }, cfg)]);
    }
});