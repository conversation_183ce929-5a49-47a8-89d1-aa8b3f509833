/*
 * File: app/store/ArchiveSentMails.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ArchiveSentMails', {
    extend: 'mc2ui.store.ArchiveMails',

    onJsonstoreBeforeLoad: function(store, operation, eOpts) {
        var query = Ext.getCmp('ArchiveMailSentSearchTxt').getValue(),
            archived = Ext.getCmp('ArchiveMailArchivedBtn').pressed;

        store.getProxy().extraParams = {
            deleted: archived ? 1 : 0,
            active: 1,
            query:query,
            out: 0,
            is_sent: 1
        };
    }

});