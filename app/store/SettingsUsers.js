/*
 * File: app/store/SettingsUsers.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.SettingsUsers', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.SettingsUser',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.SettingsUser',
            storeId: 'SettingsUsers',
            proxy: {
                type: 'ajax',
                api: {
                    read: '/mc2/applications/core/users/read.php',
                    destroy: '/mc2/applications/core/users/destroy.php'
                },
                reader: {
                    type: 'json',
                    idProperty: 'uid',
                    root: 'results'
                }
            }
        }, cfg)]);
    }
});