/*
 * File: app/store/CcpAddMovementStudents.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpAddMovementStudents', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'CcpAddMovementStudents',
            fields: [
                {
                    name: 'subject_id'
                },
                {
                    name: 'subject_type'
                },
                {
                    name: 'subject_data'
                },
                {
                    name: 'subject_seat'
                },
                {
                    name: 'subject_class'
                },
                {
                    name: 'school_address'
                },
                {
                    name: 'school_address_code'
                }
            ],
            listeners: {
                datachanged: {
                    fn: me.onArraystoreDataChangeD,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onArraystoreDataChangeD: function(store, eOpts) {
        if (store.getRange().length>0) {
            Ext.getCmp('CcpMovementSubjectOther').setDisabled(true);
            Ext.getCmp('CcpMovementSubjectOther').setValue();
            Ext.getCmp('CcpMovementCopyStudents').setDisabled(true);
            mc2ui.app.treePropagateChange(Ext.getCmp('CcpMovementCopyStudents').getRootNode());
            Ext.getCmp('CcpEditMovementSubjectYearCmb').disable();
        } else {
            Ext.getCmp('CcpEditMovementSubjectYearCmb').enable();
            Ext.getCmp('CcpMovementSubject').setValue();
            //Ext.getCmp('CcpMovementSubject').select();

            //Ext.getCmp('CcpMovementSubjectClear').setDisabled(true);

            if (Ext.getCmp('CcpMovementSubjectId').getValue() > 0) {
                Ext.getCmp('CcpMovementSubjectData').setValue();
            }
            Ext.getCmp('CcpMovementSubjectId').setValue();
            Ext.getCmp('CcpMovementSubjectType').setValue('O');
            Ext.getCmp('CcpMovementSubjectSeat').setValue();
            Ext.getCmp('CcpMovementSubjectClass').setValue();

            Ext.getCmp('CcpMovementSubject').setDisabled(false);
            Ext.getCmp('CcpMovementSubjectOther').setDisabled(false);
            Ext.getCmp('CcpMovementCopyStudents').setDisabled(false);
        }
    }

});