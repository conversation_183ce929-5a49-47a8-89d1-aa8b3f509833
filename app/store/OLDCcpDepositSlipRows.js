/*
 * File: app/store/OLDCcpDepositSlipRows.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.OLDCcpDepositSlipRows', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'OLDCcpDepositSlipRows',
            fields: [
                {
                    name: 'id',
                    type: 'int'
                },
                {
                    name: 'ccp_invoice',
                    type: 'int'
                },
                {
                    name: 'ccp_deposit_slip',
                    type: 'int'
                },
                {
                    name: 'unpaid_date',
                    type: 'date'
                },
                {
                    name: 'unpaid_note'
                },
                {
                    name: 'row_number',
                    type: 'int'
                },
                {
                    convert: function(v, rec) {

                        var invoiceEnabled = mc2ui.app.settings.invoiceEnabled,
                            invoices = rec.raw.invoices,
                            invoiceDescription = '',
                            ah, ahText='',
                            st, stText='';

                        Ext.each(invoices, function (invoice){
                            if(invoiceEnabled === true) {
                                invoiceDescription += 'Fattura N. ' + invoice.number + '. ';
                            }

                            if(!ahText) {
                                ah = Ext.decode(invoice.accountholder)[0];
                                ahText += ah.surname + ' ' + ah.name;

                                st = Ext.decode(invoice.rows)[0];
                                stText += st.subject_data;
                            }

                        });

                        invoiceDescription += stText + '/' + ahText;

                        return invoiceDescription;

                    },
                    name: 'invoice_description'
                },
                {
                    convert: function(v, rec) {

                        var invoices = rec.raw.invoices,
                            total = 0;

                        Ext.each(invoices, function (invoice){
                            total += invoice.total;
                        });

                        return total;

                    },
                    name: 'total',
                    type: 'int'
                }
            ]
        }, cfg)]);
    }
});