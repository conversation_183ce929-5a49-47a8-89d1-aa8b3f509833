/*
 * File: app/store/ProtocolDocumentsForm.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ProtocolDocumentsForm', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.ArchiveDocument',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json',
        'Ext.util.Grouper'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.ArchiveDocument',
            remoteFilter: true,
            remoteSort: true,
            storeId: 'ProtocolDocumentsForm',
            pageSize: 40,
            proxy: {
                type: 'rest',
                extraParams: {
                    action_protocol: 'on0'
                },
                url: '/mc2-api/archive/document',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            groupers: {
                property: 'class_name'
            },
            sorters: {
                property: 'filename'
            },
            listeners: {
                load: {
                    fn: me.onJsonstoreLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreLoad: function(store, records, successful, eOpts) {
        var documents = Ext.getStore('ProtocolLinkedDocumentsForm').getRange();

        Ext.getCmp('ProtocolProtocolNewLinkedDocumentsPickerGrid').getSelectionModel().select(documents, false, true);
    }

});