/*
 * File: app/store/CcpPrintWhat.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpPrintWhat', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'CcpPrintWhat',
            data: [
                {
                    id: 'credits',
                    text: 'Crediti',
                    family: false,
                    
                },
                {
                    id: 'family_balance',
                    text: 'Saldo famiglie',
                    family: true,
                    
                },
                {
                    id: 'deposit_list',
                    text: 'Lista entrate',
                    family: true,
                    
                },
                {
                    id: 'debtor_list',
                    text: 'Lista debitori',
                    family: true,
                    
                },
                {
                    id: 'estimate_list',
                    text: 'Stampa situazione rette mense',
                    family: true,
                    
                },
                {
                    id: 'school_year_registration',
                    text: 'Stampa inizio anno',
                    family: true,
                    
                },
                {
                    id: 'school_year_registration_n_i',
                    text: 'Stampa inizio anno N.I.',
                    family: true,
                    
                },
                {
                    id: 'dichiarazione',
                    text: 'Dichiarazione',
                    mcType: 'MC2.RETTE_E_CONTI_CORRENTI.DICHIARAZIONI',
                    mc2Class: 'CCP\\MCTemplate\\Declaration',
                    family: false,
                    
                },
                {
                    id: 'declaration',
                    text: 'Dichiarazione',
                    mc2Class: 'Declaration',
                    family: true,
                    
                }
            ],
            fields: [
                {
                    name: 'id'
                },
                {
                    name: 'text'
                },
                {
                    name: 'family',
                    type: 'boolean'
                },
                {
                    name: 'mcType'
                },
                {
                    name: 'mc2Class'
                }
            ]
        }, cfg)]);
    }
});