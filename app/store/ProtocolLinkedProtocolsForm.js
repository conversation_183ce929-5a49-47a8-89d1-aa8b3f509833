/*
 * File: app/store/ProtocolLinkedProtocolsForm.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ProtocolLinkedProtocolsForm', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.ProtocolProtocol',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json',
        'Ext.util.Sorter'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.ProtocolProtocol',
            storeId: 'ProtocolLinkedProtocolsForm',
            proxy: {
                type: 'rest',
                extraParams: {
                    type: 'P'
                },
                url: '/mc2-api/protocol/linked',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            sorters: {
                direction: 'DESC',
                property: 'protocol_number'
            }
        }, cfg)]);
    }
});