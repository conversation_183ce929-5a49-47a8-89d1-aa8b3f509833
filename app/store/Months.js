/*
 * File: app/store/Months.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.Months', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Array',
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'Months',
            data: [
                {
                    number: 1,
                    text: 'Gennaio'
                },
                {
                    number: 2,
                    text: '<PERSON><PERSON><PERSON>'
                },
                {
                    number: 3,
                    text: '<PERSON><PERSON>'
                },
                {
                    number: 4,
                    text: 'April<PERSON>'
                },
                {
                    number: 5,
                    text: 'Ma<PERSON>'
                },
                {
                    number: 6,
                    text: '<PERSON><PERSON><PERSON>'
                },
                {
                    number: 7,
                    text: '<PERSON>gli<PERSON>'
                },
                {
                    number: 8,
                    text: '<PERSON><PERSON><PERSON>'
                },
                {
                    number: 9,
                    text: 'Settembre'
                },
                {
                    number: 10,
                    text: 'Ottobre'
                },
                {
                    number: 11,
                    text: 'Novembre'
                },
                {
                    number: 12,
                    text: 'Dicembre'
                }
            ],
            proxy: {
                type: 'ajax',
                reader: {
                    type: 'array'
                }
            },
            fields: [
                {
                    name: 'number'
                },
                {
                    name: 'text'
                }
            ]
        }, cfg)]);
    }
});