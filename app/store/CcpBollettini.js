/*
 * File: app/store/CcpBollettini.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpBollettini', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.CcpBollettino',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Array'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.CcpBollettino',
            storeId: 'CcpBollettini',
            data: [
                {
                    name: 'TD123_BIS',
                    description: 'TD 123 BIS'
                },
                {
                    name: 'TD123_BIS_FULL',
                    description: 'TD 123 BIS con sfondo'
                },
                {
                    name: 'TD123_TER',
                    description: 'TD 123 TER'
                },
                {
                    name: 'TD123_TER_FULL',
                    description: 'TD 123 TER con sfondo'
                },
                {
                    name: 'TD451_1016',
                    description: 'TD 451 Ag. Entrate'
                },
                {
                    name: 'Pvr',
                    description: 'Polizza Pvr'
                },
                {
                    name: 'QrBill',
                    description: 'Fattura QR'
                }
            ],
            proxy: {
                type: 'ajax',
                reader: {
                    type: 'array'
                }
            }
        }, cfg)]);
    }
});