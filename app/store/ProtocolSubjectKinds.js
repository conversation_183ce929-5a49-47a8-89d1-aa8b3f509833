/*
 * File: app/store/ProtocolSubjectKinds.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ProtocolSubjectKinds', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.ProtocolSubjectKind',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.ProtocolSubjectKind',
            storeId: 'ProtocolSubjectKinds',
            proxy: {
                type: 'rest',
                url: '/mc2-api/protocol/subjectkind',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            listeners: {
                load: {
                    fn: me.onJsonstoreLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreLoad: function(store, records, successful, eOpts) {
        Ext.getStore('ProtocolSubjectKindsFilter').load();
    }

});