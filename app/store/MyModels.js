/*
 * File: app/store/MyModels.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.MyModels', {
    extend: 'Ext.data.Store',
    alias: 'store.mymodels',

    requires: [
        'mc2ui.model.MyModel',
        'Ext.data.proxy.Memory'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.MyModel',
            storeId: 'MyModels',
            data: [
                {
                    id: 611,
                    myField: '<PERSON>'
                },
                {
                    id: 985,
                    myField: '<PERSON>'
                },
                {
                    id: 351,
                    myField: '<PERSON>'
                },
                {
                    id: 34,
                    my<PERSON><PERSON>: '<PERSON>'
                },
                {
                    id: 517,
                    my<PERSON>ield: '<PERSON>'
                },
                {
                    id: 981,
                    my<PERSON><PERSON>: '<PERSON>'
                },
                {
                    id: 470,
                    myField: '<PERSON>'
                },
                {
                    id: 950,
                    my<PERSON>ield: '<PERSON> <PERSON>'
                },
                {
                    id: 683,
                    myField: '<PERSON> <PERSON>'
                },
                {
                    id: 220,
                    myField: 'Beulah <PERSON>'
                },
                {
                    id: 749,
                    myField: '<PERSON> <PERSON>'
                },
                {
                    id: 480,
                    myField: '<PERSON> <PERSON>'
                },
                {
                    id: 691,
                    myField: '<PERSON> <PERSON>'
                },
                {
                    id: 923,
                    myField: 'Harvey Miller'
                },
                {
                    id: 979,
                    myField: 'Tom Turner'
                },
                {
                    id: 307,
                    myField: 'Sallie Walker'
                },
                {
                    id: 474,
                    myField: 'Peter Thompson'
                },
                {
                    id: 234,
                    myField: 'Maude Sanchez'
                },
                {
                    id: 188,
                    myField: 'Ed Parker'
                },
                {
                    id: 273,
                    myField: 'Susie Torres'
                },
                {
                    id: 13,
                    myField: 'Floyd Hernandez'
                },
                {
                    id: 611,
                    myField: 'Russell Cooper'
                },
                {
                    id: 901,
                    myField: 'Oliver Perez'
                },
                {
                    id: 963,
                    myField: 'Leonard King'
                },
                {
                    id: 940,
                    myField: 'Blanche Rivera'
                },
                {
                    id: 997,
                    myField: 'Patrick Brooks'
                },
                {
                    id: 363,
                    myField: 'Bernard Morris'
                },
                {
                    id: 948,
                    myField: 'Ruby Martinez'
                },
                {
                    id: 6,
                    myField: 'Kathryn Baker'
                },
                {
                    id: 986,
                    myField: 'Allen Parker'
                }
            ],
            proxy: {
                type: 'memory'
            }
        }, cfg)]);
    }
});