/*
 * File: app/store/CalendarHoliday.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CalendarHoliday', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Json',
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'CalendarHoliday',
            proxy: {
                type: 'ajax',
                api: {
                    read: '/mc2/applications/employees/common/holiday/read.php',
                    update: '/mc2/applications/employees/common/holiday/write.php'
                },
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            fields: [
                {
                    name: 'day'
                },
                {
                    name: 'month'
                },
                {
                    name: 'holiday'
                },
                {
                    name: 'css_class'
                }
            ]
        }, cfg)]);
    }
});