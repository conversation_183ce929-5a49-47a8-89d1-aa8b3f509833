/*
 * File: app/store/CcpAeCategories.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpAeCategories', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'CcpAeCategories',
            fields: [
                {
                    name: 'id',
                    type: 'int'
                },
                {
                    name: 'description'
                },
                {
                    name: 'row',
                    type: 'int'
                },
                {
                    name: 'incoming',
                    type: 'boolean'
                }
            ],
            proxy: {
                type: 'ajax',
                url: '/mc2-api/ccp/ae_categories',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            }
        }, cfg)]);
    }
});