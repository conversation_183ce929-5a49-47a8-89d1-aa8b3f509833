/*
 * File: app/store/MC2Tables.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.MC2Tables', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.MC2Table',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Array'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.MC2Table',
            storeId: 'MC2Tables',
            data: [
                {
                    name: '<PERSON>e',
                    table: 'employee',
                    field: 'employee_id',
                    field_type: false
                },
                {
                    name: 'Assenze',
                    table: 'absences',
                    field: 'absence_id',
                    field_type: false
                },
                {
                    name: '<PERSON><PERSON><PERSON>',
                    table: 'absence_kind',
                    field: 'code',
                    field_type: true
                },
                {
                    name: 'Monteore',
                    table: 'absence_stack',
                    field: 'id',
                    field_type: false
                },
                {
                    name: 'Orario',
                    table: 'personnel_timetable',
                    field: 'personnel_timetable_id',
                    field_type: false
                },
                {
                    name: 'Timbrature',
                    table: 'personnel_presences',
                    field: 'personnel_presence_id',
                    field_type: false
                },
                {
                    name: 'Progetti',
                    table: 'personnel_projects',
                    field: 'personnel_projects_id',
                    field_type: false
                },
                {
                    name: 'Chiusure Mese',
                    table: 'extraordinary_stored',
                    field: 'extraordinary_stored_id',
                    field_type: false
                },
                {
                    name: 'Chiusure Giorno',
                    table: 'storage_personnel_presences',
                    field: 'storage_personnel_presences_id',
                    field_type: false
                },
                {
                    name: 'Chiusure Monteore',
                    table: 'storage_personnel_stack',
                    field: 'id',
                    field_type: false
                },
                {
                    name: 'Calendario Festività',
                    table: 'calender_holidays',
                    field: 'id',
                    field_type: false
                },
                {
                    name: 'Calendario Non Lavorativi',
                    table: 'calender_weekends',
                    field: 'week_day',
                    field_type: false
                },
                {
                    name: 'Movimenti',
                    table: 'tasse',
                    field: 'id_tasse',
                    field_type: false
                },
                {
                    name: 'Tipi Tassa',
                    table: 'tipi_tasse',
                    field: 'id_tipo_tassa',
                    field_type: false
                },
                {
                    name: 'Residui',
                    table: 'tax_residuals',
                    field: 'id_residual',
                    field_type: false
                },
                {
                    name: 'Utenti',
                    table: 'users',
                    field: 'uid',
                    field_type: false
                },
                {
                    name: 'Gruppi',
                    table: 'groups',
                    field: 'gid',
                    field_type: false
                },
                {
                    name: 'Permessi',
                    table: 'auth_permission',
                    field: 'id',
                    field_type: false
                },
                {
                    name: 'Istituti',
                    table: 'institute',
                    field: 'institute_id',
                    field_type: false
                },
                {
                    name: 'Coda di stampa',
                    table: 'print_ready',
                    field: 'id',
                    field_type: false
                },
                {
                    name: 'Parametri',
                    table: 'parameter',
                    field: 'name',
                    field_type: true
                }
            ],
            proxy: {
                type: 'ajax',
                reader: {
                    type: 'array'
                }
            }
        }, cfg)]);
    }
});