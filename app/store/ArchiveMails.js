/*
 * File: app/store/ArchiveMails.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ArchiveMails', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.ArchiveMail',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.ArchiveMail',
            storeId: 'ArchiveMails',
            pageSize: 50,
            proxy: {
                type: 'rest',
                url: '/mc2-api/archive/mail/mail',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            listeners: {
                beforeload: {
                    fn: me.onJsonstoreBeforeLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreBeforeLoad: function(store, operation, eOpts) {
        var query = Ext.getCmp('ArchiveMailSearchTxt').getValue(),
            archived = Ext.getCmp('ArchiveMailArchivedBtn').pressed;

        store.getProxy().extraParams = {
            deleted: archived ? 1 : 0,
            active: 1,
            query:query,
            out: 0
        };
    }

});