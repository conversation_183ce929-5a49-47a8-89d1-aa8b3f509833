/*
 * File: app/store/CcpPrintStudentCols.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpPrintStudentCols', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'CcpPrintStudentCols',
            data: [
                {
                    id: 'student_name',
                    text: 'Nome studente'
                },
                {
                    id: 'student_surname',
                    text: 'Cognome studente'
                },
                {
                    id: 'student_class',
                    text: 'Classe studente'
                },
                {
                    id: 'student_residence',
                    text: 'Residenza studente'
                },
                {
                    id: 'payer_name',
                    text: 'Nome pagante'
                },
                {
                    id: 'payer_surname',
                    text: 'Cognome pagante'
                },
                {
                    id: 'payer_iban',
                    text: 'IBAN pagante'
                },
                {
                    id: 'payer_residence',
                    text: 'Residenza pagante'
                }
            ],
            fields: [
                {
                    name: 'id'
                },
                {
                    name: 'text'
                }
            ]
        }, cfg)]);
    }
});