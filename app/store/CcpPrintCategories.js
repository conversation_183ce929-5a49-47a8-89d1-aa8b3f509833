/*
 * File: app/store/CcpPrintCategories.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpPrintCategories', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'CcpPrintCategories',
            data: [
                {
                    id: 'group_by_payer',
                    text: 'Raggruppato per pagante/famiglia'
                },
                {
                    id: 'group_by_student',
                    text: 'Raggruppato per studente'
                }/*,{
                    id: 'group_by_class',
                    text: 'Raggruppato per classe (NON DISPONIBILE)'
                },
                {
                    id: 'group_by_class_student',
                    text: 'Raggruppato per classe/studente (NON DISPONIBILE)'
                }*/,{
                    id: 'group_by_none',
                    text: 'Lista generica'
                }
            ],
            fields: [
                {
                    name: 'id'
                },
                {
                    name: 'text'
                }
            ]
        }, cfg)]);
    }
});