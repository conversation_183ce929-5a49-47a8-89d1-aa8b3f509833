/*
 * File: app/store/ArchiveDocumentsArchived.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.ArchiveDocumentsArchived', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.ArchiveDocument',
        'Ext.data.proxy.Rest',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.ArchiveDocument',
            remoteSort: true,
            storeId: 'ArchiveDocumentsArchived',
            pageSize: 40,
            proxy: {
                type: 'rest',
                url: '/mc2-api/archive/document',
                reader: {
                    type: 'json',
                    root: 'results'
                }
            },
            listeners: {
                beforeload: {
                    fn: me.onJsonstoreBeforeLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreBeforeLoad: function(store, operation, eOpts) {
        var params = {},
            val;

        val = Ext.getCmp('ArchiveFilterClass').getValue();
        if(val > 0) {
            params.class_id = val;
        }

        val = Ext.getCmp('ArchiveFilterMetadataDateStart').getValue();
        if(val) {
            params.date_start = val;
        }

        val = Ext.getCmp('ArchiveFilterMetadataDateEnd').getValue();
        if(val) {
            params.date_end = val;
        }

        val = Ext.getCmp('ArchiveArchivedSearchTxt').getValue();
        if(val) {
            params.query = val;
        }

        val = Ext.getCmp('ArchiveFilterCompleted').getValue();
        switch(val) {
            case 0:
                params.completed = 0;
                break;
            case 1:
                params.completed = 1;
                break;
            default:
                break;
        }

        val = Ext.getCmp('ArchiveFilterChecked').getValue();
        switch(val) {
            case 0:
                params.checked = 0;
                break;
            case 1:
                params.checked = 1;
                break;
            default:
                break;
        }

        val = Ext.getCmp('ArchiveFilterAssignToUser').getValue();
        if(val){
            params.assign_to_user = val;
        }

        val = Ext.getCmp('ArchiveFilterAssignToOffice').getValue();
        if(val){
            params.assign_to_office = Ext.encode([val]);
        }


        store.getProxy().extraParams = params;
    }

});