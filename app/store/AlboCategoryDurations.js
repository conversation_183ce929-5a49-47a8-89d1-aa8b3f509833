/*
 * File: app/store/AlboCategoryDurations.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.AlboCategoryDurations', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'AlboCategoryDurations',
            data: [
                {
                    duration: 1
                },
                {
                    duration: 3
                },
                {
                    duration: 7
                },
                {
                    duration: 8
                },
                {
                    duration: 10
                },
                {
                    duration: 15
                },
                {
                    duration: 20
                },
                {
                    duration: 30
                },
                {
                    duration: 45
                },
                {
                    duration: 60
                }
            ],
            fields: [
                {
                    name: 'duration',
                    type: 'int'
                }
            ]
        }, cfg)]);
    }
});