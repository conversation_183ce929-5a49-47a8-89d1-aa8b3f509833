/*
 * File: app/store/Employees.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.Employees', {
    extend: 'Ext.data.Store',

    requires: [
        'mc2ui.model.Employee',
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Json'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            model: 'mc2ui.model.Employee',
            storeId: 'Employees',
            groupField: 'liquid_group',
            proxy: {
                type: 'ajax',
                api: {
                    read: '/mc2/applications/employees/employee/read.php',
                    destroy: '/mc2/applications/employees/employee/destroy.php'
                },
                reader: {
                    type: 'json',
                    idProperty: 'employee_id',
                    root: 'results'
                }
            },
            listeners: {
                load: {
                    fn: me.onJsonstoreLoad,
                    scope: me
                }
            }
        }, cfg)]);
    },

    onJsonstoreLoad: function(store, records, successful, eOpts) {
        Ext.getStore('EmployeesAll').load();
    },

    getGroupString: function(instance) {
        if (instance.get('liquid_group') == '0001'){
            return 'Docenti';
        } else if (instance.get('liquid_group') == '0002') {
            return 'ATA';
        } else {
            return 'Non definiti';
        }
    }

});