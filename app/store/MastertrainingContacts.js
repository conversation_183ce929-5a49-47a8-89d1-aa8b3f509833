/*
 * File: app/store/MastertrainingContacts.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.MastertrainingContacts', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.proxy.Ajax',
        'Ext.data.reader.Array',
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            autoLoad: true,
            storeId: 'MastertrainingContacts',
            data: [
                {
                    label: 'Telefono',
                    text: '0522 1590102'
                },
                {
                    label: 'Fax',
                    text: '0522 331673'
                },
                {
                    label: 'E-Mail Assistenza',
                    text: '<a href="mailto:<EMAIL>"><EMAIL></a>'
                },
                {
                    label: 'E-Mail Tesserini',
                    text: '<a href="mailto:<EMAIL>"><EMAIL></a>'
                }
            ],
            proxy: {
                type: 'ajax',
                reader: {
                    type: 'array'
                }
            },
            fields: [
                {
                    name: 'label'
                },
                {
                    name: 'text'
                }
            ]
        }, cfg)]);
    }
});