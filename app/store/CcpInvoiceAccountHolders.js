/*
 * File: app/store/CcpInvoiceAccountHolders.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpInvoiceAccountHolders', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            storeId: 'CcpInvoiceAccountHolders',
            fields: [
                {
                    name: 'surname'
                },
                {
                    name: 'name'
                },
                {
                    name: 'fiscal_code'
                },
                {
                    name: 'address'
                },
                {
                    name: 'city'
                },
                {
                    name: 'province'
                },
                {
                    name: 'zip_code'
                },
                {
                    name: 'db_id'
                },
                {
                    name: 'type'
                },
                {
                    name: 'piva'
                },
                {
                    name: 'sdi_code'
                }
            ]
        }, cfg)]);
    }
});