/*
 * File: app/store/CcpPrintGroupings.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.store.CcpPrintGroupings', {
    extend: 'Ext.data.Store',

    requires: [
        'Ext.data.Field'
    ],

    constructor: function(cfg) {
        var me = this;
        cfg = cfg || {};
        me.callParent([Ext.apply({
            autoLoad: true,
            storeId: 'CcpPrintGroupings',
            data: [
                {
                    id: 'Filtered',
                    text: 'Nessuno',
                    icon: 'icon-find'
                },
                {
                    id: 'ByClass',
                    text: 'Classe',
                    icon: 'icon-text_ab'
                },
                {
                    id: 'BySubject',
                    text: 'Debitore',
                    icon: 'icon-group'
                },
                {
                    id: 'ByType',
                    text: 'Tipo Movimento',
                    icon: 'icon-bricks'
                },
                {
                    id: 'ByCategory',
                    text: 'Categoria',
                    icon: 'icon-build'
                },
                {
                    id: 'ByDay',
                    text: 'Incassi Giornalieri',
                    icon: 'icon-build'
                }
            ],
            fields: [
                {
                    name: 'id'
                },
                {
                    name: 'text'
                },
                {
                    name: 'icon'
                }
            ]
        }, cfg)]);
    }
});