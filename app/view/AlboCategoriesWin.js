/*
 * File: app/view/AlboCategoriesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboCategoriesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboCategoriesWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 250,
    id: 'AlboCategoriesWin',
    itemId: 'AlboCategoriesWin',
    width: 700,
    title: 'Categorie',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'AlboCategoriesGrid',
                    itemId: 'AlboCategoriesGrid',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'AlboCategories',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            hideable: false,
                            text: 'Descrizione',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 80,
                            resizable: false,
                            align: 'right',
                            dataIndex: 'duration',
                            hideable: false,
                            text: 'Durata'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            permissible: true,
                            dock: 'top',
                            id: 'AlboCategoriesToolbar',
                            itemId: 'AlboCategoriesToolbar',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('AlboCategoryEditWin').show();
                                    },
                                    id: 'AlboCategoryNewBtn',
                                    itemId: 'AlboCategoryNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuova'
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onAlboCategoriesGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'AlboCategoryEditMn',
                    itemId: 'AlboCategoryEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('AlboCategoriesGrid').getSelectionModel().getSelection()[0];

                                Ext.widget('AlboCategoryEditWin').show();
                                Ext.getCmp('AlboCategoryEditForm').getForm().loadRecord(record);
                            },
                            id: 'contextAlboCategoryEdit',
                            itemId: 'contextAlboCategoryEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('AlboCategoriesGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questa Categoria?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('AlboCategories');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Categoria eliminata');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Categoria NON eliminata');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextAlboCategoryDelete',
                            itemId: 'contextAlboCategoryDelete',
                            iconCls: 'icon-delete',
                            text: 'Elimina'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onAlboCategoriesGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('AlboCategoryEditMn').showAt([newX,newY]);

        if (record.get('locked')) {
            Ext.getCmp('contextAlboCategoryDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextAlboCategoryDelete').setDisabled(false);
        }
    }

});