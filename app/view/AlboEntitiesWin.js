/*
 * File: app/view/AlboEntitiesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboEntitiesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboEntitiesWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 400,
    id: 'AlboEntitiesWin',
    itemId: 'AlboEntitiesWin',
    width: 700,
    title: 'Enti',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'AlboEntitiesGrid',
                    itemId: 'AlboEntitiesGrid',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'AlboEntities',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            hideable: false,
                            text: 'Descrizione',
                            flex: 1
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            permissible: true,
                            dock: 'top',
                            id: 'AlboEntitiesToolbar',
                            itemId: 'AlboEntitiesToolbar',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('AlboEntityEditWin').show();
                                    },
                                    id: 'AlboEntityNewBtn',
                                    itemId: 'AlboEntityNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onAlboEntitiesGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'AlboEntityEditMn',
                    itemId: 'AlboEntityEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('AlboEntitiesGrid').getSelectionModel().getSelection()[0];

                                Ext.widget('AlboEntityEditWin').show();

                                Ext.getCmp('AlboEntityEditForm').getForm().loadRecord(record);
                            },
                            id: 'contextAlboEntityEdit',
                            itemId: 'contextAlboEntityEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('AlboEntitiesGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questo Ente?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('AlboEntities');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Ente eliminato');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Ente NON eliminato');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextAlboEntityDelete',
                            itemId: 'contextAlboEntityDelete',
                            iconCls: 'icon-delete',
                            text: 'Elimina'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onAlboEntitiesGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('AlboEntityEditMn').showAt([newX,newY]);

        if (record.get('locked')) {
            Ext.getCmp('contextAlboEntityDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextAlboEntityDelete').setDisabled(false);
        }
    }

});