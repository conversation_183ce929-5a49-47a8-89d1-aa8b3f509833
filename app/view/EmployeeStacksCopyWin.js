/*
 * File: app/view/EmployeeStacksCopyWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeStacksCopyWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeStacksCopyWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 468,
    id: 'EmployeeStacksCopyWin',
    itemId: 'EmployeeStacksCopyWin',
    minHeight: 400,
    width: 354,
    title: 'Copia quote',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeStacksCopyWin_Container',
                    itemId: 'EmployeeStacksCopyWin_Container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'toolbar',
                            padding: '5 0',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    disabled: true,
                                    id: 'EmployeeStacksCopyWinBtn',
                                    itemId: 'EmployeeStacksCopyWinBtn',
                                    iconCls: 'icon-page_copy',
                                    text: 'Copia',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            border: false,
                            height: 250,
                            id: 'EmployeeStacksCopyWinEmployees',
                            itemId: 'EmployeeStacksCopyWinEmployees',
                            width: 400,
                            autoScroll: true,
                            title: 'Personale',
                            titleAlign: 'center',
                            emptyText: 'Nessun Personale',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            hideHeaders: true,
                            sortableColumns: false,
                            store: 'EmployeesTreeActive',
                            displayField: 'denomination',
                            useArrows: true,
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'denomination',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onEmployeeStacksCopyWinEmployeesCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeeParametersCopyWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        // Take the merge id to print and put it in a JSON encoded array
        var sel = Ext.getCmp('EmployeeStacksCopyWinEmployees').getChecked(),
            mergeSelect = new Array();

        Ext.each(sel, function(a) {
            if (a.data.leaf === true) {
                mergeSelect = mergeSelect.concat(a.raw.employee_id);
            }
        });

        var mergeSelectJSON = Ext.JSON.encode(mergeSelect);

        Ext.MessageBox.show({
            title:'Salvataggio quote',
            msg:'Se si procede, le quote per i monteore verranno salvate sul personale selezionato. Procedere?',
            buttons: Ext.Msg.YESNO,
            fn: function(r){
                if( r == 'yes' ){
                    Ext.getCmp('EmployeeStacksCopyWin').close();
                    Ext.getCmp('EmployeeSettingsMenuTab').saveStacksAmounts(mergeSelectJSON);
                }
            }
        });
    },

    onEmployeeStacksCopyWinEmployeesCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeStacksCopyWin').enableCopy();
    },

    onEmployeeParametersCopyWinActivate: function(window, eOpts) {
        var t = Ext.getCmp('EmployeeStacksCopyWinEmployees');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });
    },

    enableCopy: function() {
        var employees = Ext.getCmp('EmployeeStacksCopyWinEmployees').getChecked();

        if (employees.length > 0) {
            Ext.getCmp('EmployeeStacksCopyWinBtn').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeStacksCopyWinBtn').setDisabled(true);
        }
    }

});