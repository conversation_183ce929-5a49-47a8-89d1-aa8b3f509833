/*
 * File: app/view/EmployeeResidualsPrint_Win.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeResidualsPrint_Win', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeResidualsPrint_Win',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel'
    ],

    height: 468,
    id: 'EmployeeResidualsPrint_Win',
    itemId: 'EmployeeResidualsPrint_Win',
    minHeight: 400,
    minWidth: 300,
    width: 600,
    title: 'Stampa riepilogo Residui e Monteore',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeResidualsPrint_Container',
                    itemId: 'EmployeeResidualsPrint_Container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            border: false,
                            id: 'EmployeeResidualsPrint_Form',
                            itemId: 'EmployeeResidualsPrint_Form',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-box-layout-ct'
                            ],
                            bodyPadding: 10,
                            header: false,
                            title: 'My Form',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    id: 'EmployeeResidualsPrint_FilterMonth',
                                    itemId: 'EmployeeResidualsPrint_FilterMonth',
                                    width: 200,
                                    fieldLabel: 'Mese',
                                    labelAlign: 'right',
                                    inputId: 'month',
                                    editable: false,
                                    queryMode: 'local',
                                    store: 'Months',
                                    valueField: 'number'
                                },
                                {
                                    xtype: 'combobox',
                                    id: 'EmployeeResidualsPrint_FilterYear',
                                    itemId: 'EmployeeResidualsPrint_FilterYear',
                                    width: 200,
                                    fieldLabel: 'Anno',
                                    labelAlign: 'right',
                                    inputId: 'year',
                                    editable: false,
                                    displayField: 'year',
                                    queryMode: 'local',
                                    store: 'Years',
                                    valueField: 'year'
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: '5 0',
                                    layout: {
                                        type: 'hbox',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            disabled: true,
                                            id: 'EmployeeResidualsPrintBtnPrint',
                                            itemId: 'EmployeeResidualsPrintBtnPrint',
                                            iconCls: 'icon-printer',
                                            text: 'Stampa',
                                            listeners: {
                                                click: {
                                                    fn: me.onButtonClick,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'treepanel',
                                    flex: 1,
                                    height: 250,
                                    id: 'EmployeeResidualsPrint_Employees',
                                    itemId: 'EmployeeResidualsPrint_Employees',
                                    width: 400,
                                    autoScroll: true,
                                    title: 'Personale',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun Personale',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    hideHeaders: true,
                                    sortableColumns: false,
                                    store: 'EmployeesTreeActive',
                                    displayField: 'denomination',
                                    useArrows: true,
                                    viewConfig: {

                                    },
                                    columns: [
                                        {
                                            xtype: 'treecolumn',
                                            resizable: false,
                                            dataIndex: 'denomination',
                                            text: '',
                                            flex: 1
                                        }
                                    ],
                                    listeners: {
                                        checkchange: {
                                            fn: me.onEmployeeResidualsPrint_EmployeesCheckChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'EmployeeResidualsPrint_Stacks',
                                    itemId: 'EmployeeResidualsPrint_Stacks',
                                    autoScroll: true,
                                    title: 'Residui e Monteore',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun monteore impostato',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    sortableColumns: false,
                                    store: 'AbsenceStacksAndExtraordinary',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'denomination',
                                            text: '',
                                            flex: 1
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                                        checkOnly: true,
                                        listeners: {
                                            selectionchange: {
                                                fn: me.onCheckboxModelSelectionChange1,
                                                scope: me
                                            }
                                        }
                                    })
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeeResidualsPrint_WinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('EmployeeResidualsPrint_Container').setLoading();

        // Take period (month, year)
        var period = Ext.getCmp('EmployeeResidualsPrint_Form').getForm().getValues();


        // take all people to print and put it in JSON encode array of employee_id
        var sel = Ext.getCmp('EmployeeResidualsPrint_Employees').getChecked(),
            idSelect = new Array();
        Ext.each(sel, function(a){
            if (a.data.leaf === true) {
                idSelect = idSelect.concat(a.raw.employee_id);
            }
        });
        var idSelectJSON = Ext.JSON.encode(idSelect);

        // Take the hour stacks id to print and put it in a JSON encoded array
        if (mc2ui.app.settings.stacks_print_if_first_view === true) {
            var sel = Ext.getStore('AbsenceStacks').getRange();
        } else {
            var sel = Ext.getCmp('EmployeeResidualsPrint_Stacks').getSelectionModel().getSelection();
        }

        var idStackSelect = new Array();
        Ext.each(sel, function(a) {
            idStackSelect= idStackSelect.concat(parseInt(a.get('id')));
        });
        var idStackSelectJSON = Ext.JSON.encode(idStackSelect);

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 0,
                print: 'MonthResiduals',
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFMonthResiduals',
                mime: 'application/pdf',
                month: period.month,
                year: period.year,
                employees: idSelectJSON,
                stacks: idStackSelectJSON
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeeResidualsPrint_Container').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });
    },

    onEmployeeResidualsPrint_EmployeesCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeResidualsPrint_Win').enablePrint();
    },

    onCheckboxModelSelectionChange1: function(model, selected, eOpts) {
        Ext.getCmp('EmployeeResidualsPrint_Win').enablePrint();
    },

    onEmployeeResidualsPrint_WinActivate: function(window, eOpts) {
        var year = parseInt(Ext.Date.format(new Date(),'Y'));
        Ext.getCmp('EmployeeResidualsPrint_FilterYear').setValue(year);

        var month = parseInt(Ext.Date.format(new Date(),'m'));
        Ext.getCmp('EmployeeResidualsPrint_FilterMonth').setValue(month);

        Ext.getCmp('EmployeeResidualsPrint_Win').selectAllStacks();

        var t = Ext.getCmp('EmployeeResidualsPrint_Employees');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        Ext.getCmp('EmployeeResidualsPrint_Win').enablePrint();
    },

    enablePrint: function() {
        var employees = Ext.getCmp('EmployeeResidualsPrint_Employees').getChecked(),
            stacks = Ext.getCmp('EmployeeResidualsPrint_Stacks').getSelectionModel().getSelection();

        if (employees.length > 0 && stacks.length > 0) {
            Ext.getCmp('EmployeeResidualsPrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeResidualsPrintBtnPrint').setDisabled(true);
        }
    },

    selectAllStacks: function() {
        var grid = Ext.getCmp('EmployeeResidualsPrint_Stacks').getSelectionModel();
        grid.selectAll();
    }

});