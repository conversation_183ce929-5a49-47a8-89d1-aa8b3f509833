/*
 * File: app/view/MainView.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.MainView', {
    extend: 'Ext.container.Viewport',
    alias: 'widget.MainView',

    requires: [
        'mc2ui.view.HomePnl',
        'mc2ui.view.EmployeePnl',
        'mc2ui.view.ProtocolPnl',
        'mc2ui.view.AlboPnl',
        'mc2ui.view.TrasparenzaPnl',
        'mc2ui.view.SettingsPanel',
        'mc2ui.view.ArchivePnl',
        'mc2ui.view.CcpPnl',
        'Ext.panel.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.form.Label',
        'Ext.button.Button'
    ],

    permissible: true,
    id: 'MainView',
    itemId: 'MainView',
    style: 'background-image: none;',
    layout: 'fit',

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    frame: true,
                    id: 'MC2MainPnl',
                    itemId: 'MC2MainPnl',
                    layout: 'border',
                    title: 'MASTERCOM 2',
                    titleAlign: 'center',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            region: 'north',
                            dock: 'top',
                            height: 30,
                            id: 'AdminTlb',
                            itemId: 'AdminTlb',
                            items: [
                                {
                                    xtype: 'label',
                                    html: '<b>Pannello amministrazione</b>',
                                    padding: 5
                                },
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        clearInterval(mc2ui.app.spoolInterval);
                                    },
                                    iconCls: 'icon-printer_cancel',
                                    text: 'Stop print spool'
                                },
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        Ext.widget('AdminDataLookupWin').show();
                                    },
                                    iconCls: 'icon-find',
                                    text: 'Lookup data'
                                },
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        Ext.widget('AdminParametersWin').show();
                                    },
                                    iconCls: 'icon-cog_edit',
                                    text: 'Manage parameters'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'panel',
                            activeMenuButton: function (element_id) {
                                var active_elements = Ext.query('.icon-menu-active');

                                Ext.each(active_elements, function (el) {
                                    Ext.getCmp(el.id).removeCls('icon-menu-active');
                                });

                                Ext.getCmp(element_id).addCls('icon-menu-active');
                            },
                            checkAreaActivation: function (area, element) {
                                var msg = "L'area a cui si vuole accedere non risulta ancora attiva." +
                                    "<br/>Contattare l'assistenza per l'attivazione.";

                                Ext.Ajax.request({
                                    url: '/mc2/applications/check_area_activation.php',
                                    method: 'POST',
                                    params: { 'area': area },
                                    success: function (response) {
                                        var res = Ext.decode(response.responseText);
                                        if (res.success !== false) {
                                            Ext.getCmp('MainCenterPnl').items.each(function (item) {
                                                item.hide();
                                            });

                                            Ext.getCmp(res.success).show();
                                            Ext.getCmp('MC2MainPnl').setTitle('MASTERCOM 2 - ' + res.title);
                                            Ext.getCmp('MainMenuPnl').activeMenuButton(element);
                                            if (area === 'AREA_HOME') {
                                                Ext.getStore('HomeInfos').load();
                                            }
                                        } else {
                                            Ext.Msg.alert("Mastercom2", msg);
                                        }
                                    },
                                    failure: function (response, opts) {
                                        Ext.Msg.alert("Mastercom2", msg);
                                    }
                                });
                            },
                            permissible: true,
                            region: 'west',
                            split: true,
                            splitterResize: false,
                            id: 'MainMenuPnl',
                            itemId: 'MainMenuPnl',
                            width: 180,
                            autoScroll: true,
                            bodyCls: [
                                'menu-cnt',
                                'x-panel-body-default',
                                'x-box-layout-ct'
                            ],
                            collapseDirection: 'left',
                            collapsible: true,
                            iconCls: 'icon-plugin',
                            title: 'Sezioni',
                            titleCollapse: true,
                            layout: {
                                type: 'vbox',
                                align: 'stretch',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    id: 'MenuHomeCnt',
                                    itemId: 'MenuHomeCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_HOME', 'MenuHomeCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();


                                                /*Ext.getCmp('MainCenterPnl').items.each(function (item){
                                                item.hide();
                                                });

                                                Ext.getCmp('HomePnl').show();
                                                Ext.getStore('HomeInfos').load();
                                                Ext.getCmp('MainMenuPnl').activeMenuButton('MenuHomeCnt');*/
                                            },
                                            cls: 'icon-btn-menu icon-home',
                                            height: 65,
                                            id: 'MenuHomeBtn',
                                            itemId: 'MenuHomeBtn',
                                            width: 65,
                                            tooltip: 'Informazioni & Stampe'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuHomeLbl',
                                                    itemId: 'MenuHomeLbl',
                                                    text: 'Info & Stampe'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuEmployeeCnt',
                                    itemId: 'MenuEmployeeCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_PERSONNEL', 'MenuEmployeeCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-employee',
                                            height: 65,
                                            id: 'MenuEmployeeBtn',
                                            itemId: 'MenuEmployeeBtn',
                                            width: 65,
                                            tooltip: 'Personale'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuEmployeeLbl',
                                                    itemId: 'MenuEmployeeLbl',
                                                    text: 'Personale'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuCcpCnt',
                                    itemId: 'MenuCcpCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_CCP', 'MenuCcpCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-ccp',
                                            height: 65,
                                            id: 'MenuCcpBtn',
                                            itemId: 'MenuCcpBtn',
                                            width: 65,
                                            tooltip: 'Conti Correnti'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: 'Gestione rette<br>e conti correnti',
                                                    id: 'MenuCcpLbl',
                                                    itemId: 'MenuCcpLbl'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuMailAccountCnt',
                                    itemId: 'MenuMailAccountCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_MAIL_ACCOUNT', 'MenuMailAccountCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-mail_account',
                                            height: 65,
                                            id: 'MenuMailAccountBtn',
                                            itemId: 'MenuMailAccountBtn',
                                            width: 65,
                                            tooltip: 'Account di posta'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    html: 'Account di posta',
                                                    id: 'MenuMailAccountLbl',
                                                    itemId: 'MenuMailAccountLbl'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    hidden: true,
                                    id: 'MenuWarehouseCnt',
                                    itemId: 'MenuWarehouseCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_WAREHOUSE', 'MenuWarehouseCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-warehouse',
                                            height: 65,
                                            id: 'MenuWarehouseBtn',
                                            itemId: 'MenuWarehouseBtn',
                                            width: 65,
                                            tooltip: 'Magazzino & Inventario'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuWarehouseLbl',
                                                    itemId: 'MenuWarehouseLbl',
                                                    text: 'Mag. & Inv.'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuArchiveCnt',
                                    itemId: 'MenuArchiveCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_ARCHIVE', 'MenuArchiveCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-archive',
                                            height: 65,
                                            id: 'MenuArchiveBtn',
                                            itemId: 'MenuArchiveBtn',
                                            width: 65,
                                            tooltip: 'Archivio Documenti'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuArchiveLbl',
                                                    itemId: 'MenuArchiveLbl',
                                                    text: 'Segreteria Digitale'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuProtocolCnt',
                                    itemId: 'MenuProtocolCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_PROTOCOL', 'MenuProtocolCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-protocol',
                                            height: 65,
                                            id: 'MenuProtocolBtn',
                                            itemId: 'MenuProtocolBtn',
                                            width: 65,
                                            tooltip: 'Protocollo Informatico'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuProtocolLbl',
                                                    itemId: 'MenuProtocolLbl',
                                                    text: 'Protocollo'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuAlboCnt',
                                    itemId: 'MenuAlboCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_ALBO', 'MenuAlboCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-albo',
                                            height: 65,
                                            id: 'MenuAlboBtn',
                                            itemId: 'MenuAlboBtn',
                                            width: 65,
                                            tooltip: 'Albo Pretorio'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuAlboLbl',
                                                    itemId: 'MenuAlboLbl',
                                                    text: 'Albo Pretorio'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuTrasparenzaCnt',
                                    itemId: 'MenuTrasparenzaCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_TRASPARENZA', 'MenuTrasparenzaCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();
                                            },
                                            cls: 'icon-btn-menu icon-trasparenza',
                                            height: 65,
                                            id: 'MenuTrasparenzaBtn',
                                            itemId: 'MenuTrasparenzaBtn',
                                            width: 65,
                                            tooltip: 'Trasparenza Amministrativa'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuTrasparenzaLbl',
                                                    itemId: 'MenuTrasparenzaLbl',
                                                    text: 'Trasparenza'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    id: 'MenuSettingsCnt',
                                    itemId: 'MenuSettingsCnt',
                                    margin: '0 0 10 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.getCmp('MainMenuPnl').checkAreaActivation('AREA_SETTINGS', 'MenuSettingsCnt');
                                                Ext.getCmp('MainMenuPnl').collapse();

                                                /*Ext.getCmp('MainCenterPnl').items.each(function (item){
                                                item.hide();
                                                });

                                                Ext.getCmp('SettingsPanel').show();
                                                Ext.getCmp('MainMenuPnl').activeMenuButton('MenuSettingsCnt');*/
                                            },
                                            cls: 'icon-btn-menu icon-settings',
                                            height: 65,
                                            id: 'MenuSettingsBtn',
                                            itemId: 'MenuSettingsBtn',
                                            width: 65,
                                            tooltip: 'Impostazioni'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'center'
                                            },
                                            items: [
                                                {
                                                    xtype: 'label',
                                                    id: 'MenuSettingsLbl',
                                                    itemId: 'MenuSettingsLbl',
                                                    text: 'Impostazioni'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'container',
                                    dock: 'bottom',
                                    id: 'MenuExitCnt',
                                    itemId: 'MenuExitCnt',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.Ajax.request({
                                                    url: '/mc2/applications/core/logout.php',
                                                    success: function (response) {
                                                        res = Ext.decode(response.responseText);
                                                        if (res.success === true) {
                                                            document.cookie = 'PHPSESSID=; expires=Thu, 01 Jan 1970 00:00:00 UTC; path=/;';
                                                            window.location = './';
                                                        }
                                                    }
                                                });
                                            },
                                            flex: 1,
                                            height: 30,
                                            text: 'USCITA'
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                boxready: {
                                    fn: me.onMainMenuPnlBoxReady,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'panel',
                            flex: 1,
                            region: 'center',
                            id: 'MainCenterPnl',
                            itemId: 'MainCenterPnl',
                            header: false,
                            overlapHeader: false,
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'HomePnl',
                                    flex: 1
                                },
                                {
                                    xtype: 'EmployeePnl',
                                    flex: 1
                                },
                                {
                                    xtype: 'ProtocolPnl',
                                    flex: 1
                                },
                                {
                                    xtype: 'AlboPnl',
                                    flex: 1
                                },
                                {
                                    xtype: 'TrasparenzaPnl',
                                    flex: 1
                                },
                                {
                                    xtype: 'SettingsPanel',
                                    flex: 1
                                },
                                {
                                    xtype: 'MailAccountPnl',
                                    flex: 1
                                },
                                {
                                    xtype: 'ArchivePnl',
                                    flex: 1
                                },
                                {
                                    xtype: 'CcpPnl',
                                    flex: 1
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onMainMenuPnlBoxReady: function (component, width, height, eOpts) {
        Ext.getCmp('MainMenuPnl').activeMenuButton('MenuHomeCnt');
    },

    hideNotAllowedElements: function (view) {
        Ext.iterate(mc2ui.app.permissionUI[view], function (el, state) {
            if (state == 'hide') {
                Ext.getCmp(el).hide();
            } else {
                Ext.getCmp(el).disable();
            }
        });

    }

});