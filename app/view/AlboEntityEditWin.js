/*
 * File: app/view/AlboEntityEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboEntityEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboEntityEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.TextArea',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 200,
    id: 'AlboEntityEditWin',
    itemId: 'AlboEntityEditWin',
    width: 400,
    title: 'Ente',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'AlboEntityEditForm',
                    itemId: 'AlboEntityEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'AlboEntityEditId',
                            itemId: 'AlboEntityEditId',
                            name: 'id'
                        },
                        {
                            xtype: 'textfield',
                            id: 'AlboEntityEditName',
                            itemId: 'AlboEntityEditName',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            name: 'name',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            id: 'AlboEntityEditDescription',
                            itemId: 'AlboEntityEditDescription',
                            fieldLabel: 'Descrizione',
                            labelAlign: 'right',
                            name: 'description'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('AlboEntities'),
                                            form = Ext.getCmp('AlboEntityEditForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvato';

                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornato';
                                            record = store.getById(parseInt(values.id));
                                            record.set('name', values.name);
                                            record.set('description', values.description);
                                        } else {
                                            store.add({
                                                name: values.name,
                                                description: values.description
                                            });
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('AlboEntityEditWin').close();
                                                Ext.Msg.alert('Successo', 'Ente ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Ente NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});