/*
 * File: app/view/UserEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.UserEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.UserEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.ComboBox',
        'Ext.form.FieldSet',
        'Ext.form.field.Checkbox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    id: 'UserEditWin',
    itemId: 'UserEditWin',
    width: 370,
    resizable: false,
    layout: 'fit',
    title: 'Utente',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'EditUserFrm',
                    itemId: 'EditUserFrm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default',
                        'x-box-layout-ct'
                    ],
                    bodyPadding: 10,
                    url: '/mc2/applications/core/users/write.php',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'user_id',
                            itemId: 'Uid',
                            fieldLabel: 'Label',
                            inputId: 'Uid'
                        },
                        {
                            xtype: 'textfield',
                            id: 'user_name',
                            fieldLabel: 'Nome utente',
                            labelAlign: 'right',
                            inputId: 'UserName',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'textfield',
                            id: 'user_password',
                            itemId: 'UserPassword',
                            fieldLabel: 'Password',
                            labelAlign: 'right',
                            inputId: 'UserPassword',
                            inputType: 'password',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'textfield',
                            id: 'user_password_r',
                            itemId: 'UserPasswordR',
                            fieldLabel: 'Ripeti password',
                            labelAlign: 'right',
                            inputId: 'UserPasswordR',
                            inputType: 'password',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'textfield',
                            name: 'name',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                        },
                        {
                            xtype: 'textfield',
                            name: 'surname',
                            fieldLabel: 'Cognome',
                            labelAlign: 'right',
                        },
                        {
                            xtype: 'textfield',
                            name: 'fiscal_code',
                            fieldLabel: 'Codice fiscale',
                            labelAlign: 'right',
                        },
                        {
                            xtype: 'textfield',
                            id: 'email',
                            itemId: 'Email',
                            fieldLabel: 'Email',
                            labelAlign: 'right',
                            inputId: 'Email'
                        },
                        {
                            xtype: 'combobox',
                            id: 'user_type',
                            itemId: 'UserType',
                            fieldLabel: 'Gruppo',
                            labelAlign: 'right',
                            inputId: 'UserType',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            editable: false,
                            displayField: 'group_name',
                            store: 'SettingsGroups',
                            valueField: 'gid'
                        },
                        {
                            xtype: 'combobox',
                            flex: 1,
                            fieldLabel: 'Personale',
                            labelAlign: 'right',
                            name: 'employee_id',
                            checkChangeBuffer: 500,
                            inputId: 'employee_id',
                            emptyText: 'Cerca ...',
                            hideTrigger: true,
                            displayField: 'denomination',
                            forceSelection: true,
                            minChars: 3,
                            queryMode: 'local',
                            store: 'Employees',
                            typeAhead: true,
                            typeAheadDelay: 500,
                            valueField: 'employee_id'
                        },
                        {
                            xtype: 'fieldset',
                            title: 'Parametri',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    id: 'enabled',
                                    inputId: 'Enabled',
                                    boxLabel: 'Attivo',
                                    inputValue: '1'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    id: 'privelege',
                                    inputId: 'Privelege',
                                    boxLabel: 'Amministratore',
                                    inputValue: '1'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    hidden: true,
                                    inputId: 'ModifyProtocol',
                                    boxLabel: 'Abilitato a modificare i protocolli',
                                    inputValue: '1'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    hidden: true,
                                    boxLabel: 'Abilitato al protocollo riservato'
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('EditUserFrm').getForm().submit({
            success: function(){
                Ext.getCmp('UserEditWin').close();
                Ext.getStore('SettingsUsers').load();
                Ext.getCmp('UsersGrid').getSelectionModel().deselectAll();
            }
        });


    }

});