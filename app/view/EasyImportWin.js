/*
 * File: app/view/EasyImportWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EasyImportWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EasyImportWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.File',
        'Ext.button.Button',
        'Ext.form.FieldSet'
    ],

    height: 325,
    id: 'EasyImportWin',
    itemId: 'EasyImportWin',
    width: 658,
    layout: 'fit',
    title: 'Importazione da EASY delle fatture',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'EasyImportFrm',
                    itemId: 'EasyImportFrm',
                    bodyPadding: 10,
                    title: '',
                    method: 'POST',
                    url: '/mc2-api/ccp/import_easy_invoice',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'filefield',
                            fieldLabel: '',
                            labelAlign: 'top',
                            name: 'easyInvoiceUpload',
                            allowBlank: false,
                            emptyText: 'Carica il file esportato da EASY'
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'vbox',
                                align: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {



                                        if (Ext.getCmp('EasyImportFrm').getForm().isValid()) {
                                            Ext.getCmp('EasyErrorFs').removeAll();
                                            Ext.getCmp('EasyImportWin').setLoading(true);

                                            Ext.getCmp('EasyImportFrm').submit({
                                                success: function (r, res) {
                                                    var importErrors = Ext.decode(res.response.responseText).errors,
                                                        lb;
                                                    Ext.getCmp('EasyImportWin').setLoading(false);
                                                    Ext.getStore('CcpInvoices').load();
                                                    if (importErrors.length > 0 ) {

                                                        Ext.each(importErrors, function(e) {
                                                            lb = Ext.create('Ext.form.Label', {
                                                                text: e,
                                                                margin: '2 0'
                                                            });
                                                            Ext.getCmp('EasyErrorFs').add(lb);
                                                        });

                                                    }
                                                }
                                            });
                                        }


                                    },
                                    text: 'Importa'
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            id: 'EasyErrorFs',
                            itemId: 'EasyErrorFs',
                            autoScroll: true,
                            collapsible: true,
                            title: 'Errori',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});