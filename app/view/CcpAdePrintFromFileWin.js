/*
 * File: app/view/CcpAdePrintFromFileWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpAdePrintFromFileWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpAdePrintFromFileWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.File',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    width: 443,
    layout: 'fit',
    title: 'Stampa da file',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpAdePrintFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/ccp/ade-print',
                    items: [
                        {
                            xtype: 'filefield',
                            anchor: '100%',
                            fieldLabel: 'File AdE',
                            labelWidth: 200,
                            name: 'file'
                        },
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            fieldLabel: 'Studente specifico (codice fiscale)',
                            labelWidth: 200,
                            name: 'fiscal_code'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'bottom',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('CcpAdePrintFrm').submit({
                                            success: function(e, response) {
                                                var res = Ext.decode(response.response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                            },
                                            failure: function(e, response) {
                                                var res = Ext.decode(response.response.responseText);
                                                Ext.Msg.alert('ERRORE' , res.message);
                                            }
                                        });


                                    },
                                    text: 'Stampa'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});