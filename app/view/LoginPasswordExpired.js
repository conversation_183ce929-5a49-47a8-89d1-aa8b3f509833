/*
 * File: app/view/LoginPasswordExpired.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.LoginPasswordExpired', {
    extend: 'Ext.window.Window',
    alias: 'widget.LoginPasswordExpired',

    requires: [
        'Ext.form.Panel',
        'Ext.form.Label',
        'Ext.form.field.Text',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    id: 'LoginPasswordExpired',
    itemId: 'LoginPasswordExpired',
    width: 340,
    title: 'Attenzione',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'LoginPasswordExpiredForm',
                    itemId: 'LoginPasswordExpiredForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    url: '/mc2/applications/core/users/change_paswd.php',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'label',
                            margin: '0 0 15 0',
                            text: 'La vostra password risulta scaduta: occorre aggiornarla.'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'vbox',
                                align: 'center'
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    id: 'LoginPasswordExpiredPwd',
                                    itemId: 'LoginPasswordExpiredPwd',
                                    fieldLabel: 'Nuova password',
                                    labelAlign: 'right',
                                    msgTarget: 'side',
                                    name: 'passwd',
                                    inputType: 'password',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    minLength: 8
                                },
                                {
                                    xtype: 'textfield',
                                    id: 'LoginPasswordExpiredPwdR',
                                    itemId: 'LoginPasswordExpiredPwdR',
                                    fieldLabel: 'Ripeti password',
                                    labelAlign: 'right',
                                    msgTarget: 'side',
                                    name: 'passwdRepeat',
                                    inputType: 'password',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    minLength: 8
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('SettingsUsers'),
                                            form = Ext.getCmp('LoginPasswordExpiredForm').getForm();

                                        form.submit({
                                            params:{
                                                user_id: Ext.getCmp('LoginPasswordExpired').user,
                                                force_mc2: Ext.getCmp('LoginPasswordExpired').force_mc2
                                            },
                                            success : function(form, action) {
                                                if (action.result.success === true) {
                                                    Ext.getCmp('LoginPasswordExpired').close();
                                                    Ext.Msg.alert('Successo', 'Password aggiornata correttamente; è nuovamente possibile effettuare l\'autenticazione');
                                                }
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Aggiorna'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});