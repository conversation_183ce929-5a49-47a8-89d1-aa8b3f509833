/*
 * File: app/view/CcpDepositSlipNewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpDepositSlipNewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpDepositSlipNewWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Number',
        'Ext.form.field.Checkbox',
        'Ext.grid.Panel',
        'Ext.grid.column.Number',
        'Ext.grid.column.Date',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Toolbar',
        'Ext.form.field.Date',
        'Ext.toolbar.Spacer',
        'Ext.button.Button',
        'Ext.form.Label'
    ],

    height: 600,
    id: 'CcpDepositSlipNewWin',
    itemId: 'CcpDepositSlipNewWin',
    width: 793,
    title: 'Creazione distinta',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpDepositSlipAddFrm',
                    itemId: 'CcpDepositSlipAddFrm',
                    bodyPadding: 10,
                    title: '',
                    items: [
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            width: '',
                            fieldLabel: 'Anno scolastico',
                            labelWidth: 150,
                            name: 'school_year',
                            displayField: 'name',
                            store: 'McDbs',
                            valueField: 'name',
                            listeners: {
                                afterrender: {
                                    fn: me.onComboboxAfterRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'numberfield',
                            anchor: '100%',
                            fieldLabel: 'Numero',
                            labelWidth: 150,
                            name: 'number',
                            hideTrigger: true
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            fieldLabel: 'Conto corrente di accredito',
                            labelWidth: 150,
                            name: 'bank_account',
                            allowBlank: false,
                            displayField: 'denomination',
                            forceSelection: true,
                            store: 'CoreBankAccounts',
                            valueField: 'id'
                        },
                        {
                            xtype: 'checkboxfield',
                            fieldLabel: 'Raggruppa fatture fratelli',
                            labelWidth: 150,
                            name: 'sons_merge',
                            boxLabel: '',
                            inputValue: '1',
                            uncheckedValue: '0',
                            listeners: {
                                afterrender: {
                                    fn: me.onCheckboxfieldAfterRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            hidden: true,
                            fieldLabel: 'Metodo di pagamento',
                            labelWidth: 150,
                            name: 'payment_method',
                            allowBlank: false,
                            displayField: 'name',
                            forceSelection: true,
                            store: 'CcpPaymentMethods',
                            valueField: 'id'
                        },
                        {
                            xtype: 'checkboxfield',
                            anchor: '100%',
                            hidden: true,
                            fieldLabel: 'Raggruppa',
                            labelWidth: 150,
                            name: 'group',
                            boxLabel: '',
                            inputValue: 'true',
                            uncheckedValue: 'false'
                        }
                    ]
                },
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'CcpInvoicesToDepositSlipGrid',
                    itemId: 'CcpInvoicesToDepositSlipGrid',
                    title: 'Elenco fatture',
                    store: 'CcpInvoicesToDepositSlip',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                var number = value;

                                if (record.get('suffix')) number += '_' + record.get('suffix');

                                return number;
                            },
                            width: 80,
                            align: 'center',
                            dataIndex: 'number',
                            text: 'Numero'
                        },
                        {
                            xtype: 'datecolumn',
                            align: 'center',
                            dataIndex: 'date',
                            text: 'Data',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {

                                var expirations = value.split(','),
                                    error_expirations = record.get('error_expirations') ? record.get('error_expirations').split(','):[],
                                    total_expirations = record.get('total_expirations') ? record.get('total_expirations').split(','):[],
                                    errDate = error_expirations.length>0 ?  '<font style=\'color:red\'>' + error_expirations.join('<br />')+ '</font><br />' : '';

                                metaData.tdAttr = 'data-qtip="' + errDate + expirations.join('<br />')+'</b>"';
                                return expirations.length+'/'+total_expirations.length;
                            },
                            align: 'center',
                            dataIndex: 'expirations',
                            text: 'Scadenze'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'accountholder',
                            text: 'Intestatario',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'debitors',
                            text: 'Studente/Debitore',
                            flex: 1
                        },
                        {
                            xtype: 'numbercolumn',
                            width: 130,
                            align: 'right',
                            dataIndex: 'remain_to_pay',
                            text: 'Da pagare (filtrati)'
                        },
                        {
                            xtype: 'numbercolumn',
                            align: 'right',
                            dataIndex: 'total',
                            text: 'Totale'
                        }
                    ],
                    viewConfig: {
                        getRowClass: function(record, rowIndex, rowParams, store) {
                            if(record.get('error_expirations')) {
                                return 'negative';
                            }
                        }
                    },
                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                    }),
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'datefield',
                                    id: 'CcpDepositSlipInvoiceExpirationStart',
                                    fieldLabel: 'Scadenza da',
                                    name: 'expiration_date_start',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d'
                                },
                                {
                                    xtype: 'datefield',
                                    id: 'CcpDepositSlipInvoiceExpirationEnd',
                                    margin: '0 0 0 5',
                                    fieldLabel: 'a',
                                    labelWidth: 15,
                                    name: 'expiration_date_end',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('CcpDepositSlipCreateErrorTb').hide();
                                        Ext.getStore('CcpInvoicesToDepositSlip').load({
                                            callback: function(r){
                                                Ext.each(r, function(val) {
                                                    if(val.get('error_expirations')){
                                                        Ext.getCmp('CcpDepositSlipCreateErrorTb').show();
                                                    }
                                                });

                                            }
                                        });
                                    },
                                    text: 'Filtra'
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            height: 40,
                            hidden: true,
                            id: 'CcpDepositSlipCreateErrorTb',
                            layout: {
                                type: 'hbox',
                                padding: '-10 2 2 2'
                            },
                            items: [
                                {
                                    xtype: 'label',
                                    margin: '-20 0 0 0',
                                    style: 'color:red',
                                    text: 'Le righe in rosso contengono fatture con movimenti non ancora pagati ma che hanno una scadenza minore di quella filtrata. Verifiicare i dati prima di procedere.'
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    id: 'CcpDepositSlipTb',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var values = Ext.getCmp('CcpDepositSlipAddFrm').getValues(),
                                    records = Ext.getCmp('CcpInvoicesToDepositSlipGrid').getSelectionModel().getSelection(),
                                    ids = [],
                                    data = {};

                                Ext.each(records, function(v) {
                                    ids.push(v.get('id'));
                                });

                                // Ext.getStore('CoreBankAccounts').load();

                                data =  {
                                    number: values.number,
                                    bank_account: values.bank_account,
                                    payment_method: values.payment_method,
                                    invoices: ids,
                                    group: values.group,
                                    sons_merge: parseInt(values.sons_merge),
                                    school_year: values.school_year,
                                    expiration_date_end: Ext.getCmp('CcpDepositSlipInvoiceExpirationEnd').getValue(),
                                    expiration_date_start: Ext.getCmp('CcpDepositSlipInvoiceExpirationStart').getValue()
                                };

                                Ext.getCmp('CcpDepositSlipNewWin').setLoading(true);
                                Ext.Ajax.request({
                                    url: '/mc2-api/ccp/deposit_slip',
                                    method: 'POST',
                                    jsonData: data,
                                    success: function(r) {
                                        Ext.getCmp('CcpDepositSlipNewWin').setLoading(false);
                                        var response = Ext.decode(r.responseText);
                                        if (response.success) {
                                            Ext.getCmp('CcpDepositSlipNewWin').close();
                                            Ext.getStore('CcpDepositSlips').load();
                                        } else {
                                            Ext.Msg.alert('ERRORE', response.message);
                                        }
                                    }
                                });
                            },
                            text: 'Genera distinta'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpDepositSlipNewWinShow,
                    scope: me
                },
                close: {
                    fn: me.onCcpDepositSlipNewWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onComboboxAfterRender: function(component, eOpts) {
        Ext.getStore('McDbs').filterBy(function(v){
            return v.get('current')===true;
        });
        var year = Ext.getStore('McDbs').getRange()[0];
        Ext.getStore('McDbs').clearFilter();
        if(year) {
            component.select(year);
        }
    },

    onCheckboxfieldAfterRender: function(component, eOpts) {
        if(mc2ui.app.settings.sepaCheckBrother === true) {
            component.setValue(1);
        }
    },

    onCcpDepositSlipNewWinShow: function(component, eOpts) {
        Ext.getStore('CcpInvoicesToDepositSlip').removeAll();
    },

    onCcpDepositSlipNewWinClose: function(panel, eOpts) {
        var invoiceEnabled = Ext.getCmp('CcpMainPnl').getActiveTab().id == 'CcpInvoicePanel';

        if(Ext.getCmp('CcpMainPnl').getActiveTab().id != 'CcpInvoicePanel' && mc2ui.app.settings.invoiceEnabled===false) {
            Ext.getCmp('CcpInvoiceNewWin').close();
        }
    }

});