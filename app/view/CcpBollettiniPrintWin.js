/*
 * File: app/view/CcpBollettiniPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpBollettiniPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpBollettiniPrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.form.FieldSet',
        'Ext.form.CheckboxGroup',
        'Ext.form.field.Checkbox',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.form.field.Hidden'
    ],

    height: 520,
    id: 'CcpBollettiniPrintWin',
    itemId: 'CcpBollettiniPrintWin',
    width: 650,
    resizable: false,
    title: 'Produzione bollettini',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    getData: function() {
                        var rec = Ext.getCmp('CcpBollettiniFrm').getValues(),
                            students = Ext.getCmp('CcpBollettiniStudents').getChecked(),
                            filter = {},
                            s = [];

                        filter.printSettings = Ext.encode(rec);

                        for (var k in students) {
                            if (students[k].data.leaf === true) {
                                s.push(students[k].data.id);
                            }
                        }

                        filter.students = Ext.encode(s);

                        return filter;
                    },
                    flex: 1,
                    border: false,
                    id: 'CcpBollettiniFrm',
                    itemId: 'CcpBollettiniFrm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default',
                        'x-box-layout-ct'
                    ],
                    header: false,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    disabled: true,
                                    id: 'CcpBollettiniPrintBtn',
                                    itemId: 'CcpBollettiniPrintBtn',
                                    iconCls: 'icon-printer',
                                    text: 'Stampa',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'container',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center',
                                padding: 10
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'CcpBollettiniModel',
                                    width: 200,
                                    name: 'template',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    emptyText: 'Modello...',
                                    editable: false,
                                    displayField: 'description',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'CcpBollettini',
                                    valueField: 'name',
                                    listeners: {
                                        select: {
                                            fn: me.onCcpBollettiniModelSelect,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'panel',
                                    flex: 1,
                                    id: 'CcpBollSettPnl',
                                    itemId: 'CcpBollSettPnl',
                                    title: 'Caratteristiche',
                                    titleAlign: 'center',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        padding: 5
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'CcpBollettiniTaxType',
                                            margin: '0 0 0 0',
                                            name: 'id_causale',
                                            emptyText: 'Causale (Tipo di movimento) ...',
                                            displayField: 'name',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'CcpTypes',
                                            typeAhead: true,
                                            valueField: 'id'
                                        },
                                        {
                                            xtype: 'fieldset',
                                            margin: '15 0 0 0',
                                            title: 'Anagrafica',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'checkboxgroup',
                                                    columns: 2,
                                                    items: [
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'eseguito_da',
                                                            boxLabel: 'Esecutore',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'eseguito_da_gen',
                                                            boxLabel: 'Esecutore (genitore)',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'residente_in_via',
                                                            boxLabel: 'Indirizzo ',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'localita',
                                                            boxLabel: 'Località',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'cap',
                                                            boxLabel: 'CAP',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'codice_fiscale',
                                                            boxLabel: 'Codice Fiscale',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'data_nascita',
                                                            boxLabel: 'Data di nascita',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'sesso',
                                                            boxLabel: 'Sesso',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'provincia_nascita',
                                                            boxLabel: 'Provincia di nascita',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'nascita',
                                                            boxLabel: 'Luogo di nascita',
                                                            checked: true
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'fieldset',
                                            margin: '15 0 0 0',
                                            title: 'Dati contabili',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'checkboxgroup',
                                                    columns: 2,
                                                    items: [
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'intestato_a',
                                                            boxLabel: 'Intestatario',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'causale',
                                                            boxLabel: 'Causale',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'cc_p',
                                                            boxLabel: 'Conto Corrente',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'sum',
                                                            boxLabel: 'Importo (cifre)',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'doc_type',
                                                            boxLabel: 'Tipo Documento',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'sum_words',
                                                            boxLabel: 'Importo (lettere)',
                                                            checked: true
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'fieldset',
                                            margin: '15 0 0 0',
                                            title: 'Dati scolastici',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'checkboxgroup',
                                                    labelAlign: 'top',
                                                    columns: 2,
                                                    items: [
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'anno_scolastico',
                                                            boxLabel: 'Anno Scolastico',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'eseguito_da_matricola',
                                                            boxLabel: 'Matricola',
                                                            checked: true
                                                        },
                                                        {
                                                            xtype: 'checkboxfield',
                                                            name: 'eseguito_da_classe',
                                                            boxLabel: 'Classe',
                                                            checked: true
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'treepanel',
                                    flex: 1,
                                    id: 'CcpBollettiniStudents',
                                    title: 'Studenti',
                                    titleAlign: 'center',
                                    emptyText: 'Nessuno studente trovato.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    store: 'CcpStudentsTree',
                                    lines: false,
                                    useArrows: true,
                                    viewConfig: {

                                    },
                                    columns: [
                                        {
                                            xtype: 'treecolumn',
                                            dataIndex: 'text',
                                            text: 'Nominativo',
                                            flex: 1
                                        }
                                    ],
                                    listeners: {
                                        checkchange: {
                                            fn: me.onCcpBollettiniStudentsCheckChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            id: 'CcpBollettiniSchoolYear',
                            itemId: 'CcpBollettiniSchoolYear',
                            fieldLabel: 'Label',
                            name: 'school_year'
                        }
                    ],
                    listeners: {
                        validitychange: {
                            fn: me.onCcpBollettiniFrmValidityChange,
                            scope: me
                        }
                    }
                }
            ],
            listeners: {
                activate: {
                    fn: me.onCcpBollettiniPrintWinActivate,
                    scope: me
                },
                boxready: {
                    fn: me.onCcpBollettiniPrintWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('CcpBollettiniPrintWin').createBollettino();
    },

    onCcpBollettiniModelSelect: function(combo, records, eOpts) {
        Ext.getCmp('CcpBollSettPnl').show();
        if(records[0].get('name') == 'Pvr') {
            Ext.getCmp('CcpBollSettPnl').hide();
        }
    },

    onCcpBollettiniStudentsCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('CcpBollettiniPrintWin').enablePrint();
    },

    onCcpBollettiniFrmValidityChange: function(basic, valid, eOpts) {
        var students = Ext.getCmp('CcpBollettiniStudents').getChecked();

        if (students.length > 0 && valid) {
            Ext.getCmp('CcpBollettiniPrintBtn').setDisabled(false);
        } else {
            Ext.getCmp('CcpBollettiniPrintBtn').setDisabled(true);
        }
    },

    onCcpBollettiniPrintWinActivate: function(window, eOpts) {
        var today = new Date();

        if (today.getMonth() + 1 >= 9) {
            Ext.getCmp('CcpBollettiniSchoolYear').setValue(today.getFullYear() + '/' + (today.getFullYear() + 1));
        } else {
            Ext.getCmp('CcpBollettiniSchoolYear').setValue((today.getFullYear() - 1) + '/' + today.getFullYear());
        }
    },

    onCcpBollettiniPrintWinBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('CcpStudentsTree').load();

        var t = Ext.getCmp('CcpBollettiniStudents');

        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });
    },

    printBollettini: function(rec) {
        Ext.getCmp('CcpBollettiniPrintWin').setLoading();

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params: rec,
            success: function(response, opts) {
                Ext.getCmp('CcpBollettiniPrintWin').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });
    },

    createBollettino: function() {
        var rec = Ext.getCmp('CcpBollettiniFrm').getData(),
            template = Ext.decode(rec.printSettings).template;

        rec.newSpool = 1;

        if(template === 'Pvr' || template === 'QrBill') rec.print = template;
        else rec.print = 'Bollettino';
        rec.namespace = 'CCP';
        rec.type = 'PDF';
        rec.mime = 'application/pdf';

        Ext.getCmp('CcpBollettiniPrintWin').printBollettini(rec);
    },

    enablePrint: function() {
        var students = Ext.getCmp('CcpBollettiniStudents').getChecked(),
            form = Ext.getCmp('CcpBollettiniFrm').getForm();

        if (students.length > 0 && form.isValid()) {
            Ext.getCmp('CcpBollettiniPrintBtn').setDisabled(false);
        } else {
            Ext.getCmp('CcpBollettiniPrintBtn').setDisabled(true);
        }
    }

});