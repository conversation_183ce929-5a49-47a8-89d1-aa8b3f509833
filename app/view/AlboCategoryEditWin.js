/*
 * File: app/view/AlboCategoryEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboCategoryEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboCategoryEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.TextArea',
        'Ext.form.field.ComboBox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 250,
    id: 'AlboCategoryEditWin',
    itemId: 'AlboCategoryEditWin',
    width: 400,
    title: 'Categoria',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'AlboCategoryEditForm',
                    itemId: 'AlboCategoryEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'AlboCategoryEditId',
                            itemId: 'AlboCategoryEditId',
                            name: 'id'
                        },
                        {
                            xtype: 'textfield',
                            id: 'AlboCategoryEditName',
                            itemId: 'AlboCategoryEditName',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            name: 'name',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            id: 'AlboCategoryEditDescription',
                            itemId: 'AlboCategoryEditDescription',
                            fieldLabel: 'Descrizione',
                            labelAlign: 'right',
                            name: 'description'
                        },
                        {
                            xtype: 'combobox',
                            id: 'AlboCategoryEditDuration',
                            itemId: 'AlboCategoryEditDuration',
                            fieldLabel: 'Durata (giorni)',
                            labelAlign: 'right',
                            name: 'duration',
                            value: 15,
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            editable: false,
                            displayField: 'duration',
                            forceSelection: true,
                            store: 'AlboCategoryDurations',
                            valueField: 'duration'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('AlboCategories'),
                                            form = Ext.getCmp('AlboCategoryEditForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvata';

                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornata';
                                            record = store.getById(parseInt(values.id));
                                            record.set('name', values.name);
                                            record.set('description', values.description);
                                            record.set('duration', values.duration);
                                        } else {
                                            store.add({
                                                name: values.name,
                                                description: values.description,
                                                duration: values.duration
                                            });
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('AlboCategoryEditWin').close();
                                                Ext.Msg.alert('Successo', 'Categoria ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Categoria NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});