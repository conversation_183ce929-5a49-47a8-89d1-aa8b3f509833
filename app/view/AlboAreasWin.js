/*
 * File: app/view/AlboAreasWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboAreasWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboAreasWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 400,
    id: 'AlboAreasWin',
    itemId: 'AlboAreasWin',
    width: 700,
    title: 'Aree',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'AlboAreasGrid',
                    itemId: 'AlboAreasGrid',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'AlboAreas',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            hideable: false,
                            text: 'Descrizione',
                            flex: 1
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            permissible: true,
                            dock: 'top',
                            id: 'AlboAreasToolbar',
                            itemId: 'AlboAreasToolbar',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('AlboAreaEditWin').show();
                                    },
                                    id: 'AlboAreaNewBtn',
                                    itemId: 'AlboAreaNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuova'
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onAlboAreasGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'AlboAreaEditMn',
                    itemId: 'AlboAreaEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('AlboAreasGrid').getSelectionModel().getSelection()[0];

                                Ext.widget('AlboAreaEditWin').show();

                                Ext.getCmp('AlboAreaEditForm').getForm().loadRecord(record);
                            },
                            id: 'contextAlboAreaEdit',
                            itemId: 'contextAlboAreaEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('AlboAreasGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questa Area?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('AlboAreas');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Area eliminata');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Area NON eliminata');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextAlboAreaDelete',
                            itemId: 'contextAlboAreaDelete',
                            iconCls: 'icon-delete',
                            text: 'Elimina'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onAlboAreasGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('AlboAreaEditMn').showAt([newX,newY]);

        if (record.get('locked')) {
            Ext.getCmp('contextAlboAreaDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextAlboAreaDelete').setDisabled(false);
        }
    }

});