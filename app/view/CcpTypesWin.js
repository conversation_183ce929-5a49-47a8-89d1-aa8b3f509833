/*
 * File: app/view/CcpTypesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpTypesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpTypesWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Spacer',
        'Ext.form.field.ComboBox',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.grid.column.Number',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.selection.CheckboxModel',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    permissible: true,
    height: 490,
    id: 'CcpTypesWin',
    itemId: 'CcpTypesWin',
    minHeight: 400,
    minWidth: 600,
    width: 744,
    resizable: false,
    title: 'Tipi di movimento',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'CcpTypesGrid',
                    itemId: 'CcpTypesGrid',
                    header: false,
                    emptyText: 'Nessun Tipo Movimento presente.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'CcpTypes',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getStore('CcpLinkedAdditionalsForm').removeAll();
                                        Ext.widget('CcpTypeEditWin').show();
                                        Ext.getStore('CcpTypeSteps').load();
                                    },
                                    id: 'CcpTypeNewBtn',
                                    itemId: 'CcpTypeNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'container',
                                    hidden: true,
                                    id: 'CcpTypeCopyToYearCnt',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'CcpTypeCopyCmb',
                                            emptyText: 'Anno su cui copiare ...',
                                            editable: false,
                                            displayField: 'value',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'CcpTypeSchoolYears',
                                            valueField: 'value'
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                var ids = [];

                                                Ext.each(Ext.getCmp('CcpTypesGrid').getSelectionModel().getSelection(), function(val) {
                                                    ids.push(val.get('id'));
                                                });

                                                Ext.Ajax.request({
                                                    url: '/mc2-api/ccp/type_copy',
                                                    params: {
                                                        to_year: Ext.getCmp('CcpTypeCopyCmb').getValue(),
                                                        'ids[]' : ids
                                                    },
                                                    success: function(response) {
                                                        var res = Ext.decode(response.responseText);
                                                        if(res.success=== true) {
                                                            Ext.getCmp('CcpTypeCopytoYearBtn').setText('Copia su anno');
                                                            Ext.getCmp('CcpTypeNewBtn').enable();
                                                            Ext.getCmp('CcpTypesGrid').headerCt.items.getAt(0).hide();
                                                            Ext.getCmp('CcpTypesGrid').getSelectionModel().deselectAll();
                                                            Ext.getCmp('CcpTypeCopyToYearCnt').hide();
                                                            Ext.getCmp('CcpTypeCopyCmb').setValue();

                                                            Ext.getStore('CcpTypes').load();
                                                        } else {
                                                            Ext.Msg.alert('ERRORE', res.message);
                                                        }
                                                    }
                                                });
                                            },
                                            text: 'Copia'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {

                                        if(button.getText() === 'Copia su anno') {
                                            Ext.getCmp('CcpTypeNewBtn').disable();
                                            Ext.getCmp('CcpTypesGrid').headerCt.items.getAt(0).show();
                                            button.setText('Annulla');
                                            Ext.getCmp('CcpTypeCopyToYearCnt').show();
                                            Ext.getCmp('CcpTypeCopyToYearCnt').disable();
                                        } else {
                                            button.setText('Copia su anno');
                                            Ext.getCmp('CcpTypeNewBtn').enable();
                                            Ext.getCmp('CcpTypesGrid').headerCt.items.getAt(0).hide();
                                            Ext.getCmp('CcpTypesGrid').getSelectionModel().deselectAll();
                                            Ext.getCmp('CcpTypeCopyToYearCnt').hide();
                                            Ext.getCmp('CcpTypeCopyCmb').setValue();
                                        }
                                    },
                                    id: 'CcpTypeCopytoYearBtn',
                                    text: 'Copia su anno'
                                }
                            ]
                        }
                    ],
                    viewConfig: {
                        listeners: {
                            itemdblclick: {
                                fn: me.onViewItemDblClick,
                                scope: me
                            }
                        }
                    },
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            width: 180,
                            dataIndex: 'category_text',
                            hideable: false,
                            text: 'Categoria'
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (!r.get('incoming')) {
                                            return 'icon-basket_remove';
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (!r.get('incoming')) {
                                            return 'In Uscita';
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 99,
                            align: 'center',
                            dataIndex: 'ae_category',
                            text: 'Agenzia Entrate'
                        },
                        {
                            xtype: 'numbercolumn',
                            draggable: false,
                            hidden: true,
                            width: 110,
                            resizable: false,
                            align: 'right',
                            dataIndex: 'amount',
                            hideable: false,
                            text: 'Importo base '
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            resizable: false,
                            hideable: false,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('linked_additionals').length > 0) {
                                            return 'icon-money_add';
                                        }
                                    },
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        if (record.get('linked_additionals').length > 0) {
                                            var s = Ext.getStore('CcpLinkedAdditionals');
                                            s.removeAll();
                                            s.load({
                                                params: {
                                                    type: 'T',
                                                    item: record.get('id')
                                                },
                                                callback: function(records, operation, success) {
                                                    if (success) {
                                                        Ext.widget('CcpLinkedAdditionalsWin').show();
                                                        Ext.getCmp('CcpLinkedAdditionalsWin').setTitle('Addizionali abbinate al Tipo Movimento "' + record.get('name') + '"');
                                                    } else {
                                                        Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al tipo movimento fallito.');
                                                    }
                                                }
                                            });
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var additionals = r.get('linked_additionals').length,
                                            suffix = 'e',
                                            msg = 'Comprende ' + additionals + ' addizional';

                                        if (additionals > 0) {
                                            if (additionals >= 2) {
                                                suffix = 'i';
                                            }
                                            return msg + suffix;
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onCcpTypesGridItemContextMenu,
                            scope: me
                        },
                        afterrender: {
                            fn: me.onCcpTypesGridAfterRender,
                            scope: me
                        }
                    },
                    features: [
                        {
                            ftype: 'grouping',
                            enableGroupingMenu: false,
                            groupHeaderTpl: [
                                'Anno Scolastico: {name} ({children.length})'
                            ]
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true,
                        listeners: {
                            selectionchange: {
                                fn: me.onCheckboxModelSelectionChange,
                                scope: me
                            }
                        }
                    })
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    id: 'CcpTypeEditMn',
                    itemId: 'CcpTypeEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('CcpTypesGrid').getSelectionModel().getSelection()[0],
                                    s = Ext.getStore('CcpLinkedAdditionalsForm'),
                                    typeStepStore = Ext.getStore('CcpTypeSteps');

                                if(r.get('incoming') === true) r.data.incoming = 'on';
                                else if (r.get('incoming') === false) r.data.incoming = 'off';

                                s.removeAll();
                                s.load({
                                    params: {
                                        type: 'T',
                                        item: r.get('id')
                                    },
                                    callback: function(records, operation, success) {
                                        if (success) {

                                            Ext.widget('CcpTypeEditWin').show();
                                            Ext.getCmp('CcpTypeEditForm').getForm().loadRecord(r);


                                            if(r.get('payment_mail')) {
                                                Ext.getCmp('CcpTypePaymentMail').setValue('on');
                                            }

                                            if(r.get('section')) Ext.getCmp('CcpTypeSectionCmb').setValue(r.get('section'));
                                            else Ext.getCmp('CcpTypeSectionCmb').setValue('');

                                            if(r.get('vat_code_id')) {
                                                // Non esiste l'id quindi bloccata tutto da qui in poi
                                                //Ext.getCmp('CcpTypeExemption').setValue('on');
                                            }

                                            // Se è un tipo movimento utilizzato,
                                            // disabilito i campi che non si possono modificare
                                            if(r.get('locked')) {
                                                Ext.getCmp('CcpTypeEditableCnt').setDisabled(true);
                                                Ext.getCmp('CcpTypeContainerIvaCnt').setDisabled(true);
                                            }




                                        } else {
                                            Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al tipo movimento fallita.');
                                        }
                                    }
                                });

                                typeStepStore.load({
                                    params: {
                                        ccp_type: r.get('id')
                                    }
                                });


                            },
                            id: 'contextCcpTypeEdit',
                            itemId: 'contextCcpTypeEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('CcpTypesGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questo Tipo di Movimento?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            sTypes = Ext.getStore('CcpTypes');
                                            sAdds = Ext.getStore('CcpAdditionals');
                                            sCats = Ext.getStore('CcpCategories');
                                            sTypes.remove(record);
                                            sTypes.sync({
                                                callback: function () {
                                                    sTypes.load();
                                                    sAdds.load();
                                                    sCats.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Tipo di Movimento eliminato');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Tipo di Movimento NON eliminato');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextCcpTypeDelete',
                            itemId: 'contextCcpTypeDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpTypesWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onViewItemDblClick: function(dataview, record, item, index, e, eOpts) {
        Ext.getCmp('contextCcpTypeEdit').handler();
    },

    onCcpTypesGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        // Fixes the issue of the GROUPED grid that prevents the selection
        // of a record on right click
        dataview.getSelectionModel().select(record.index);

        e.stopEvent();
        var newX = e.xy[0],
            newY = e.xy[1],
            menu = Ext.getCmp('CcpTypeEditMn');
        menu.showAt([newX,newY]);

        // Already in use tax types cannot be deleted
        if (record.get('locked')) {
            //menu.items.get('contextCcpTypeEdit').setDisabled(true);
            menu.items.get('contextCcpTypeDelete').setDisabled(true);
        } else {
            //menu.items.get('contextCcpTypeEdit').setDisabled(false);
            menu.items.get('contextCcpTypeDelete').setDisabled(false);
        }
    },

    onCheckboxModelSelectionChange: function(model, selected, eOpts) {

        if(selected.length>0) {
            Ext.getCmp('CcpTypeCopyToYearCnt').enable();
        } else {
            Ext.getCmp('CcpTypeCopyToYearCnt').disable();

        }
    },

    onCcpTypesGridAfterRender: function(component, eOpts) {

        Ext.getCmp('CcpTypesGrid').headerCt.items.getAt(0).hide();
    },

    onCcpTypesWinShow: function(component, eOpts) {
        Ext.getStore('CcpTypes').load();
    }

});