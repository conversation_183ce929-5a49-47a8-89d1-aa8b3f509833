/*
 * File: app/view/CcpMovementCopyWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpMovementCopyWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpMovementCopyWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.tree.Panel',
        'Ext.tree.View'
    ],

    height: 520,
    id: 'CcpMovementCopyWin',
    width: 400,
    resizable: false,
    title: 'Copia movimento',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            movement: null,
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            id: 'CcpMovementCopyCopyBtn',
                            iconCls: 'icon-page_copy',
                            text: 'Copia',
                            listeners: {
                                click: {
                                    fn: me.onCcpMovementCopyCopyBtnClick,
                                    buffer: 500,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onCcpBollettiniPrintWinBoxReady,
                    scope: me
                }
            },
            items: [
                {
                    xtype: 'treepanel',
                    flex: 1,
                    id: 'CcpMovementCopy2Students',
                    title: '',
                    store: 'CcpStudentsTree',
                    viewConfig: {

                    }
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpMovementCopyCopyBtnClick: function(button, e, eOpts) {
        var movement = Ext.getCmp('CcpMovementCopyWin').movement,
            students = Ext.getCmp('CcpMovementCopy2Students').getChecked(),
            sMovs = Ext.getStore('CcpMovements'),
            sAdds = Ext.getStore('CcpAdditionals'),
            sTypes = Ext.getStore('CcpTypes');

        movement.remaining = movement.id;
        delete(movement.id);

        // Adds new records
        Ext.each(students, function(s) {
            r = movement;
            r.number = movement.number !== null ? 0 : null;
            r.subject_id = s.get('db_id');
            r.subject_seat = s.get('seat_id');
            r.subject_class = s.get('class') + s.get('section');
            r.subject_data = s.get('text');
            r.linked_additionals = [];
            sMovs.add(r);
        });

        // Syncs the record
        sMovs.sync({
            callback: function() {
                sMovs.load();
                sAdds.load();
                sTypes.load();
            },
            success: function(form, action) {
                Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();
                Ext.getCmp('CcpMovementCopyWin').close();
                Ext.Msg.alert('Successo', 'Movimento copiato');
            },
            failure: function(form, action) {
                Ext.Msg.alert('Attenzione', 'Movimento NON copiato');
            }
        });
    },

    onCcpBollettiniPrintWinBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('CcpStudentsTree').load();

        Ext.getCmp('CcpMovementCopyStudents').getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });
    }

});