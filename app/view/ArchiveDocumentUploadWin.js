/*
 * File: app/view/ArchiveDocumentUploadWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDocumentUploadWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDocumentUploadWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.FieldSet',
        'Ext.form.field.File',
        'Ext.Img',
        'Ext.form.Label',
        'Ext.form.field.ComboBox',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.field.TextArea',
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.form.field.Hidden',
        'Ext.form.field.Date'
    ],

    id: 'ArchiveDocumentUploadWin',
    itemId: 'ArchiveDocumentUploadWin',
    width: 500,
    resizable: false,
    title: 'Carica Documento',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'ArchiveUploadForm',
                    itemId: 'ArchiveUploadForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    url: '/mc2-api/archive/document',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var form = Ext.getCmp('ArchiveUploadForm').getForm(),
                                            metaStore = Ext.getStore('ArchiveClassMetadata'),
                                            meta = [],
                                            params = form.getValues(),
                                            win = Ext.getCmp('ArchiveDocumentUploadWin'),
                                            c = Ext.getCmp('ArchiveUploadFileCnt'),
                                            assignRecord = Ext.getCmp('ArchiveUploadClass').record;

                                        for (i=0; i< c.items.getCount(); ++i) {
                                            var comp = c.items.get(i);
                                            if(!comp.getValue()){
                                                comp.destroy();
                                            }
                                        }

                                        delete params.class_id;

                                        switch(assignRecord.get('type')) {
                                            case 'F':
                                            Ext.getCmp('ArchiveUploadClass').name = 'class_id';
                                            break;
                                            case 'U':
                                            Ext.getCmp('ArchiveUploadClass').name = 'assign_to_user';
                                            break;
                                            case 'O':
                                            Ext.getCmp('ArchiveUploadClass').name = 'assign_to_office';
                                            break;
                                        }

                                        metaStore.each(function (metadata) {
                                            meta.push(metadata.getData());
                                        });

                                        if(!params.class_id) {
                                            delete params.class_id;
                                        }
                                        if(!params.assign_to_user) {
                                            delete params.assign_to_user;
                                        }
                                        if(!params.assign_to_office) {
                                            delete params.assign_to_office;
                                        }

                                        if (form.isValid()) {
                                            Ext.getCmp('ArchiveUploadClass').show();
                                            form.submit({
                                                url: form.url,
                                                params: params,
                                                waitMsg: 'Archiviazione file...',
                                                success: function(fp, o) {

                                                    Ext.getStore('ArchiveDocumentsArchived').load();
                                                    Ext.getStore('ArchiveDocumentsUser').load();
                                                    Ext.getStore('ArchiveDocumentsOffice').load();

                                                    Ext.Msg.show({
                                                        title: 'Successo',
                                                        msg: 'Flusso caricato nella sezione "In corso".<br /><br />Archiviare un nuovo Documento?',
                                                        buttons: Ext.Msg.YESNO,
                                                        fn: function(r) {
                                                            win.close();
                                                            if (r == 'yes') {
                                                                Ext.widget('ArchiveDocumentUploadWin').show();
                                                            }
                                                        }
                                                    });

                                                    if (Ext.getCmp('ArchiveUploadOriginId').getValue() == 2){
                                                        Ext.getCmp('ArchiveMailPnl').getStore().load();
                                                    }
                                                },
                                                failure: function(form, action) {
                                                    var msg = 'Comunicazione col server non riuscita';
                                                    if (action.result.message !== '') {
                                                        msg = action.result.message;
                                                    }
                                                    Ext.Msg.alert('Attenzione', msg);
                                                }
                                            });
                                        }
                                    },
                                    id: 'ArchiveUploadDocumentBtn',
                                    itemId: 'ArchiveUploadDocumentBtn',
                                    iconCls: 'icon-database',
                                    text: 'Conferma'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('ArchiveDocumentUploadWin').uploadDocument('protocol');
                                    },
                                    hidden: true,
                                    id: 'ArchiveProtocolBtn',
                                    itemId: 'ArchiveProtocolBtn',
                                    iconCls: 'icon-page_portrait',
                                    text: 'Protocolla'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'fieldset',
                            hidden: true,
                            id: 'ArchiveAttachmentCnt',
                            title: 'Documenti',
                            items: [
                                {
                                    xtype: 'checkboxgroup',
                                    id: 'ArhiveCheckBoxGroupAttachment',
                                    fieldLabel: '',
                                    layout: {
                                        type: 'vbox',
                                        pack: 'center'
                                    },
                                    listeners: {
                                        render: {
                                            fn: me.onCheckboxgroupRender,
                                            scope: me
                                        }
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            name: 'attachment_message',
                                            boxLabel: 'Testo della mail'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            cleanFileFields: function() {
                                var c = Ext.getCmp('ArchiveUploadFileCnt');
                                for (i=0; i<c.items.getCount(); ++i) {
                                    var comp = c.items.get(i);
                                    if(!comp.getValue()){
                                        comp.destroy();
                                    }
                                }

                                var f = Ext.create('Ext.form.field.File', {
                                    name: 'file[]',
                                    buttonText:'Sfoglia...',
                                    fieldLabel: 'Documento*',
                                    labelAlign: 'right',
                                    labelWidth:120,
                                    listeners: {
                                        change: function(){
                                            Ext.getCmp('ArchiveUploadFileCnt').cleanFileFields();

                                            var c = Ext.getCmp('ArchiveUploadFileCnt'),
                                                shortDescription = [],
                                                comp,
                                                val;

                                            for (i=0; i<c.items.getCount(); ++i) {
                                                comp = c.items.get(i);
                                                val = comp.getValue();
                                                if (val) {
                                                    shortDescription.push(val.replace(/C:\\fakepath\\/g, ''));
                                                }
                                            }
                                            Ext.getCmp('ArchiveUploadShortDescription').setValue(shortDescription.join(', '));
                                        }
                                    }
                                });

                                Ext.getCmp('ArchiveUploadFileCnt').add(f);
                            },
                            flex: 1,
                            id: 'ArchiveUploadFileCnt',
                            itemId: 'ArchiveUploadFileCnt',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'filefield',
                                    id: 'ArchiveUploadFile',
                                    itemId: 'ArchiveUploadFile',
                                    fieldLabel: 'Documento*',
                                    labelAlign: 'right',
                                    labelWidth: 120,
                                    name: 'file[]',
                                    buttonMargin: 10,
                                    buttonText: 'Sfoglia...',
                                    listeners: {
                                        change: {
                                            fn: me.onArchiveUploadFileChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            margin: '10 0 10 0',
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'image',
                                    height: 16,
                                    hidden: true,
                                    id: 'ArchiveUploadActionIconL',
                                    itemId: 'ArchiveUploadActionIconL',
                                    style: 'background-repeat: none;',
                                    width: 16,
                                    imgCls: 'icon-key'
                                },
                                {
                                    xtype: 'label',
                                    id: 'ArchiveUploadActionLabel',
                                    itemId: 'ArchiveUploadActionLabel',
                                    margin: '0 10 0 10'
                                },
                                {
                                    xtype: 'image',
                                    height: 16,
                                    hidden: true,
                                    id: 'ArchiveUploadActionIconR',
                                    itemId: 'ArchiveUploadActionIconR',
                                    style: 'background-repeat: none;',
                                    width: 16,
                                    imgCls: 'icon-key'
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            id: 'ArchiveUploadClass',
                            itemId: 'ArchiveUploadClass',
                            fieldLabel: 'Assegnato a',
                            labelAlign: 'right',
                            labelWidth: 120,
                            editable: false,
                            displayField: 'name',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'Assignees',
                            valueField: 'id',
                            listeners: {
                                change: {
                                    fn: me.onComboboxChange,
                                    scope: me
                                },
                                select: {
                                    fn: me.onArchiveUploadClassSelect,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'radiogroup',
                            hidden: true,
                            id: 'ArchiveUploadDestination',
                            itemId: 'ArchiveUploadDestination',
                            margin: '10 0 0 0',
                            fieldLabel: 'Destinazione',
                            labelWidth: 120,
                            items: [
                                {
                                    xtype: 'radiofield',
                                    name: 'destination',
                                    boxLabel: 'Locale',
                                    checked: true,
                                    inputValue: 'L'
                                },
                                {
                                    xtype: 'radiofield',
                                    name: 'destination',
                                    boxLabel: 'Cloud',
                                    inputValue: 'C'
                                }
                            ]
                        },
                        /*{
                            xtype: 'combobox',
                            fieldLabel: 'Modello',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'model',
                            displayField: 'name',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'ArchiveTemplates',
                            typeAhead: true,
                            valueField: 'id'
                        },*/
                        {
                            xtype: 'textfield',
                            id: 'ArchiveUploadShortDescription',
                            itemId: 'ArchiveUploadShortDescription',
                            fieldLabel: 'Breve descrizione',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'short_description',
                            allowBlank: false
                        },
                        {
                            xtype: 'textareafield',
                            id: 'ArchiveUploadDescription',
                            itemId: 'ArchiveUploadDescription',
                            fieldLabel: 'Note',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'description'
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            hidden: true,
                            id: 'ArchiveUploadMetadata',
                            itemId: 'ArchiveUploadMetadata',
                            collapsible: true,
                            title: 'Metadati per conservazione',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'ArchiveUploadMetadataGrid',
                                    itemId: 'ArchiveUploadMetadataGrid',
                                    margin: '0 0 10 0',
                                    minHeight: 130,
                                    emptyText: 'Nessun metadata associato al tipo di documento selezionato.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    sortableColumns: false,
                                    store: 'ArchiveClassMetadata',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                metaData.tdAttr = 'data-qtip="' + record.get('description') + '"';
                                                return value;
                                            },
                                            width: 150,
                                            align: 'right',
                                            dataIndex: 'name',
                                            text: 'Nome'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'value',
                                            text: 'Valore',
                                            flex: 1,
                                            editor: {
                                                xtype: 'textfield'
                                            }
                                        }
                                    ],
                                    plugins: [
                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                            listeners: {
                                                validateedit: {
                                                    fn: me.onRowEditingValidateedit,
                                                    scope: me
                                                }
                                            }
                                        })
                                    ],
                                    features: [
                                        {
                                            ftype: 'grouping',
                                            collapsible: false,
                                            enableGroupingMenu: false,
                                            enableNoGroups: false,
                                            groupHeaderTpl: [
                                                '{name}'
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ArchiveUploadUserId',
                            itemId: 'ArchiveUploadUserId',
                            name: 'archive_user_id'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ArchiveUploadReferenceYear',
                            itemId: 'ArchiveUploadReferenceYear',
                            name: 'reference_year'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ArchiveUploadOriginId',
                            itemId: 'ArchiveUploadOriginId',
                            name: 'origin_id',
                            value: 1
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            id: 'ArchiveUploadMailIdAccount',
                            fieldLabel: 'Label',
                            name: 'mail'
                        },
                        {
                            xtype: 'datefield',
                            flex: 1,
                            fieldLabel: 'Scadenza',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'expiration_date',
                            submitFormat: 'm/d/Y'
                        },
                        {
                            xtype: 'fieldset',
                            hidden: true,
                            title: 'Operazioni',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    id: 'ArchiveUploadActionProtocol',
                                    itemId: 'ArchiveUploadActionProtocol',
                                    name: 'action_protocol',
                                    boxLabel: 'Da protocollare',
                                    uncheckedValue: 'off'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    id: 'ArchiveUploadActionAlbo',
                                    itemId: 'ArchiveUploadActionAlbo',
                                    name: 'action_albo',
                                    boxLabel: 'Da pubblicare su Albo',
                                    uncheckedValue: 'off'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    id: 'ArchiveUploadActionTrasparenza',
                                    itemId: 'ArchiveUploadActionTrasparenza',
                                    name: 'action_trasparenza',
                                    boxLabel: 'Da allegare a Trasparenza',
                                    uncheckedValue: 'off'
                                }
                            ]
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            id: 'ArchiveUploadAssignFromUser',
                            itemId: 'ArchiveUploadAssignFromUser',
                            fieldLabel: 'Label',
                            name: 'assign_from_user'
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onArchiveUploadWinClose,
                    scope: me
                },
                show: {
                    fn: me.onArchiveDocumentUploadWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCheckboxgroupRender: function(component, eOpts) {
        /*var cg = Ext.getCmp('ArhiveCheckBoxGroupAttachment'),
            ch;


        Ext.each(Ext.getStore('ArchiveMailAttachments').getRange(), function(attachment){
            cg.add(Ext.form.field.Checkbox({
                boxLabel  : attachment.get('name'),
                name      : 'attachment_' + attachment.get('id'),
                inputValue: 'on',
                uncheckedValue: 'off',
                id        : 'checkbox_' + attachment.get('id')
            }));
        });*/
    },

    onArchiveUploadFileChange: function(filefield, value, eOpts) {
        Ext.getCmp('ArchiveUploadFileCnt').cleanFileFields();

        var c = Ext.getCmp('ArchiveUploadFileCnt'),
            shortDescription = [],
            comp,
            val;

        for (i=0; i<c.items.getCount(); ++i) {
            comp = c.items.get(i);
            val = comp.getValue();
            if (val) {
                shortDescription.push(val.replace(/C:\\fakepath\\/g, ''));
            }
        }
        Ext.getCmp('ArchiveUploadShortDescription').setValue(shortDescription.join(', '));
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {
        /*var metadataStore = Ext.getStore('ArchiveMetadata'),
            classStore = Ext.getStore('ArchiveClasses'),
            iconL = Ext.getCmp('ArchiveUploadActionIconL'),
            iconR = Ext.getCmp('ArchiveUploadActionIconR'),
            label = Ext.getCmp('ArchiveUploadActionLabel'),
            radio = Ext.getCmp('ArchiveUploadDestination'),
            year = Ext.getCmp('ArchiveUploadReferenceYear'),
            classAction = classStore.getById(newValue).get('action'),
            classFormat = classStore.getById(newValue).get('format');

        if (classFormat !== 'PDF') {
            radio.setValue({destination: 'L'});
            radio.items.items[0].enable();
            radio.items.items[1].disable();
        } else if (classAction === 'A' || classAction === 'N') {
            radio.setValue({destination: 'L'});
            radio.items.items[0].enable();
            radio.items.items[1].enable();
        } else {
            radio.setValue({destination: 'C'});
            radio.items.items[0].disable();
            radio.items.items[1].enable();
        }

        if (classAction === 'C') {
            iconL.show();
            iconR.show();
            //label.setText('Il documento verrà Archiviato e Conservato');
        } else if (classAction === 'A' || classAction === 'N') {
            iconL.hide();
            iconR.hide();
            //label.setText('Il documento verrà Archiviato');
        } else {
            iconL.hide();
            iconR.hide();
            //label.setText('Selezionare il tipo di documento da caricare');
        }

        year.setValue(new Date().getFullYear());

        */
    },

    onArchiveUploadClassSelect: function(combo, records, eOpts) {
        Ext.getCmp('ArchiveUploadClass').record = records[0];
    },

    onRowEditingValidateedit: function(editor, context, eOpts) {
        var //record = Ext.getCmp('ArchiveUploadMetadataGrid').getSelectionModel().getSelection()[0],
        isValid = true,
            record = context.record,
            newValue = context.newValues.value,
            oldValue = context.originalValues.value;

        // Check value against metadata type: DATE (yyyy-mm-dd), Integer (> 0), String (No checks).
        if (record.get('kind') === 'D') {
            var regex = /^(19|20)([0-9]{2})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
            if (!regex.test(newValue)) {
                context.cancel();
                isValid = false;
            }
        } else if (record.get('kind') === 'I') {
            if (parseInt(newValue) < 0) {
                context.cancel();
                isValid = false;
            }
        }

        // Check for mandatory
        if (!record.get('optional') && !newValue) {
            isValid = false;
        }

        return isValid;
    },

    onArchiveUploadWinClose: function(panel, eOpts) {
        var classmetaStore = Ext.getStore('ArchiveClassMetadata');
        classmetaStore.removeAll();
    },

    onArchiveDocumentUploadWinShow: function(component, eOpts) {
        var win = Ext.getCmp('ArchiveDocumentUploadWin');

        Ext.getStore('ArchiveClasses').load();

        if (win.archiveFrom === 'P') {
            Ext.getCmp('ArchiveUploadActionProtocol').setValue(true);
            Ext.getCmp('ArchiveUploadActionProtocol').readOnly = true;
        } else if (win.archiveFrom === 'A') {
            Ext.getCmp('ArchiveUploadActionAlbo').setValue(true);
            Ext.getCmp('ArchiveUploadActionAlbo').readOnly = true;
        } else if (win.archiveFrom === 'T') {
            Ext.getCmp('ArchiveUploadActionTrasparenza').setValue(true);
            Ext.getCmp('ArchiveUploadActionTrasparenza').readOnly = true;
        }


    },

    uploadDocument: function(next) {
        var form = Ext.getCmp('ArchiveUploadForm').getForm(),
            metaStore = Ext.getStore('ArchiveClassMetadata'),
            meta = [],
            params = form.getValues(),
            win = Ext.getCmp('ArchiveDocumentUploadWin'),
            c = Ext.getCmp('ArchiveUploadFileCnt'),
            assignRecord = Ext.getCmp('ArchiveUploadClass').record;

        for (i=0; i< c.items.getCount(); ++i) {
            var comp = c.items.get(i);
            if(!comp.getValue()){
                comp.destroy();
            }
        }

        delete params.class_id;

        if(assignRecord) {
            switch(assignRecord.get('type')) {
                case 'F':
                    Ext.getCmp('ArchiveUploadClass').name = 'class_id';
                    break;
                case 'U':
                    Ext.getCmp('ArchiveUploadClass').name = 'assign_to_user';
                    break;
                case 'O':
                    Ext.getCmp('ArchiveUploadClass').name = 'assign_to_office';
                    break;
            }
        }

        metaStore.each(function (metadata) {
            meta.push(metadata.getData());
        });

        if(!params.class_id) {
            delete params.class_id;
        }
        if(!params.assign_to_user) {
            delete params.assign_to_user;
        }
        if(!params.assign_to_office) {
            delete params.assign_to_office;
        }

        if (form.isValid()) {
            Ext.getCmp('ArchiveUploadClass').show();
            form.submit({
                url: form.url,
                params: params,
                waitMsg: 'Archiviazione file...',
                success: function(fp, o) {

                    Ext.getStore('ArchiveDocumentsArchived').load();
                    Ext.getStore('ArchiveDocumentsUser').load();
                    Ext.getStore('ArchiveDocumentsOffice').load();

                    if(next === 'protocol') {
                        var protocolWin = Ext.widget('ProtocolProtocolNewWin').show(),
                            response = Ext.decode(o.response.responseText),
                            record = Ext.create('mc2ui.model.ArchiveDocument', response.results);

                        protocolWin.fromDocument = false;
                        protocolWin.record = record;
                        Ext.getCmp('ProtocolNewDate').setValue(new Date());
                        Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
                        Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
                        Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
                        Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getView().disable();
                        Ext.getCmp('ProtocolDocumentToolBar').disable();
                        if (mc2ui.app.settings.areaArchive) {
                            Ext.getCmp('ProtocolAddCompleteBtn').show();
                        }

                        Ext.Ajax.request({
                            method: 'GET',
                            url: '/mc2-api/archive/document_file',
                            params:{
                                document: record.get('id')
                            },
                            success:function(res){
                                var r = Ext.decode(res.responseText);
                                Ext.getStore('ProtocolLinkedDocumentsForm').add(r.results);
                            }
                        });

                        Ext.getCmp('ArchivePnl').populateProtocol(record.get('model'));

                    } else {
                        Ext.Msg.show({
                            title: 'Successo',
                            msg: 'Flusso caricato nella sezione "In corso".<br /><br />Archiviare un nuovo Documento?',
                            buttons: Ext.Msg.YESNO,
                            fn: function(r) {
                                win.close();
                                if (r == 'yes') {
                                    Ext.widget('ArchiveDocumentUploadWin').show();
                                }
                            }
                        });
                    }


                    if (Ext.getCmp('ArchiveUploadOriginId').getValue() == 2){
                        Ext.getCmp('ArchiveMailPnl').getStore().load();
                    }
                },
                failure: function(form, action) {
                    var msg = 'Comunicazione col server non riuscita';
                    if (action.result.message !== '') {
                        msg = action.result.message;
                    }
                    Ext.Msg.alert('Attenzione', msg);
                }
            });
        }
    }

});