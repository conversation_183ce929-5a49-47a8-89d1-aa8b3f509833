/*
 * File: app/view/InstituteEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.InstituteEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.InstituteEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Hidden',
        'Ext.form.field.ComboBox'
    ],

    id: 'InstituteEditWin',
    itemId: 'InstituteEditWin',
    width: 496,
    resizable: false,
    title: 'Istituto',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'InstituteEditFrm',
                    itemId: 'InstituteEditFrm',
                    width: 396,
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default',
                        'x-box-layout-ct'
                    ],
                    bodyPadding: 10,
                    header: false,
                    url: '/mc2/applications/core/institutes/update.php',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'InstituteEditId',
                            itemId: 'InstituteEditId',
                            fieldLabel: 'Label',
                            inputId: 'institute_id'
                        },
                        {
                            xtype: 'textfield',
                            id: 'InstituteEditName',
                            itemId: 'InstituteEditName',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            name: 'name'
                        },
                        {
                            xtype: 'textfield',
                            id: 'InstituteEditMechanCode',
                            itemId: 'InstituteEditMechanCode',
                            fieldLabel: 'Meccanografico',
                            labelAlign: 'right',
                            name: 'mechan_code'
                        },
                        {
                            xtype: 'textfield',
                            id: 'InstituteEditAddress',
                            itemId: 'InstituteEditAddress',
                            fieldLabel: 'Indirizzo',
                            labelAlign: 'right',
                            name: 'address'
                        },
                        {
                            xtype: 'combobox',
                            id: 'InstituteEditCity',
                            itemId: 'InstituteEditCity',
                            fieldLabel: 'Città',
                            labelAlign: 'right',
                            name: 'city_id',
                            hideTrigger: true,
                            displayField: 'description',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'CoreCities',
                            typeAhead: true,
                            valueField: 'city_id'
                        },
                        {
                            xtype: 'textfield',
                            id: 'InstituteEditFiscalCode',
                            itemId: 'InstituteEditFiscalCode',
                            fieldLabel: 'C. F. / P. IVA',
                            labelAlign: 'right',
                            name: 'fiscal_code'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('InstituteEditFrm').getForm().submit({
            success : function(){
                Ext.getCmp('InstituteEditWin').close();
                Ext.getStore('Institutes').load();
            }
        });
    }

});