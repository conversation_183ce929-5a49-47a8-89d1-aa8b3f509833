/*
 * File: app/view/CcpAdditionalEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpAdditionalEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpAdditionalEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Number'
    ],

    id: 'CcpAdditionalEditWin',
    itemId: 'CcpAdditionalEditWin',
    width: 400,
    resizable: false,
    title: 'Addizionale',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'CcpAdditionalEditForm',
                    itemId: 'CcpAdditionalEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            id: 'CcpAdditionalEditName',
                            itemId: 'CcpAdditionalEditName',
                            fieldLabel: 'Nome',
                            name: 'name',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'radiogroup',
                            flex: 1,
                            hidden: true,
                            width: 400,
                            fieldLabel: 'Tipologia',
                            items: [
                                {
                                    xtype: 'radiofield',
                                    name: 'positive',
                                    boxLabel: 'Addizionale'
                                },
                                {
                                    xtype: 'radiofield',
                                    name: 'positive',
                                    boxLabel: 'Deduzione',
                                    checked: true,
                                    inputValue: 'off'
                                }
                            ]
                        },
                        {
                            xtype: 'radiogroup',
                            flex: 1,
                            width: 400,
                            fieldLabel: 'Calcolo',
                            items: [
                                {
                                    xtype: 'radiofield',
                                    name: 'percentual',
                                    boxLabel: 'Percentuale',
                                    checked: true
                                },
                                {
                                    xtype: 'radiofield',
                                    name: 'percentual',
                                    boxLabel: 'Valore assoluto',
                                    inputValue: 'off'
                                }
                            ]
                        },
                        {
                            xtype: 'radiogroup',
                            flex: 1,
                            hidden: true,
                            width: 400,
                            fieldLabel: 'Visibile in',
                            items: [
                                {
                                    xtype: 'radiofield',
                                    name: 'payment',
                                    boxLabel: 'Movimenti',
                                    checked: true,
                                    inputValue: 'off'
                                },
                                {
                                    xtype: 'radiofield',
                                    name: 'payment',
                                    boxLabel: 'Pagamenti'
                                }
                            ]
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            id: 'CcpAdditionalEditId',
                            itemId: 'CcpAdditionalEditId',
                            fieldLabel: 'Label',
                            name: 'id'
                        },
                        {
                            xtype: 'numberfield',
                            flex: 1,
                            fieldLabel: 'Valore predefinito',
                            labelWidth: 150,
                            name: 'amount',
                            hideTrigger: true
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('CcpAdditionals'),
                                            form = Ext.getCmp('CcpAdditionalEditForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvata';

                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornata';
                                            record = store.getById(parseInt(values.id));
                                            record.set('name', values.name);
                                            record.set('positive', values.positive === 'on');
                                            record.set('percentual', values.percentual === 'on');
                                            record.set('payment', values.payment === 'on');
                                            record.set('amount', values.amount);
                                        } else {
                                            store.add({
                                                name: values.name,
                                                amount: values.amount,
                                                positive: values.positive === 'on',
                                                percentual: values.percentual === 'on',
                                                payment: values.payment === 'on'
                                            });
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load({params: {type: 'D'}});
                                            },
                                            success: function(batch, action) {
                                                var res = Ext.decode(batch.operations[0].response.responseText);
                                                if (Ext.getCmp('CcpAdditionalEditWin').createLink) {
                                                    Ext.getStore('CcpLinkedAdditionalsForm').add({
                                                        additional_id: res.results.id,
                                                        item_id: 0,
                                                        name: values.name,
                                                        positive: values.positive === 'on',
                                                        percentual: values.percentual === 'on',
                                                        payment: values.payment === 'on',
                                                        amount: 0.00
                                                    });
                                                }
                                                Ext.getCmp('CcpAdditionalEditWin').close();
                                                Ext.getCmp('CcpAdditionalsGrid').getSelectionModel().deselectAll();
                                                Ext.Msg.alert('Successo', 'Addizionale ' + a);
                                            },
                                            failure: function(batch, action) {
                                                Ext.Msg.alert('Attenzione', 'Addizionale NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpAdditionalEditWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCcpAdditionalEditWinShow: function(component, eOpts) {
        var win = Ext.getCmp('CcpAdditionalEditWin');

        if (win.createLink === 'T' || win.createLink === 'P') {
            if (win.createLink === 'P') {
                Ext.getCmp('CcpAdditionalEditPayment').setValue(true);
            }

            Ext.getCmp('CcpAdditionalEditPayment').readOnly = true;
        }
    }

});