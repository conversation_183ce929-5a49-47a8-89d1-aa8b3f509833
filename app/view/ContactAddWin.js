/*
 * File: app/view/ContactAddWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ContactAddWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ContactAddWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Text',
        'Ext.form.field.Hidden',
        'Ext.button.Button'
    ],

    height: 138,
    id: 'ContactAddWin',
    itemId: 'ContactAddWin',
    width: 375,
    layout: 'fit',
    title: 'Aggiungi nuovo contatto',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'ContactAddFrm',
                    itemId: 'ContactAddFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/core/contacts',
                    items: [
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            fieldLabel: 'Nome',
                            name: 'name'
                        },
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            fieldLabel: 'Email',
                            name: 'email',
                            allowBlank: false,
                            vtype: 'email'
                        },
                        {
                            xtype: 'hiddenfield',
                            anchor: '100%',
                            fieldLabel: 'Label',
                            name: 'contact_type_id',
                            value: 4
                        },
                        {
                            xtype: 'hiddenfield',
                            anchor: '100%',
                            id: 'ContactAddFromMail',
                            itemId: 'ContactAddFromMail',
                            fieldLabel: 'Label',
                            value: 0
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var form = Ext.getCmp('ContactAddFrm');

                                        if (form.isValid()) {
                                            form.submit({
                                                success: function(r, res) {
                                                    if (parseInt(Ext.getCmp('ContactAddFromMail').getValue()) > 0) {
                                                        Ext.getCmp('ContactAddWin').close();
                                                        Ext.getStore('ArchiveMailSendContact').add(res.result.results);
                                                    }
                                                },
                                                failure: function(r1,r) {
                                                    Ext.Msg.alert('ATTENZIONE', r.result.message);
                                                }
                                            });
                                        }
                                    },
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});