/*
 * File: app/view/RegisterDetailWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.RegisterDetailWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.RegisterDetailWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.FieldSet',
        'Ext.form.field.Date',
        'Ext.form.field.Number',
        'Ext.button.Button'
    ],

    height: 324,
    id: 'RegisterDetailWin',
    itemId: 'RegisterDetailWin',
    width: 710,
    layout: 'fit',
    title: 'Dettaglio',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'RegisterDetailForm',
                    itemId: 'RegisterDetailForm',
                    layout: 'hbox',
                    bodyPadding: 10,
                    title: '',
                    items: [
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            id: 'ProtocolRegisterId',
                            itemId: 'ProtocolRegisterId',
                            fieldLabel: 'Label',
                            name: 'id'
                        },
                        {
                            xtype: 'fieldset',
                            id: 'RegisterIdentifyFs',
                            itemId: 'RegisterIdentifyFs',
                            margin: '0 5 0 0',
                            width: 276,
                            title: 'Dati indentificazione',
                            items: [
                                {
                                    xtype: 'textfield',
                                    id: 'RegisterNumber',
                                    fieldLabel: 'Numero',
                                    name: 'register_number',
                                    readOnly: true
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    id: 'RegisterCode',
                                    fieldLabel: 'Codice',
                                    name: 'code',
                                    readOnly: true
                                },
                                {
                                    xtype: 'datefield',
                                    anchor: '100%',
                                    id: 'RegisterDateFs',
                                    fieldLabel: 'Data',
                                    name: 'close_date',
                                    readOnly: true,
                                    editable: false,
                                    hideTrigger: true,
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Scuola',
                                    name: 'school_name',
                                    readOnly: true
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Codice IPA',
                                    name: 'ipa_code',
                                    readOnly: true
                                },
                                {
                                    xtype: 'numberfield',
                                    anchor: '100%',
                                    fieldLabel: 'Numero da',
                                    name: 'first_registration_number',
                                    readOnly: true,
                                    editable: false,
                                    hideTrigger: true
                                },
                                {
                                    xtype: 'numberfield',
                                    anchor: '100%',
                                    fieldLabel: 'Numero a',
                                    name: 'last_registration_number',
                                    readOnly: true,
                                    editable: false,
                                    hideTrigger: true
                                },
                                {
                                    xtype: 'datefield',
                                    anchor: '100%',
                                    fieldLabel: 'Data da',
                                    name: 'first_registration_date',
                                    readOnly: true,
                                    editable: false,
                                    hideTrigger: true,
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'datefield',
                                    anchor: '100%',
                                    fieldLabel: 'Data a',
                                    name: 'last_registration_date',
                                    readOnly: true,
                                    editable: false,
                                    hideTrigger: true,
                                    format: 'd/m/Y'
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            height: 164,
                            id: 'RegisterCreationFs',
                            itemId: 'RegisterCreationFs',
                            title: 'Dati creazione',
                            items: [
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Utente',
                                    name: 'creator_person',
                                    readOnly: true
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Responsabile',
                                    name: 'responsible',
                                    readOnly: true
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Software',
                                    name: 'creator_software',
                                    readOnly: true
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Impronta documento',
                                    name: 'hash',
                                    readOnly: true
                                },
                                {
                                    xtype: 'container',
                                    layout: {
                                        type: 'hbox',
                                        align: 'middle',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                var id = Ext.getCmp('ProtocolRegisterId').getValue();
                                                window.open('/mc2-api/protocol/register/' + id + '/download','_blank');
                                            },
                                            text: 'Scarica file',
                                            listeners: {
                                                render: {
                                                    fn: me.onButtonRender,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonRender: function(component, eOpts) {
        if(!mc2ui.app.settings.canManageReservedProtocol) {
            component.disable();

        }
    },

    showDetail: function(id) {

        Ext.Ajax.request({
            url: '/mc2-api/protocol/register/' + id,
            success: function(res){
                var r = Ext.decode(res.responseText);
                if (r.success === true){
                    r.results.close_date = new Date(r.results.close_date);
                    r.results.first_registration_date = new Date(r.results.first_registration_date);
                    r.results.last_registration_date = new Date(r.results.last_registration_date);
                    Ext.getCmp('RegisterDetailForm').getForm().setValues(r.results);
                } else {

                }
            }
        });
    }

});