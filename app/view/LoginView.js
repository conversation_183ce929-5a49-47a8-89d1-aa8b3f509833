/*
 * File: app/view/LoginView.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.LoginView', {
    extend: 'Ext.container.Viewport',
    alias: 'widget.LoginView',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Text',
        'Ext.button.Button'
    ],

    id: 'LoginView',
    itemId: 'LoginView',
    style: 'background-image: url(resources/images/background.jpg)',
    layout: 'fit',

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    id: 'LoginCnt',
                    itemId: 'LoginCnt',
                    layout: {
                        type: 'vbox',
                        align: 'center',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'form',
                            height: 132,
                            id: 'LoginForm',
                            itemId: 'LoginForm',
                            style: 'box-shadow: 2px 2px 15px #000000',
                            width: 320,
                            bodyPadding: 10,
                            title: 'Mastercom 2',
                            titleAlign: 'center',
                            url: '/mc2/applications/core/login.php',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    id: 'LoginUsernameTxt',
                                    itemId: 'LoginUsernameTxt',
                                    fieldLabel: 'Nome utente',
                                    labelAlign: 'right',
                                    name: 'username',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false
                                },
                                {
                                    xtype: 'textfield',
                                    id: 'LoginPasswordTxt',
                                    itemId: 'LoginPasswordTxt',
                                    fieldLabel: 'Password',
                                    labelAlign: 'right',
                                    name: 'password',
                                    inputType: 'password',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    enableKeyEvents: true,
                                    listeners: {
                                        keyup: {
                                            fn: me.onLoginPasswordTxtKeyup,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        var p = Ext.getCmp('LoginPasswordTxt');

                                        Ext.getCmp('LoginForm').submit({
                                            success: function (form, action) {
                                                if (action.result.auth === 0) {
                                                    Ext.MessageBox.show({
                                                        title: 'Login non riuscito',
                                                        msg: 'Username o password errati',
                                                        buttons: Ext.Msg.OK
                                                    });
                                                } else if (action.result.auth === 1) {
                                                    Ext.widget('LoginPasswordExpired').show();
                                                    Ext.getCmp('LoginPasswordExpired').user = action.result.uid;
                                                    p.reset();
                                                    Ext.getCmp('LoginPasswordExpired').force_mc2 = 0;
                                                    if (action.result.force_mc2 === 1) {
                                                        Ext.getCmp('LoginPasswordExpired').force_mc2 = 1;
                                                    }
                                                } else {
                                                    if (action.result.auth == 3) {
                                                        Ext.Msg.alert('ATTENZIONE', 'Al fine di migliorare la sicurezza e ottimizzare la gestione degli accessi, il Suo nome utente è stato modificato per garantirne l\'unicità. <br/><br/> Il Suo nuovo nome utente è: <b>' + action.result.username + '</b>. <br/>Si prega di utilizzare questo nuovo nome utente al prossimo accesso. La password attuale rimarrà invariata. <br/><br/> Grazie per la collaborazione ', function () {
                                                            //location.href = './';
                                                            Ext.getCmp('LoginForm').getForm().setValues({ 'username': action.result.username, 'password': p.getValue() });
                                                        });
                                                    } else {
                                                        location.href = './';
                                                    }

                                                }
                                            }
                                        });
                                    },
                                    formBind: true,
                                    id: 'LoginEnterBtn',
                                    iconCls: 'icon-key',
                                    text: 'Entra'
                                }
                            ],
                            listeners: {
                                afterrender: {
                                    fn: function (form, eOpts) {
                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/parameter',
                                            method: 'GET',
                                            params: {
                                                name: 'APP_VERSION'
                                            },
                                            success: function (response) {
                                                var res = Ext.decode(response.responseText);
                                                if (res.success && res.results && res.results.length > 0) {
                                                    form.setTitle('Mastercom 2 - v' + res.results[0].value);
                                                } else {
                                                    form.setTitle('Mastercom 2');
                                                }
                                            },
                                            failure: function () {
                                                form.setTitle('Mastercom 2');
                                            }
                                        });
                                    },
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onLoginPasswordTxtKeyup: function (textfield, e, eOpts) {
        if (e.getCharCode() == 13) {
            Ext.getCmp('LoginEnterBtn').handler();
        }

    }

});