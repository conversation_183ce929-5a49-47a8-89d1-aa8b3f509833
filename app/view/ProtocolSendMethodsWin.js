/*
 * File: app/view/ProtocolSendMethodsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolSendMethodsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolSendMethodsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 600,
    id: 'ProtocolSendMethodsWin',
    itemId: 'ProtocolSendMethodsWin',
    width: 700,
    resizable: false,
    title: '<PERSON><PERSON> di Invio',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    permissible: true,
                    flex: 1,
                    border: false,
                    id: 'ProtocolSendMethodsGrid',
                    itemId: 'ProtocolSendMethodsGrid',
                    header: false,
                    emptyText: 'Nessun Mezzo di Invio caricato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'ProtocolSendMethods',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'title',
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                allowBlank: false,
                                allowOnlyWhitespace: false
                            }
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onProtocolSendMethodsGridItemContextMenu,
                            scope: me
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ProtocolSendMethodEditWin').show();
                                    },
                                    id: 'ProtocolSendMethodNewBtn',
                                    itemId: 'ProtocolSendMethodNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'ProtocolSendMethodsEditMn',
                    itemId: 'ProtocolSendMethodsEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var pg = Ext.getCmp('ProtocolSendMethodsGrid'),
                                    record = pg.getSelectionModel().getSelection()[0];

                                Ext.widget('ProtocolSendMethodEditWin').show();

                                Ext.getCmp('ProtocolSendMethodEditForm').getForm().loadRecord(record);
                            },
                            id: 'contextProtocolSendMethodEdit',
                            itemId: 'contextProtocolSendMethodEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('ProtocolSendMethodsGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('denomination'),
                                    msg: 'Sei sicuro di voler eliminare questo Mezzo di Invio?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('ProtocolSendMethods');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Mezzo di Invio eliminato');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Mezzo di Invio NON eliminato');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextProtocolSendMethodDelete',
                            itemId: 'contextProtocolSendMethodDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onProtocolSendMethodsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        var menu = Ext.getCmp('ProtocolSendMethodsEditMn');
        menu.showAt([newX,newY]);

        // Already in use send methods cannot be deleted
        if (record.get('locked')) {
            menu.items.get('contextProtocolSendMethodDelete').setDisabled(true);
        } else {
            menu.items.get('contextProtocolSendMethodDelete').setDisabled(false);
        }
    }

});