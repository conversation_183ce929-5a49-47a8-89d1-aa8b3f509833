/*
 * File: app/view/CcpResidualsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpResidualsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpResidualsWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.form.field.ComboBox',
        'Ext.panel.Panel',
        'Ext.chart.Chart',
        'Ext.util.Point',
        'Ext.chart.axis.Category',
        'Ext.chart.axis.Numeric',
        'Ext.chart.series.Column'
    ],

    height: 500,
    id: 'CcpResidualsWin',
    width: 600,
    resizable: false,
    title: 'Saldi',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    id: 'CcpResidualsToolbar',
                    layout: {
                        type: 'hbox',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            id: 'CcpResidualsYear',
                            width: 150,
                            fieldLabel: 'Anno',
                            labelAlign: 'right',
                            labelWidth: 50,
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            editable: false,
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'CcpResidualsYears',
                            valueField: 'id',
                            listeners: {
                                change: {
                                    fn: me.onCcpResidualsYearChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            id: 'CcpResidualsGrouping',
                            width: 250,
                            fieldLabel: 'Raggruppamento',
                            labelAlign: 'right',
                            value: 'C',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            editable: false,
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'CcpResidualsGroupings',
                            valueField: 'id',
                            listeners: {
                                change: {
                                    fn: me.onCcpResidualsGroupingChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'panel',
                    flex: 1,
                    border: false,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'chart',
                            flex: 1,
                            id: 'CcpResidualsPaymentCategory',
                            shadow: true,
                            animate: true,
                            insetPadding: 20,
                            store: 'CcpResiduals',
                            theme: 'Residuals',
                            axes: [
                                {
                                    type: 'Category',
                                    fields: [
                                        'text'
                                    ],
                                    label: {
                                        rotate: {
                                            degrees: 45
                                        },
                                        renderer: function(s) {
                                            return Ext.String.ellipsis(s,10);    
                                        }
                                    },
                                    position: 'bottom'
                                },
                                {
                                    type: 'Numeric',
                                    fields: [
                                        'balance'
                                    ],
                                    label: {
                                        renderer: Ext.util.Format.numberRenderer('0,000.00')
                                    },
                                    grid: true,
                                    title: '',
                                    adjustMaximumByMajorUnit: true,
                                    adjustMinimumByMajorUnit: true,
                                    position: 'left'
                                }
                            ],
                            series: [
                                {
                                    type: 'column',
                                    renderer: function(sprite, record, attributes, index, store) {
                                        if (record.get('balance') >= 0) {
                                            return Ext.apply(attributes, {
                                                fill: 'rgb(0,104,51)'
                                            });
                                        } else {
                                            return Ext.apply(attributes, {
                                                fill: 'rgb(153,0,0)'
                                            });
                                        }
                                    },
                                    highlight: true,
                                    label: {
                                        display: 'insideEnd',
                                        orientation: 'vertical',
                                        field: 'balance',
                                        contrast: true,
                                        color: '#333',
                                        'text-anchor': 'middle',
                                        renderer: Ext.util.Format.numberRenderer('0,000.00')
                                    },
                                    tips: {
                                        renderer: function(storeItem, item) {
                                            this.setTitle(storeItem.get('text') + ' ' + Ext.util.Format.number(storeItem.get('balance'), '0,000.00') + ' ');
                                            this.update(
                                                'Iniziale: ' + Ext.util.Format.number(storeItem.get('initial_balance'), '0,000.00') + ' <br />' +
                                                'Operazioni a credito al 01/01: ' + Ext.util.Format.number(storeItem.get('credit'), '0,000.00') + ' <br />' +
                                                'Operazioni a debito al 01/01: ' + Ext.util.Format.number(storeItem.get('debit'), '0,000.00') + ' <br />'
                                            );
                                        }
                                    },
                                    title: 'Saldo',
                                    xField: 'text',
                                    yField: 'balance'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpResidualsYearChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpResidualsWin').loadResiduals();
    },

    onCcpResidualsGroupingChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpResidualsWin').loadResiduals();
    },

    loadResiduals: function() {
        var year = Ext.getCmp('CcpResidualsYear').getValue(),
            grouping = Ext.getCmp('CcpResidualsGrouping').getValue();

        Ext.getStore('CcpResiduals').load({
            params: {
                year: year,
                type: 'P' + grouping
            }
        });
    }

});