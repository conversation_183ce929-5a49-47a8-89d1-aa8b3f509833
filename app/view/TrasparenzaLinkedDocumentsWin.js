/*
 * File: app/view/TrasparenzaLinkedDocumentsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TrasparenzaLinkedDocumentsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.TrasparenzaLinkedDocumentsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.View'
    ],

    permissible: true,
    height: 250,
    id: 'TrasparenzaLinkedDocumentsWin',
    itemId: 'TrasparenzaLinkedDocumentsWin',
    width: 400,
    resizable: false,
    title: 'Documenti allegati',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'TrasparenzaLinkedDocumentsGrid',
                    itemId: 'TrasparenzaLinkedDocumentsGrid',
                    emptyText: 'Nessun documento allegato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'TrasparenzaLinkedDocuments',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'filename',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'actioncolumn',
                            draggable: false,
                            id: 'TrasparenzaLinkedDocumentsDownloadColumn',
                            itemId: 'TrasparenzaLinkedDocumentsDownloadColumn',
                            width: 20,
                            resizable: false,
                            hideable: false,
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        window.open("/mc2-api/archive/document_file/" + record.get('id'), "MC2DocumentDownloadWindow", "status=0, menubar=0, location=0");
                                    },
                                    iconCls: 'icon-arrow_down',
                                    tooltip: 'Scarica'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});