/*
 * File: app/view/CcpReceiptEditMultiWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpReceiptEditMultiWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpReceiptEditMultiWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.form.field.Hidden',
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.grid.column.Number',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Spacer'
    ],

    height: 474,
    id: 'CcpReceiptEditMultiWin',
    width: 927,
    resizable: false,
    title: 'Ricevuta',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'CcpReceiptEditMultiForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    method: 'POST',
                    url: '/mc2-api/ccp/receipt',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var m = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection()[0],
                                            form = Ext.getCmp('CcpReceiptEditMultiForm').getForm();

                                        form.submit({
                                            success: function(form, action) {
                                                var r = action.result.results;

                                                Ext.getStore('CcpPayments').load();
                                                Ext.getStore('CcpReceipts').load();

                                                Ext.getCmp('CcpReceiptEditMultiWin').close();

                                                /*if (r) {
                                                Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: {
                                                newSpool: 1,
                                                print: 'Receipt',
                                                namespace: 'CCP',
                                                type: 'PDF',
                                                mime: 'application/pdf',
                                                receipt_id: r.id,
                                                number: r.number,
                                                date: r.date
                                                },
                                                success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                                }
                                                });
                                                }*/
                                                Ext.getStore('CcpReceiptAddressCode').load();
                                                Ext.getCmp('CcpStudentsTab').reloadStudent();
                                            },
                                            failure: function(form, action) {

                                                Ext.Msg.alert('Attenzione', action.result.message);
                                            }
                                        });
                                    },
                                    iconCls: 'icon-page',
                                    text: 'Emetti'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'middle'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    fieldLabel: 'Tipologia',
                                    labelAlign: 'right',
                                    name: 'receipt_type',
                                    store: [
                                        [
                                            'R',
                                            'Ricevuta'
                                        ],
                                        [
                                            'A',
                                            'Attestazione'
                                        ]
                                    ],
                                    listeners: {
                                        render: {
                                            fn: me.onComboboxRender,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'numberfield',
                                    flex: 1,
                                    fieldLabel: 'Numero',
                                    labelAlign: 'right',
                                    name: 'number',
                                    hideTrigger: true,
                                    allowDecimals: false,
                                    allowExponential: false,
                                    minValue: 1
                                },
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    labelAlign: 'right',
                                    fieldLabel: 'Suffisso',
                                    name: 'suffix',
                                    id: 'CcpReceiptEditMultiSuffix',
                                },
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    fieldLabel: 'Data',
                                    labelAlign: 'right',
                                    name: 'date',
                                    editable: false,
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'c'
                                },
                                {
                                    xtype: 'checkboxfield',

                                    margin: '0 0 0 10',
                                    name: 'group_by_brothers',
                                    value: 1,
                                    boxLabel: 'Raggruppa fratelli',
                                    inputValue: '1',
                                    uncheckedValue: '0'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    id: 'CcpReceiptEditMultiLinkedPayments',
                                    name: 'linked_payments'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'CcpNewReceiptStudentPayments',
                    title: 'Pagamenti',
                    titleAlign: 'center',
                    emptyText: 'Nessun pagamento senza fattura trovato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    store: 'CcpPaymentsUnreceipted',
                    columns: [
                        {
                            xtype: 'datecolumn',
                            width: 110,
                            align: 'center',
                            dataIndex: 'operation_date',
                            text: 'Data operazione',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'numbercolumn',
                            align: 'right',
                            dataIndex: 'total',
                            text: 'Importo'
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if (record.get('description') && record.get('description') !== value) value+=' - '+record.get('description');

                                return value;
                            },
                            dataIndex: 'type_text',
                            text: 'Tipo movimento',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'payer_data',
                            text: 'Pagante',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'payment_method_text',
                            text: 'Metodo di pagamento',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'subject_data',
                            text: 'Debitore',
                            flex: 1
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true,
                        listeners: {
                            selectionchange: {
                                fn: me.onCheckboxModelSelectionChange,
                                scope: me
                            }
                        }
                    }),
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch',
                                        pack: 'end'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            id: 'CcpReceptNewDateStart',
                                            fieldLabel: 'Da',
                                            labelWidth: 30,
                                            name: 'accountable_date_start',
                                            format: 'd/m/Y',
                                            submitFormat: 'Y-m-d',
                                            listeners: {
                                                afterrender: {
                                                    fn: me.onCcpReceptNewDateStartAfterRender,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            id: 'CcpReceptNewDateEnd',
                                            fieldLabel: 'A',
                                            labelWidth: 30,
                                            name: 'accountable_date_end',
                                            format: 'd/m/Y',
                                            submitFormat: 'Y-m-d',
                                            listeners: {
                                                afterrender: {
                                                    fn: me.onCcpReceptNewDateEndAfterRender,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpReceptNewQuery',
                                            fieldLabel: '',
                                            emptyText: 'Debitore ...'
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'CcpPayFromCreditCh',
                                            margin: '0 5',
                                            fieldLabel: 'Agg. pagati da credito',
                                            labelWidth: 120,
                                            inputValue: '1',
                                            uncheckedValue: '0'
                                        },
                                        {
                                            xtype: 'tbspacer'
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                var params = {
                                                    accountable_date_start: Ext.getCmp('CcpReceptNewDateStart').getValue(),
                                                    accountable_date_end: Ext.getCmp('CcpReceptNewDateEnd').getValue(),
                                                    query: Ext.getCmp('CcpReceptNewQuery').getValue(),
                                                    by_credit: Ext.getCmp('CcpPayFromCreditCh').getValue() === true ? 1 : 0
                                                };

                                                Ext.getStore('CcpPaymentsUnreceipted').load({
                                                    params: params
                                                });
                                            },
                                            flex: 1,
                                            text: 'Cerca'
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpReceiptEditMultiWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onComboboxRender: function(component, eOpts) {
        component.setValue('R');
    },

    onCheckboxModelSelectionChange: function(model, selected, eOpts) {
        var payments = [];

        Ext.each(selected, function(i){
            payments.push(i.get('id'));
        });

        Ext.getCmp('CcpReceiptEditMultiLinkedPayments').setValue(Ext.encode(payments));
    },

    onCcpReceptNewDateStartAfterRender: function(component, eOpts) {

        var dd = new Date();
        dd.setMonth(dd.getMonth()-1);
        component.setValue(dd);
    },

    onCcpReceptNewDateEndAfterRender: function(component, eOpts) {
        var dd = new Date();
        component.setValue(dd);

    },

    onCcpReceiptEditMultiWinShow: function(component, eOpts) {
        Ext.getStore('CcpPaymentsUnreceipted').removeAll();
    }

});