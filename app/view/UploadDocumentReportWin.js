/*
 * File: app/view/UploadDocumentReportWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.UploadDocumentReportWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.UploadDocumentReportWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.File',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    height: 113,
    id: 'UploadDocumentReportWin',
    width: 400,
    layout: 'fit',
    title: 'Aggiorna documento',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'UploadDocumentReportFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/ccp/report/update_doc',
                    items: [
                        {
                            xtype: 'filefield',
                            anchor: '100%',
                            fieldLabel: 'Cerca file',
                            name: 'file'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'bottom',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('UploadDocumentReportFrm').getForm().submit({
                                            params: {
                                                id: Ext.getCmp('CcpReportGrd').getSelectionModel().getSelection()[0].get('id')
                                            },
                                            success: function(res, rr) {
                                                var r= Ext.decode(rr.response.responseText);
                                                if(r.success===true) {
                                                    Ext.Msg.alert('SUCCESSO', 'File aggiornato');
                                                    Ext.getStore('CcpReports').load();
                                                    Ext.getCmp('UploadDocumentReportWin').close();
                                                } else {
                                                    Ext.Msg.alert('ERRORE', r.message);
                                                }
                                            },
                                            failure: function(res, rr) {
                                                var r= Ext.decode(rr.response.responseText);
                                                if (r.message) {
                                                    Ext.Msg.alert('ERRORE', r.message);
                                                } else {
                                                    Ext.Msg.alert('ERRORE', 'Errore inaspettato. Contattare l\'assistenza');
                                                }

                                            }
                                        });

                                    },
                                    text: 'Carica'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});