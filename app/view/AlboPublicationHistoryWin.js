/*
 * File: app/view/AlboPublicationHistoryWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboPublicationHistoryWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboPublicationHistoryWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.grid.View'
    ],

    height: 150,
    id: 'AlboPublicationHistoryWin',
    itemId: 'AlboPublicationHistoryWin',
    width: 400,
    resizable: false,
    title: 'Storico Pubblicazione',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    disableSelection: true,
                    emptyText: 'Nessuno storico per questa pubblicazione.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'AlboHistories',
                    columns: [
                        {
                            xtype: 'datecolumn',
                            resizable: false,
                            align: 'center',
                            dataIndex: 'date',
                            hideable: false,
                            text: 'Data',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'user_text',
                            hideable: false,
                            text: 'Utente'
                        },
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'action',
                            hideable: false,
                            text: 'Operazione',
                            flex: 1
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});