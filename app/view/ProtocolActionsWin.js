/*
 * File: app/view/ProtocolActionsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolActionsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolActionsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.form.field.Checkbox',
        'Ext.form.field.ComboBox',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 300,
    id: 'ProtocolActionsWin',
    itemId: 'ProtocolActionsWin',
    width: 700,
    resizable: false,
    title: 'Protocollazione Automatica',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'ProtocolActionsGrid',
                    itemId: 'ProtocolActionsGrid',
                    header: false,
                    emptyText: 'Nessuna Azione di protocollazione automatica caricata.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'ProtocolActions',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value === true) {
                                    return '<img src="./resources/icons/accept.png" />';
                                }
                                return '';
                            },
                            width: 50,
                            dataIndex: 'active',
                            text: 'Attivo',
                            editor: {
                                xtype: 'checkboxfield',
                                name: 'active',
                                uncheckedValue: 'off'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            text: 'Azione',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'comment',
                            text: 'Commento su Protocollo',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                name: 'comment',
                                allowBlank: false,
                                allowOnlyWhitespace: false
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                return record.get('type_codes');
                            },
                            resizable: false,
                            dataIndex: 'type_id',
                            text: 'Titolario',
                            editor: {
                                xtype: 'combobox',
                                name: 'type_id',
                                editable: false,
                                matchFieldWidth: false,
                                displayField: 'full_denomination',
                                forceSelection: true,
                                store: 'ProtocolTypesLeaf',
                                valueField: 'id'
                            }
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            blocked: true,
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                },
                                beforeedit: {
                                    fn: me.onRowEditingBeforeEdit,
                                    scope: me
                                }
                            }
                        })
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onProtocolActionsGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'ProtocolActionsEditMn',
                    itemId: 'ProtocolActionsEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var pg = Ext.getCmp('ProtocolActionsGrid'),
                                    record = pg.getSelectionModel().getSelection()[0];

                                pg.getPlugin().blocked = false;
                                pg.getPlugin().startEdit(record);
                                pg.getPlugin().blocked = true;
                            },
                            id: 'contextProtocolActionEdit',
                            itemId: 'contextProtocolActionEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        var store = Ext.getStore('ProtocolActions');

        store.sync();
    },

    onRowEditingBeforeEdit: function(editor, context, eOpts) {
        var pg = Ext.getCmp('ProtocolActionsGrid');

        return !pg.getPlugin().blocked;
    },

    onProtocolActionsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        var menu = Ext.getCmp('ProtocolActionsEditMn');
        menu.showAt([newX,newY]);
    }

});