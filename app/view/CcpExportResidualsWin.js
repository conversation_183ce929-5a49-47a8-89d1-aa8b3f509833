/*
 * File: app/view/CcpExportResidualsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpExportResidualsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpExportResidualsWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Spacer'
    ],

    width: 400,
    layout: 'fit',
    title: 'Esportazione saldi (fino a ...)',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpExportResidualsFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '',
                    items: [
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            id: 'CcpExpResidualsAccountableDate',
                            fieldLabel: '<b>Data pagamento</b>',
                            labelWidth: 150,
                            name: 'accountable_date',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d',
                            listeners: {
                                change: {
                                    fn: me.onCcpExpResidualsAccountableDateChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            id: 'CcpExpResidualsInvoiceDate',
                            fieldLabel: 'Data fattura',
                            labelWidth: 150,
                            name: 'invoice_date',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        },
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            id: 'CcpExpResidualsCreditAccountableDate',
                            fieldLabel: 'Data versamento credito',
                            labelWidth: 150,
                            name: 'deposit_accountable_date',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        }
                    ],
                    listeners: {
                        afterrender: {
                            fn: me.onFormAfterRender,
                            scope: me
                        }
                    }
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {

                                var form = Ext.getCmp('CcpExportResidualsFrm');
                                if(form.isValid()) {
                                    var rec = form.getValues();

                                    rec.newSpool = 1;
                                    rec.namespace = 'CCP';
                                    rec.type = 'XLS';
                                    rec.mime = 'application/vnd.ms-excel';
                                    rec.print = 'ExportResiduals';

                                    Ext.Ajax.request({
                                        url: '/mc2-api/core/print',
                                        params: rec,
                                        success: function(response, opts) {
                                            var res = Ext.decode(response.responseText);
                                            mc2ui.app.showNotifyPrint(res);
                                        }
                                    });
                                }
                            },
                            iconCls: 'icon-printer',
                            text: 'Esporta'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpExpResidualsAccountableDateChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpExpResidualsInvoiceDate').setValue(newValue);
        Ext.getCmp('CcpExpResidualsCreditAccountableDate').setValue(newValue);
    },

    onFormAfterRender: function(component, eOpts) {
        Ext.getCmp('CcpExpResidualsAccountableDate').setValue(new Date());
        Ext.getCmp('CcpExpResidualsInvoiceDate').setValue(new Date());
        Ext.getCmp('CcpExpResidualsCreditAccountableDate').setValue(new Date());
    }

});