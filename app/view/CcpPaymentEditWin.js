/*
 * File: app/view/CcpPaymentEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpPaymentEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpPaymentEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.FieldSet',
        'Ext.form.field.Date',
        'Ext.form.field.Hidden',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Checkbox',
        'Ext.form.field.Number',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.column.Number',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Spacer',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    id: 'CcpPaymentEditWin',
    width: 700,
    title: 'Pagamento',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    resetBaseData: function() {
                        Ext.getCmp('CcpPaymentPayerId').setValue();
                        Ext.getCmp('CcpPaymentPayerName').setValue();
                        Ext.getCmp('CcpPaymentPayerSurname').setValue();
                        Ext.getCmp('CcpPaymentPayerFiscalCode').setValue();
                        Ext.getCmp('CcpPaymentPayerAddress').setValue();
                        Ext.getCmp('CcpPaymentPayerCity').setValue();
                        Ext.getCmp('CcpPaymentPayerProvince').setValue();
                        Ext.getCmp('CcpPaymentPayerZipCode').setValue();
                    },
                    border: false,
                    id: 'CcpPaymentForm',
                    itemId: 'CcpPaymentForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    margin: '0 10 0 0',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'fieldset',
                                            title: 'Date',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'datefield',
                                                    id: 'CcpPaymentOperationDate',
                                                    itemId: 'CcpPaymentOperationDate',
                                                    maxWidth: 200,
                                                    fieldLabel: 'Operazione',
                                                    labelAlign: 'right',
                                                    name: 'operation_date',
                                                    format: 'd/m/Y',
                                                    startDay: 1,
                                                    submitFormat: 'c',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCcpPaymentOperationDateChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'datefield',
                                                    id: 'CcpPaymentAccountableDate',
                                                    itemId: 'CcpPaymentAccountableDate',
                                                    maxWidth: 200,
                                                    fieldLabel: 'Pagamento',
                                                    labelAlign: 'right',
                                                    name: 'accountable_date',
                                                    format: 'd/m/Y',
                                                    startDay: 1,
                                                    submitFormat: 'c'
                                                },
                                                {
                                                    xtype: 'hiddenfield',
                                                    flex: 1,
                                                    id: 'CcpPaymentCreditRemain',
                                                    itemId: 'CcpPaymentCreditRemain',
                                                    fieldLabel: 'Label'
                                                },
                                                {
                                                    xtype: 'hiddenfield',
                                                    flex: 1,
                                                    id: 'CcpPaymentCreditId',
                                                    itemId: 'CcpPaymentCreditId',
                                                    fieldLabel: 'Label',
                                                    name: 'ccp_credit'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'fieldset',
                                            height: 130,
                                            minHeight: 100,
                                            title: 'Dati di pagamento',
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    margin: '0 10 0 0',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            flex: 1,
                                                            id: 'CcpPaymentPaymentMethodId',
                                                            fieldLabel: 'Modalità',
                                                            labelAlign: 'right',
                                                            name: 'payment_method_id',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            editable: false,
                                                            displayField: 'name',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpPaymentMethods',
                                                            valueField: 'id',
                                                            listeners: {
                                                                afterrender: {
                                                                    fn: me.onCcpPaymentPaymentMethodIdBeforeRender1,
                                                                    scope: me
                                                                },
                                                                change: {
                                                                    fn: me.onCcpPaymentPaymentMethodIdChange,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'combobox',
                                                            flex: 1,
                                                            id: 'CcpPaymentPaymentDestinationId',
                                                            fieldLabel: 'Conto Corrente',
                                                            labelAlign: 'right',
                                                            name: 'account_id',
                                                            allowBlank: false,
                                                            allowOnlyWhitespace: false,
                                                            editable: false,
                                                            displayField: 'denomination',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpPaymentDestinations',
                                                            valueField: 'id'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'checkboxfield',
                                                    id: 'CcpPaymentGroupPaymentCh',
                                                    fieldLabel: 'Raggruppa',
                                                    labelAlign: 'right',
                                                    name: 'group_payment',
                                                    boxLabel: '',
                                                    inputValue: '1',
                                                    uncheckedValue: '0',
                                                    listeners: {
                                                        beforerender: {
                                                            fn: me.onCcpPaymentGroupPaymentChBeforeRender,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    disabled: true,
                                                    hidden: true,
                                                    id: 'CcpPayByCreditTxt',
                                                    fieldLabel: 'Pagata da credito',
                                                    name: 'payment_method_text'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            margin: '0 0 5 0',
                                            layout: {
                                                type: 'hbox',
                                                align: 'middle',
                                                pack: 'end'
                                            },
                                            items: [
                                                {
                                                    xtype: 'numberfield',
                                                    id: 'CcpPaymentAmount',
                                                    itemId: 'CcpPaymentAmount',
                                                    width: 230,
                                                    fieldLabel: 'Importo',
                                                    labelAlign: 'right',
                                                    name: 'amount',
                                                    fieldStyle: 'text-align:right;',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    minLength: 1,
                                                    hideTrigger: true,
                                                    repeatTriggerClick: false,
                                                    keyNavEnabled: false,
                                                    mouseWheelEnabled: false,
                                                    spinDownEnabled: false,
                                                    spinUpEnabled: false,
                                                    allowExponential: false,
                                                    autoStripChars: true,
                                                    submitLocaleSeparator: false
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridpanel',
                                            flex: 1,
                                            hidden: true,
                                            id: 'CcpPaymentEditLinkedAdditionalsGrid',
                                            margin: '0 0 5 0',
                                            minHeight: 180,
                                            title: 'Addizionali',
                                            titleAlign: 'center',
                                            emptyText: 'Nessuna Addizionale abbinata.',
                                            enableColumnHide: false,
                                            enableColumnMove: false,
                                            sortableColumns: false,
                                            store: 'CcpLinkedAdditionalsForm',
                                            columns: [
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 20,
                                                    resizable: false,
                                                    hideable: false,
                                                    items: [
                                                        {
                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('positive')) {
                                                                    return 'icon-control_add';
                                                                } else {
                                                                    return 'icon-control_remove';
                                                                }
                                                            },
                                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('positive')) {
                                                                    return 'Addizionale positiva';
                                                                } else {
                                                                    return 'Addizionale negativa';
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    resizable: false,
                                                    dataIndex: 'name',
                                                    hideable: false,
                                                    text: 'Nome',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    resizable: false,
                                                    align: 'right',
                                                    dataIndex: 'amount',
                                                    text: 'Importo',
                                                    editor: {
                                                        xtype: 'numberfield',
                                                        allowBlank: false,
                                                        allowOnlyWhitespace: false,
                                                        hideTrigger: true,
                                                        allowExponential: false,
                                                        minValue: 0
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if (value) {
                                                            return '%';
                                                        } else {
                                                            return '';
                                                        }
                                                    },
                                                    width: 20,
                                                    resizable: false,
                                                    align: 'center',
                                                    dataIndex: 'percentual',
                                                    hideable: false
                                                }
                                            ],
                                            listeners: {
                                                itemcontextmenu: {
                                                    fn: me.onCcpPaymentEditLinkedAdditionalsGridItemContextMenu,
                                                    scope: me
                                                }
                                            },
                                            plugins: [
                                                Ext.create('Ext.grid.plugin.RowEditing', {
                                                    blocked: true,
                                                    listeners: {
                                                        beforeedit: {
                                                            fn: me.onRowEditingBeforeEdit,
                                                            scope: me
                                                        }
                                                    }
                                                })
                                            ],
                                            dockedItems: [
                                                {
                                                    xtype: 'toolbar',
                                                    permissible: true,
                                                    dock: 'top',
                                                    id: 'CcpPaymentEditAdditionalsToolbar',
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                Ext.widget('CcpLinkedAdditionalsPickerWin').show();

                                                                Ext.getStore('CcpAdditionalsForm').load({
                                                                    params: {
                                                                        type: 'P'
                                                                    }
                                                                });
                                                            },
                                                            iconCls: 'icon-link',
                                                            text: 'Abbina addizionali'
                                                        },
                                                        {
                                                            xtype: 'tbspacer',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                Ext.widget('CcpAdditionalEditWin').createLink = 'P';
                                                                Ext.getCmp('CcpAdditionalEditWin').show();
                                                            },
                                                            id: 'CcpPaymentEditNewAdditionalBtn',
                                                            iconCls: 'icon-add',
                                                            text: 'Crea nuova'
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            flex: 1,
                                            id: 'CcpPaymentId',
                                            itemId: 'CcpPaymentId',
                                            name: 'id'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            flex: 1,
                                            id: 'CcpPaymentMovementId',
                                            itemId: 'CcpPaymentMovementId',
                                            name: 'movement_id'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    flex: 1,
                                                    id: 'CcpPaymentBollettino',
                                                    itemId: 'CcpPaymentBollettino',
                                                    fieldLabel: 'Bollettino',
                                                    labelAlign: 'right',
                                                    name: 'bollettino',
                                                    fieldStyle: 'text-align:right;'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    flex: 1,
                                                    id: 'CcpPaymentAccountReference',
                                                    itemId: 'CcpPaymentAccountReference',
                                                    fieldLabel: 'Riferimento C/C',
                                                    labelAlign: 'right',
                                                    name: 'account_reference'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Pagante / Ricevente',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            id: 'CcpPaymentDefPayerCh',
                                            fieldLabel: 'Pag/Ric. Predefinito',
                                            labelAlign: 'right',
                                            labelWidth: 120,
                                            name: 'default_payers',
                                            boxLabel: '',
                                            inputValue: '1',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpPaymentDefPayerChChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            id: 'CcpPaymentPayerCnt',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    margin: '0 0 5 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            flex: 1,
                                                            id: 'CcpPaymentPayer',
                                                            fieldLabel: 'Pagante / Ricevente',
                                                            labelAlign: 'right',
                                                            labelWidth: 120,
                                                            name: 'payer',
                                                            editable: false,
                                                            displayField: 'display',
                                                            forceSelection: true,
                                                            queryMode: 'local',
                                                            store: 'CcpPayers',
                                                            valueField: 'id',
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onComboboxSelect,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                Ext.getCmp('CcpPaymentPayer').setValue();
                                                                Ext.getCmp('CcpPaymentPayerType').setValue();
                                                                Ext.getCmp('CcpPaymentForm').resetBaseData();
                                                            },
                                                            id: 'CcpPaymentPayerClear',
                                                            iconCls: 'icon-delete'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'hiddenfield',
                                                    id: 'CcpPaymentPayerId',
                                                    itemId: 'CcpPaymentPayerId',
                                                    fieldLabel: 'Label',
                                                    name: 'payer_id'
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    id: 'CcpPaymentPayerType',
                                                    fieldLabel: 'Tipo',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_type',
                                                    editable: false,
                                                    forceSelection: true,
                                                    queryMode: 'local',
                                                    store: 'CcpPayerTypes',
                                                    valueField: 'id'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpPaymentPayerName',
                                                    fieldLabel: 'Nome',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_name'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpPaymentPayerSurname',
                                                    fieldLabel: 'Cognome',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_surname'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpPaymentPayerFiscalCode',
                                                    maxWidth: 250,
                                                    fieldLabel: 'Codice Fiscale',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_fiscal_code',
                                                    maxLength: 16
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpPaymentPayerAddress',
                                                    fieldLabel: 'Indirizzo',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_address'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpPaymentPayerCity',
                                                    fieldLabel: 'Città',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_city'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpPaymentPayerProvince',
                                                    maxWidth: 170,
                                                    fieldLabel: 'Provincia',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_province',
                                                    maxLength: 2
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    id: 'CcpPaymentPayerZipCode',
                                                    maxWidth: 220,
                                                    fieldLabel: 'CAP',
                                                    labelAlign: 'right',
                                                    labelWidth: 120,
                                                    name: 'payer_zip_code',
                                                    maxLength: 5
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            flex: 1,
                            hidden: true,
                            id: 'CppCoverMovementId',
                            fieldLabel: 'Movimento da coprire',
                            labelAlign: 'right',
                            name: 'covered_movement_id',
                            displayField: 'movement_description',
                            queryMode: 'local',
                            store: 'CcpMovementsCover',
                            valueField: 'id',
                            listeners: {
                                select: {
                                    fn: me.onCppCoverMovementIdSelect,
                                    scope: me
                                }
                            }
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('CcpPayments'),
                                            adds = Ext.getStore('CcpLinkedAdditionalsForm').getRange(),
                                            form = Ext.getCmp('CcpPaymentForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvato';

                                        values.linked_additionals = [];

                                        adds.forEach(function(a) {
                                            values.linked_additionals.push([a.get('additional_id'), a.get('amount')]);
                                        });

                                        values.count_additionals = values.linked_additionals.length;
                                        Ext.getCmp('CcpPaymentEditWin').setLoading();
                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornato';
                                            Ext.getStore('CcpPayments').load({
                                                params: {id: values.id},
                                                callback:function(res){
                                                    record = res[0];
                                                    //record = store.getById(parseInt(values.id));
                                                    record.set('operation_date', values.operation_date);
                                                    record.set('accountable_date', values.accountable_date);
                                                    record.set('amount', values.amount);
                                                    record.set('payment_method_id', values.payment_method_id);
                                                    record.set('account_id', values.account_id);
                                                    record.set('bollettino', values.bollettino);
                                                    record.set('account_reference', values.account_reference);
                                                    record.set('payer_type', values.payer_type);
                                                    record.set('payer_id', values.payer_id);
                                                    record.set('payer_name', values.payer_name);
                                                    record.set('payer_surname', values.payer_surname);
                                                    record.set('payer_fiscal_code', values.payer_fiscal_code);
                                                    record.set('payer_address', values.payer_address);
                                                    record.set('payer_city', values.payer_city);
                                                    record.set('payer_province', values.payer_province);
                                                    record.set('payer_zip_code', values.payer_zip_code);
                                                    record.set('linked_additionals', values.linked_additionals);
                                                    record.set('count_additionals', values.count_additionals);
                                                    record.set('covered_movement_id', values.cover_movement_id);

                                                    store.sync({
                                                        callback: function() {
                                                            store.load({
                                                                params: {
                                                                    linked: 'on',
                                                                    movement: values.movement_id
                                                                }
                                                            });
                                                            Ext.getStore('CcpMovements').load();
                                                            Ext.getStore('CcpAdditionals').load();
                                                            Ext.getStore('CcpReceipts').load();

                                                            if (values.id) {
                                                                Ext.getStore('CcpLinkedPayments').load({
                                                                    params: {
                                                                        linked: 'M',
                                                                        item: values.movement_id
                                                                    }
                                                                });
                                                            }




                                                        },
                                                        success: function(form, action) {
                                                            Ext.getCmp('CcpPaymentEditWin').close();
                                                            Ext.Msg.alert('Successo', 'Pagamento ' + a);
                                                            // Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                            Ext.getStore('CcpMovements').load();
                                                            Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().deselectAll();
                                                            Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();
                                                        },
                                                        failure: function(form, action) {
                                                            Ext.Msg.alert('Attenzione', 'Pagamento NON ' + a);
                                                        }
                                                    });

                                                    if(typeof Ext.getCmp('CcpLinkedPaymentsGrid') !== 'undefined'){
                                                        Ext.getCmp('CcpLinkedPaymentsGrid').getSelectionModel().deselectAll();
                                                    }

                                                    if(typeof Ext.getCmp('CcpPaymentsGrid') !== 'undefined'){
                                                        Ext.getCmp('CcpPaymentsGrid').getSelectionModel().deselectAll();
                                                    }
                                                }
                                            });

                                        } else {


                                            store.add({
                                                movement_id: values.movement_id,
                                                operation_date: values.operation_date,
                                                accountable_date: values.accountable_date,
                                                amount: values.amount,
                                                payment_method_id: values.payment_method_id,
                                                account_id: values.account_id,
                                                bollettino: values.bollettino,
                                                account_reference: values.account_reference,
                                                payer_type: values.payer_type,
                                                payer_id: values.payer_id,
                                                payer_name: values.payer_name,
                                                payer_surname: values.payer_surname,
                                                payer_fiscal_code: values.payer_fiscal_code,
                                                payer_address: values.payer_address,
                                                payer_city: values.payer_city,
                                                payer_province: values.payer_province,
                                                payer_zip_code: values.payer_zip_code,
                                                linked_additionals: values.linked_additionals,
                                                count_additionals: values.count_additionals,
                                                ccp_credit: values.ccp_credit,
                                                covered_movement_id: values.covered_movement_id
                                            });

                                            store.sync({
                                                callback: function() {
                                                    store.load({
                                                        params: {
                                                            linked: 'on',
                                                            movement: values.movement_id
                                                        }
                                                    });
                                                    Ext.getStore('CcpMovements').load();
                                                    Ext.getStore('CcpAdditionals').load();
                                                    Ext.getStore('CcpReceipts').load();

                                                    //if (values.id) {
                                                    Ext.getStore('CcpLinkedPayments').load({
                                                        params: {
                                                            linked: 'M',
                                                            item: values.movement_id
                                                        }
                                                    });
                                                    //}

                                                    var grid =Ext.getCmp('CcpStudentsGrd'),
                                                        studentSel = grid.getSelectionModel().getSelection();
                                                    if(studentSel.length > 0 ){
                                                        var s = studentSel[0];
                                                        grid.getView().fireEvent('itemclick',grid , s);
                                                    }
                                                    Ext.getCmp('CcpPaymentEditWin').setLoading(false);
                                                },
                                                success: function(form, action) {
                                                    Ext.getCmp('CcpPaymentEditWin').close();
                                                    Ext.Msg.alert('Successo', 'Pagamento ' + a);
                                                    // Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                    Ext.getStore('CcpMovements').load();
                                                    Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().deselectAll();
                                                    Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();

                                                },
                                                failure: function(form, action) {
                                                    Ext.Msg.alert('Attenzione', 'Pagamento NON ' + a);
                                                }
                                            });

                                            if(typeof Ext.getCmp('CcpLinkedPaymentsGrid') !== 'undefined'){
                                                Ext.getCmp('CcpLinkedPaymentsGrid').getSelectionModel().deselectAll();
                                            }

                                            if(typeof Ext.getCmp('CcpPaymentsGrid') !== 'undefined'){
                                                Ext.getCmp('CcpPaymentsGrid').getSelectionModel().deselectAll();
                                            }
                                        }



                                    },
                                    formBind: true,
                                    id: 'CcpStudentPaymentSave',
                                    itemId: 'CcpStudentPaymentSave',
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('CcpPayments'),
                                            adds = Ext.getStore('CcpLinkedAdditionalsForm').getRange(),
                                            form = Ext.getCmp('CcpPaymentForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvato';

                                        values.linked_additionals = [];

                                        adds.forEach(function(a) {
                                            values.linked_additionals.push([a.get('additional_id'), a.get('amount')]);
                                        });

                                        values.count_additionals = values.linked_additionals.length;

                                        var params = {
                                            movements: values.movement_id,
                                            operation_date: values.operation_date,
                                            accountable_date: values.accountable_date,
                                            amount: values.amount,
                                            payment_method_id: values.payment_method_id,
                                            account_id: values.account_id,
                                            bollettino: values.bollettino,
                                            account_reference: values.account_reference,
                                            payer_type: values.payer_type,
                                            payer_id: values.payer_id,
                                            payer_name: values.payer_name,
                                            payer_surname: values.payer_surname,
                                            payer_fiscal_code: values.payer_fiscal_code,
                                            payer_address: values.payer_address,
                                            payer_city: values.payer_city,
                                            payer_province: values.payer_province,
                                            payer_zip_code: values.payer_zip_code,
                                            linked_additionals: values.linked_additionals,
                                            count_additionals: values.count_additionals,
                                            ccp_credit: values.ccp_credit,
                                            group_payment: values.group_payment === '1' ? true: false,
                                            default_payers: values.default_payers === '1' ? true: false
                                        };

                                        Ext.Ajax.request({
                                            method: 'POST',
                                            url: '/mc2-api/ccp/massive_payments',
                                            params: params,
                                            success: function(form, action) {
                                                Ext.getCmp('CcpPaymentEditWin').close();
                                                Ext.Msg.alert('Successo', 'Pagamento avvenuto');
                                                Ext.getStore('CcpMovements').load();
                                                Ext.getCmp('CcpMovementsStudentGrid').getSelectionModel().deselectAll();
                                                Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();
                                                var grid =Ext.getCmp('CcpStudentsGrd'),
                                                    studentSel = grid.getSelectionModel().getSelection();
                                                if(studentSel.length > 0 ){
                                                    var s = studentSel[0];
                                                    grid.getView().fireEvent('itemclick',grid , s);
                                                }

                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Pagamento NON ' + a);
                                            }
                                        });



                                    },
                                    id: 'CcpStudentPaymentMassive',
                                    itemId: 'CcpStudentPaymentMassive',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    flex: 1,
                    id: 'CcpPaymentEditAdditionalEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var g = Ext.getCmp('CcpPaymentEditLinkedAdditionalsGrid'),
                                    r = g.getSelectionModel().getSelection()[0],
                                    sAdds = Ext.getStore('CcpLinkedAdditionalsForm');

                                g.getPlugin().blocked = false;
                                g.getPlugin().startEdit(r);
                                g.getPlugin().blocked = true;
                            },
                            id: 'contextCcpPaymentEditAdditionalEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica Importo'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('CcpPaymentEditLinkedAdditionalsGrid').getSelectionModel().getSelection()[0],
                                    sAdds = Ext.getStore('CcpLinkedAdditionalsForm');

                                sAdds.remove(r);
                            },
                            id: 'contextCcpPaymentEditAdditionalDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onCcpPaymentEditWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCcpPaymentOperationDateChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpPaymentAccountableDate').setValue(newValue);
    },

    onCcpPaymentPaymentMethodIdBeforeRender1: function(component, eOpts) {
        setTimeout(function(){
            if(!component.getValue())
                component.setValue(mc2ui.app.settings.def_paymenth_method);}, 500);


    },

    onCcpPaymentPaymentMethodIdChange: function(field, newValue, oldValue, eOpts) {
        if (newValue>0) {
            Ext.getCmp('CcpPaymentGroupPaymentCh').setValue(0);
            if(field.getStore().getById(newValue) && field.getStore().getById(newValue).get('massive_payment_group') === true) {
                Ext.getCmp('CcpPaymentGroupPaymentCh').setValue(1);
            }
        }
    },

    onCcpPaymentGroupPaymentChBeforeRender: function(component, eOpts) {
         if (Ext.getStore('CcpPaymentMethods').getAt(mc2ui.app.settings.def_paymenth_method).get('massive_payment_group') === true) {
             Ext.getCmp('CcpPaymentGroupPaymentCh').setValue(1);
         }
    },

    onCcpPaymentEditLinkedAdditionalsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('CcpPaymentEditAdditionalEditMn').showAt([newX,newY]);
    },

    onRowEditingBeforeEdit: function(editor, context, eOpts) {
        var pg = Ext.getCmp('CcpPaymentEditLinkedAdditionalsGrid');

        return !pg.getPlugin().blocked;
    },

    onCcpPaymentDefPayerChChange: function(field, newValue, oldValue, eOpts) {

        if (newValue === true) {
            Ext.getCmp('CcpPaymentPayerClear').handler();
            Ext.getCmp('CcpPaymentPayerCnt').disable();
        } else {
            Ext.getCmp('CcpPaymentPayerCnt').enable();
        }
    },

    onComboboxSelect: function(field, newValue, oldValue, eOpts) {
        var record = Ext.getStore('CcpPayers').getById(newValue);

        if (record){
            Ext.getCmp('CcpPaymentPayerType').setValue(record.get('type'));
            Ext.getCmp('CcpPaymentPayerId').setValue(record.get('db_id'));
            Ext.getCmp('CcpPaymentPayerName').setValue(record.get('name'));
            Ext.getCmp('CcpPaymentPayerSurname').setValue(record.get('surname'));
            Ext.getCmp('CcpPaymentPayerFiscalCode').setValue(record.get('fiscal_code'));
            Ext.getCmp('CcpPaymentPayerAddress').setValue(record.get('address'));
            Ext.getCmp('CcpPaymentPayerCity').setValue(record.get('city'));
            Ext.getCmp('CcpPaymentPayerProvince').setValue(record.get('province'));
            Ext.getCmp('CcpPaymentPayerZipCode').setValue(record.get('zip_code'));
        }
    },

    onCppCoverMovementIdSelect: function(combo, records, eOpts) {
        var diff_linked_mov = records[0].get('total') - records[0].get('total_payments'),
            remain_to_spend = Ext.getCmp('CcpPaymentAmount').getValue();

        Ext.getCmp('CcpPaymentAmount').setValue(Math.min(remain_to_spend, diff_linked_mov));

    },

    onCcpPaymentEditWinClose: function(panel, eOpts) {
        Ext.getStore('CcpLinkedAdditionalsForm').removeAll();
    }

});