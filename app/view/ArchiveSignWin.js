/*
 * File: app/view/ArchiveSignWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveSignWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveSignWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    id: 'ArchiveSignWin',
    width: 500,
    layout: 'fit',
    title: 'Firma documenti',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'SignFilesFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/archive/document/sign',
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'end'
                    }
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.getCmp('SignFilesFrm').submit({
                                    success: function(r, res){
                                        var response = Ext.decode(res.response.responseText);

                                        if(response.success === true){
                                            Ext.Msg.alert('FIRMA', response.message);
                                            Ext.getCmp('ArchiveSignWin').close();
                                            Ext.getStore('ArchiveDocumentsArchived').load();
                                        } else {
                                            Ext.Msg.alert('ERRORE', response.message);
                                        }
                                    }
                                });
                            },
                            iconCls: 'icon-text_signature',
                            text: 'Aggiorna documenti firmati'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});