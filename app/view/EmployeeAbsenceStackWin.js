/*
 * File: app/view/EmployeeAbsenceStackWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeAbsenceStackWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeAbsenceStackWin',

    requires: [
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.grid.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.selection.CheckboxModel',
        'Ext.menu.Menu',
        'Ext.menu.Item',
        'Ext.form.field.Checkbox',
        'Ext.grid.plugin.RowEditing'
    ],

    height: 550,
    id: 'EmployeeAbsenceStackWin',
    itemId: 'EmployeeAbsenceStackWin',
    minHeight: 200,
    minWidth: 700,
    width: 1100,
    title: 'Monteore e Tipi di Assenza',
    modal: true,

    layout: {
        type: 'hbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'tabpanel',
                    flex: 1,
                    border: false,
                    activeTab: 0,
                    items: [
                        {
                            xtype: 'panel',
                            border: false,
                            title: 'Monteore',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                iconCls: 'icon-sitemap',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    produceTooltip: function(r) {
                                        var type = r.raw.reset_type,
                                            tooltip = '<li>Questo monteore viene reimpostato su base ';

                                        if (type === 1 || type === 3 || type === 5) {
                                            tooltip += 'ANNUALE.</li>';
                                        } else if (type === 2 || type === 4 || type === 6) {
                                            tooltip += 'MENSILE.</li>';
                                        } else {
                                            return 'Questo monteore viene gestito MANUALMENTE.';
                                        }

                                        middle = 'totale verrà ';

                                        if (type === 1 || type === 2) {
                                            tooltip += '<li>Il ' + middle + 'REIMPOSTATO alla ';
                                        } else if (type === 3 || type === 4) {
                                            tooltip += '<li>Al ' + middle + 'AGGIUNTA la ';
                                        } else if (type === 5 || type === 6) {
                                            tooltip += '<li>Al ' + middle + 'SOTTRATTA la ';
                                        }

                                        tooltip += 'quota specifica di ogni personale.</li>';

                                        if (r.raw.reset_to_stack_id !== null) {
                                            tooltip += '<li>Il totale verrà passato sul monteore &quot;' + r.get('reset_to_stack_denomination') + '&quot;.</li>';
                                        }

                                        return '<ul>' + tooltip + '</ul>';
                                    },
                                    permissible: true,
                                    id: 'AbsenceStackGrid',
                                    itemId: 'AbsenceStackGrid',
                                    width: 350,
                                    title: 'Monteore',
                                    emptyText: 'Nessun monteore impostato',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    store: 'AbsenceStacks',
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    id: 'AbsenceStackNewBtn',
                                                    itemId: 'AbsenceStackNewBtn',
                                                    width: 65,
                                                    iconCls: 'icon-add',
                                                    text: 'Nuovo',
                                                    listeners: {
                                                        click: {
                                                            fn: me.onButtonClick,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        loadKinds: function(record) {
                                            Ext.getCmp('AbsenceKindLinkedGrid').setDisabled(true);

                                            var kinds_store = Ext.getCmp('AbsenceKindLinkedGrid').getStore(),
                                                id = record.get('id'),
                                                indexes = [];

                                            Ext.each(kinds_store.getRange(), function(value){
                                                if (value.raw.absence_stack == id){
                                                    indexes.push(value);
                                                }
                                            });

                                            Ext.getCmp('AbsenceKindLinkedGrid').getSelectionModel().select(indexes);

                                            Ext.getCmp('AbsenceKindLinkedGrid').getView().refresh();

                                            Ext.getCmp('AbsenceKindLinkedGrid').setDisabled(false);
                                        },
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onGridviewItemContextMenu,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'denomination',
                                            hideable: false,
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 70,
                                            resizable: false,
                                            hideable: false,
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.raw.recover === true) {
                                                            return 'icon-text_inverse';
                                                        } else {
                                                            return '';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        if (r.raw.recover === true) {
                                                            return 'Monteore A RECUPERO: Le assenze verranno SOMMATE agli straordinari di giornata.';
                                                        } else {
                                                            return '';
                                                        }
                                                    }
                                                },
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        var type = r.raw.reset_type;

                                                        if (type === 1 || type === 3 || type === 5) {
                                                            return 'icon-calendar_view_day';
                                                        } else if (type === 2 || type === 4 || type === 6) {
                                                            return 'icon-calendar_view_month';
                                                        } else {
                                                            return '';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        return Ext.getCmp('AbsenceStackGrid').produceTooltip(r);
                                                    }
                                                },
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        var type = r.raw.reset_type;

                                                        if (type === 1 || type === 2) {
                                                            return 'icon-control_repeat';
                                                        } else if (type === 3 || type === 4) {
                                                            return 'icon-control_add';
                                                        } else if (type === 5 || type === 6) {
                                                            return 'icon-control_remove';
                                                        } else {
                                                            return '';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        return Ext.getCmp('AbsenceStackGrid').produceTooltip(r);
                                                    }
                                                },
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.raw.reset_to_stack_id !== null) {
                                                            return 'icon-arrow_merge';
                                                        } else {
                                                            return '';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        return Ext.getCmp('AbsenceStackGrid').produceTooltip(r);
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    features: [
                                        {
                                            ftype: 'grouping',
                                            enableGroupingMenu: false,
                                            groupHeaderTpl: [
                                                'Unità: {name}'
                                            ]
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.RowModel', {
                                        mode: 'SINGLE'
                                    }),
                                    listeners: {
                                        select: {
                                            fn: me.onAbsenceStackGridSelect,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'gridpanel',
                                    permissible: true,
                                    flex: 1,
                                    weight: 1,
                                    disabled: true,
                                    id: 'AbsenceKindLinkedGrid',
                                    itemId: 'AbsenceKindLinkedGrid',
                                    title: 'Tipi di Assenza collegati al monteore selezionato',
                                    enableColumnMove: false,
                                    store: 'AbsenceKinds',
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    id: 'AbsenceKindSaveLinkBtn',
                                                    itemId: 'AbsenceKindSaveLinkBtn',
                                                    iconCls: 'icon-disk',
                                                    text: 'Salva abbinamenti',
                                                    listeners: {
                                                        click: {
                                                            fn: me.onButtonClick1,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        getRowClass: function(record, rowIndex, rowParams, store) {
                                            var monteore = Ext.getCmp('AbsenceStackGrid').getSelectionModel().getSelection()[0];

                                            if (typeof monteore !== 'undefined') {
                                                var id = parseInt(monteore.get('id'));

                                                if (record.get('absence_stack') != id && record.get('absence_stack') !== 0) {
                                                    return 'kind-disable-linked';
                                                }
                                            }

                                            return '';
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            width: 81,
                                            align: 'center',
                                            dataIndex: 'code',
                                            hideable: false,
                                            text: 'Codice'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'description',
                                            hideable: false,
                                            text: 'Descrizione',
                                            flex: 1
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                                        ignoreRightMouseSelection: true,
                                        checkOnly: true,
                                        showHeaderCheckbox: false,
                                        listeners: {
                                            beforeselect: {
                                                fn: me.onCheckboxModelBeforeSelect,
                                                scope: me
                                            }
                                        }
                                    })
                                },
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    hidden: true,
                                    id: 'AbsenceStackEditMn',
                                    itemId: 'AbsenceStackEditMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            id: 'ContextAbsenceStackEdit',
                                            itemId: 'ContextAbsenceStackEdit',
                                            iconCls: 'icon-pencil',
                                            text: 'Modifica',
                                            listeners: {
                                                click: {
                                                    fn: me.onMenuitemClick42,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'menuitem',
                                            id: 'ContextAbsenceStackDelete',
                                            itemId: 'ContextAbsenceStackDelete',
                                            iconCls: 'icon-cancel',
                                            text: 'Elimina',
                                            listeners: {
                                                click: {
                                                    fn: me.onMenuitemClick52,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            border: false,
                            title: 'Tipi di Assenza',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            tabConfig: {
                                xtype: 'tab',
                                iconCls: 'icon-page_white_stack',
                                textAlign: 'left'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    permissible: true,
                                    flex: 1,
                                    border: false,
                                    id: 'AbsenceKindGrid',
                                    itemId: 'AbsenceKindGrid',
                                    header: false,
                                    emptyText: 'Nessun tipo di assenza presente.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    store: 'AbsenceKinds',
                                    viewConfig: {
                                        preserveScrollOnRefresh: true,
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onViewItemContextMenu,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            width: 81,
                                            align: 'center',
                                            dataIndex: 'code',
                                            hideable: false,
                                            text: 'Codice'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'description',
                                            hideable: false,
                                            text: 'Descrizione',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value === true) {
                                                    return '<img src="./resources/icons/accept.png" />';
                                                }
                                                return '';
                                            },
                                            resizable: false,
                                            defaultWidth: 65,
                                            sortable: false,
                                            align: 'center',
                                            dataIndex: 'calc_ferials',
                                            text: 'Calcolo Feriali',
                                            tooltip: 'Le assenze verranno conteggiate anche nei giorni feriali in cui non è presente un orario.',
                                            editor: {
                                                xtype: 'checkboxfield'
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                icon = '';// '/resources/icons/delete.png';
                                                if (value === true) {
                                                    icon = './resources/icons/accept.png';
                                                }
                                                return '<img src="' + icon + '" />';
                                            },
                                            resizable: false,
                                            defaultWidth: 65,
                                            sortable: false,
                                            align: 'center',
                                            dataIndex: 'calc_festivities',
                                            text: 'Calcolo Festivi',
                                            tooltip: 'Le assenze verranno conteggiate anche nei festivi od in presenza di una festività.',
                                            editor: {
                                                xtype: 'checkboxfield'
                                            }
                                        }
                                    ],
                                    plugins: [
                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                            blocked: true,
                                            listeners: {
                                                edit: {
                                                    fn: me.onRowEditingEdit1,
                                                    scope: me
                                                },
                                                beforeedit: {
                                                    fn: me.onRowEditingBeforeEdit,
                                                    scope: me
                                                }
                                            }
                                        })
                                    ]
                                },
                                {
                                    xtype: 'menu',
                                    permissible: true,
                                    hidden: true,
                                    id: 'AbsenceKindEditMn',
                                    itemId: 'AbsenceKindEditMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            id: 'ContextAbsenceKindEdit',
                                            itemId: 'ContextAbsenceKindEdit',
                                            iconCls: 'icon-pencil',
                                            text: 'Modifica',
                                            listeners: {
                                                click: {
                                                    fn: me.onMenuitemClick421,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.widget('EmployeeAbsenceStackEditWin').show();
    },

    onGridviewItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('AbsenceStackEditMn').showAt([newX,newY]);
    },

    onAbsenceStackGridSelect: function(rowmodel, record, index, eOpts) {
        Ext.getCmp('AbsenceStackGrid').getView().loadKinds(record);
    },

    onButtonClick1: function(button, e, eOpts) {
        var abs_kinds = Ext.getCmp('AbsenceKindLinkedGrid').getSelectionModel().getSelection();

        var abk = [];

        abs_kinds.forEach(function(el){
            abk = abk.concat(el.get('code'));
        });


        var record = Ext.getCmp('AbsenceStackGrid').getSelectionModel().getSelection()[0];
        Ext.getCmp('AbsenceKindLinkedGrid').setLoading();
        Ext.Ajax.request({
            url: '/mc2/applications/employees/absence_kinds/link_abs_stack.php',
            params: {
                absence_kinds: Ext.encode(abk),
                absence_stack: record.get('id')
            },
            success: function(){
                Ext.getCmp('AbsenceKindLinkedGrid').getStore().load({
                    callback: function() {
                        Ext.getCmp('AbsenceKindLinkedGrid').setLoading(false);
                        Ext.getCmp('AbsenceStackGrid').getView().loadKinds(record);
                        mc2ui.app.showNotifySave();
                    }
                });
            }
        });
    },

    onCheckboxModelBeforeSelect: function(rowmodel, record, index, eOpts) {
        if (!Ext.getCmp('AbsenceKindLinkedGrid').isDisabled()) {
            var id = parseInt(Ext.getCmp('AbsenceStackGrid').getSelectionModel().getSelection()[0].get('id'));

            if (record.get('absence_stack') != id && record.get('absence_stack') !== 0) {
                return false;
            }
        }
    },

    onMenuitemClick42: function(item, e, eOpts) {
        Ext.widget('EmployeeAbsenceStackEditWin').show();

        var form = Ext.getCmp('EmployeeAbsenceStackEditForm').getForm(),
            pg = Ext.getCmp('AbsenceStackGrid'),
            record = pg.getSelectionModel().getSelection()[0];

        form.reset();
        form.loadRecord(record);
    },

    onMenuitemClick52: function(item, e, eOpts) {
        var record = Ext.getCmp('AbsenceStackGrid').getSelectionModel().getSelection()[0];

        Ext.Msg.show({
            title: record.get('denomination'),
            msg: 'Sei sicuro di voler eliminare questo monteore?',
            buttons: Ext.Msg.YESNO,
            fn: function(r){
                if( r == 'yes' ){
                    store = Ext.getStore('AbsenceStacks');
                    store.remove(record);
                    store.sync();
                    Ext.getCmp('AbsenceKindLinkedGrid').setDisabled(true);
                    Ext.getCmp('AbsenceKindLinkedGrid').getSelectionModel().deselectAll();
                }
            }
        });
    },

    onViewItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('AbsenceKindEditMn').showAt([newX,newY]);
    },

    onRowEditingEdit1: function(editor, context, eOpts) {
        var kinds_store = Ext.getCmp('AbsenceKindGrid').getStore();

        kinds_store.save();
    },

    onRowEditingBeforeEdit: function(editor, context, eOpts) {
        var pg = Ext.getCmp('AbsenceKindGrid');

        return !pg.getPlugin().blocked;
    },

    onMenuitemClick421: function(item, e, eOpts) {
        var pg = Ext.getCmp('AbsenceKindGrid'),
            record = pg.getSelectionModel().getSelection()[0];

        pg.getPlugin().blocked = false;
        pg.getPlugin().startEdit(record);
        pg.getPlugin().blocked = true;
    }

});