/*
 * File: app/view/ArchiveDocumentCheckWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDocumentCheckWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDocumentCheckWin',

    requires: [
        'Ext.form.Panel',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 257,
    id: 'ArchiveDocumentCheckWin',
    width: 240,
    layout: 'fit',
    title: 'Presa visione',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    autoScroll: true,
                    title: '',
                    items: [
                        {
                            xtype: 'gridpanel',
                            height: 300,
                            id: 'ArchiveDocumentCheckGrid',
                            itemId: 'ArchiveDocumentCheckGrid',
                            autoScroll: true,
                            title: '',
                            hideHeaders: true,
                            store: 'Assignees',
                            columns: [
                                {
                                    xtype: 'actioncolumn',
                                    width: 30,
                                    dataIndex: 'type',
                                    items: [
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                var icon = '';

                                                if(v === 'U') {
                                                    icon = 'user';
                                                } else if (v === 'O') {
                                                    icon = 'building';
                                                }
                                                return 'icon-' + icon;
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'name',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            selModel: Ext.create('Ext.selection.CheckboxModel', {
                                showHeaderCheckbox: false
                            })
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var ids = [],
                                    objs = Ext.getCmp('ArchiveDocumentCheckGrid').getSelectionModel().getSelection(),
                                    docId = Ext.getCmp('ArchiveDocumentCheckWin').record.get('id');

                                Ext.each(objs, function(obj){
                                    ids.push(obj.get('id'));
                                });


                                Ext.Ajax.request({
                                    url: '/mc2-api/archive/document/' + docId,
                                    method: 'PUT',
                                    params: {
                                        check_document_users: Ext.encode(ids)
                                    },
                                    success: function() {
                                        Ext.getCmp('ArchiveDocumentCheckWin').close();
                                        Ext.Msg.alert('SUCCESSO', 'Flusso inviato in presa visione correttamente');
                                        Ext.getStore('ArchiveDocumentsUser').load();
                                    }
                                });

                            },
                            text: 'Invia'
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onArchiveDocumentCheckWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onArchiveDocumentCheckWinClose: function(panel, eOpts) {
        Ext.getStore('Assignees').removeFilter();
    }

});