
Ext.define('mc2ui.view.MailAccountPnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.MailAccountPnl',

    requires: [
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.form.field.Hidden',
        'Ext.form.FieldSet',
        'Ext.form.field.ComboBox',
        'Ext.grid.Panel',
        'Ext.grid.column.Number',
        'Ext.form.field.Checkbox',
        'Ext.grid.column.Action',
        'Ext.grid.View',
        'Ext.toolbar.Spacer',
        'Ext.form.Label',
        'Ext.grid.plugin.RowEditing',
        'Ext.menu.Menu',
        'Ext.menu.Item',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.Img',
        'Ext.form.field.File',
        'Ext.form.field.Number',
        'Ext.form.field.HtmlEditor'
    ],

    border: false,
    hidden: true,
    id: 'MailAccountPnl',
    itemId: 'MailAccountPnl',
    layout: 'fit',
    titleAlign: 'center',

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            listeners: {
                boxready: {
                    fn: me.onMailAccountPnlBoxReady,
                    scope: me
                },
            },

            dockedItems: [

                {
                    xtype: 'toolbar',
                    permissible: true,
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.getCmp('MailAccountPnl').openSendMailWin();
                            },
                            id: 'ArchiveMailWriteMailBtn',
                            itemId: 'ArchiveMailWriteMailBtn',
                            enableToggle: true,
                            iconCls: 'icon-pencil',
                            text: 'Scrivi'
                        },
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                Ext.widget('MailingListWin').show();
                            },
                            iconCls: 'icon-group',
                            text: 'Mailing List'
                        },
                    ]
                },
            ],

            items: [
                {
                    xtype: 'tabpanel',
                    permissible: true,
                    id: 'ArchiveIncomingPnl',
                    layout: 'fit',
                    region: 'center',
                    split: true,
                    activeTab: 0,
                    items: [
                        {
                            xtype: 'gridpanel',
                            countMail: function () {
                                Ext.Ajax.request({
                                    url: '/mc2-api/archive/mail/mail/count',
                                    params: {
                                        is_sent: 0
                                    },
                                    success: function (res) {
                                        var response = Ext.decode(res.responseText);
                                        Ext.getCmp('ArchiveMailPnl').setTitle('<b>In entrata (' + response.results + ')</b>');
                                    }
                                });
                            },
                            id: 'ArchiveMailPnl',
                            itemId: 'ArchiveMailPnl',
                            title: '<b>In entrata  </b>',
                            store: 'ArchiveMails',
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    width: 271,
                                    dataIndex: 'from',
                                    text: 'Da'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        message = record.get('message_text');
                                        if (message.length > 100)
                                            message = message.substring(0, 100);
                                        return '<b>' + value + '</b> - <span style="color:#888;">' + message + ' ...</span>';
                                    },
                                    dataIndex: 'subject',
                                    text: 'Oggetto',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'account_text',
                                    text: 'Account'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        if (value > 0) {
                                            return '<img src="./resources/icons/attach.png">';
                                        }
                                        return '';
                                    },
                                    width: 60,
                                    align: 'center',
                                    dataIndex: 'attachments',
                                    text: 'Allegato'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var currentTime = new Date(),
                                            month = currentTime.getMonth() + 1,
                                            day = currentTime.getDate(),
                                            year = currentTime.getFullYear(),
                                            today = day + '/' + month + '/' + year,
                                            date = Ext.Date.format(value, 'j/n/Y');

                                        if (today == date) {
                                            date = Ext.Date.format(value, 'G:i');
                                        } else {
                                            date = Ext.Date.format(value, 'd/m/Y G:i');
                                        }

                                        return date;
                                    },
                                    align: 'right',
                                    dataIndex: 'date',
                                    text: 'Date'
                                },
                                {
                                    xtype: 'actioncolumn',
                                    id: 'ArchiveActionArchivedCol',
                                    itemId: 'ArchiveActionArchivedCol',
                                    width: 25,
                                    align: 'center',
                                    items: [
                                        {
                                            handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                var store = Ext.getStore('ArchiveMails');

                                                record.set('deleted', true);

                                                store.sync({
                                                    callback: function () {
                                                        Ext.getCmp('ArchiveMailPnl').countMail();
                                                        store.load();
                                                    }
                                                });


                                            },
                                            iconCls: 'icon-package_down',
                                            tooltip: 'Sposta nel cestino'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 25,
                                    align: 'center',
                                    items: [
                                        {
                                            getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('documents') > 0) {
                                                    return 'icon-arrow_switch';
                                                }
                                            },
                                            tooltip: 'E\' stato generato un flusso da questa mail'
                                        }
                                    ]
                                }
                            ],
                            viewConfig: {
                                getRowClass: function (record, rowIndex, rowParams, store) {
                                    if (record.get('deleted')) {
                                        return 'archived-mail';
                                    }
                                },
                                listeners: {
                                    itemdblclick: {
                                        fn: me.onViewItemDblClick,
                                        scope: me
                                    },
                                    itemcontextmenu: {
                                        fn: function (dataview, record, item, index, e, eOpts) {
                                            Ext.getCmp('ArchivePnl').generateAssignMn(record, ['M', 'A']).showAt(e.xy[0], e.xy[1]);
                                        },
                                        scope: me
                                    }
                                }
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: 3,
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ArchiveMailSearchTxt',
                                            fieldLabel: '',
                                            name: 'query',
                                            emptyText: 'Cerca ...',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                var col = Ext.getCmp('ArchiveActionArchivedCol');

                                                Ext.getStore('ArchiveMails').load();

                                                if (button.pressed) {
                                                    button.setIconCls('icon-mail');
                                                    button.setText('In arrivo');
                                                    col.hide();
                                                } else {
                                                    button.setIconCls('icon-package_down');
                                                    button.setText('Cestino');
                                                    col.show();
                                                }
                                            },
                                            id: 'ArchiveMailArchivedBtn',
                                            itemId: 'ArchiveMailArchivedBtn',
                                            enableToggle: true,
                                            iconCls: 'icon-package_down',
                                            text: 'Cestino'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'pagingtoolbar',
                                    dock: 'bottom',
                                    id: 'ArchiveMailPagBar',
                                    width: 360,
                                    displayInfo: true,
                                    store: 'ArchiveMails'
                                }
                            ]
                        },
                        {
                            xtype: 'gridpanel',
                            countMail: function () {
                                Ext.Ajax.request({
                                    url: '/mc2-api/archive/mail/mail/count',
                                    params: {
                                        is_sent: 1
                                    },
                                    success: function (res) {
                                        var response = Ext.decode(res.responseText);
                                        Ext.getCmp('ArchiveMailSentPnl').setTitle('<b>Inviata (' + response.results + ')</b>');
                                    }
                                });
                            },
                            id: 'ArchiveMailSentPnl',
                            itemId: 'ArchiveMailSentPnl',
                            title: '<b>Inviata</b>',
                            store: 'ArchiveSentMails',
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    width: 271,
                                    dataIndex: 'to',
                                    text: 'A'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        message = record.get('message_text');
                                        if (message.length > 100)
                                            message = message.substring(0, 100);
                                        return '<b>' + value + '</b> - <span style="color:#888;">' + message + ' ...</span>';
                                    },
                                    dataIndex: 'subject',
                                    text: 'Oggetto',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'account_text',
                                    text: 'Account'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        if (value > 0) {
                                            return '<img src="./resources/icons/attach.png">';
                                        }
                                        return '';
                                    },
                                    width: 60,
                                    align: 'center',
                                    dataIndex: 'attachments',
                                    text: 'Allegato'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                        var currentTime = new Date(),
                                            month = currentTime.getMonth() + 1,
                                            day = currentTime.getDate(),
                                            year = currentTime.getFullYear(),
                                            today = day + '/' + month + '/' + year,
                                            date = Ext.Date.format(value, 'j/n/Y');

                                        if (today == date) {
                                            date = Ext.Date.format(value, 'G:i');
                                        } else {
                                            date = Ext.Date.format(value, 'd/m/Y G:i');
                                        }

                                        return date;
                                    },
                                    align: 'right',
                                    dataIndex: 'date',
                                    text: 'Date'
                                },
                                /*{
                                    xtype: 'actioncolumn',
                                    id: 'ArchiveActionArchivedSentCol',
                                    itemId: 'ArchiveActionArchivedSentCol',
                                    width: 25,
                                    align: 'center',
                                    items: [
                                        {
                                            handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                var store = Ext.getStore('ArchiveMails');

                                                record.set('deleted', true);

                                                store.sync({
                                                    callback: function () {
                                                        Ext.getCmp('ArchiveMailSentPnl').countMail();
                                                        store.load();
                                                    }
                                                });


                                            },
                                            iconCls: 'icon-package_down',
                                            tooltip: 'Sposta nel cestino'
                                        }
                                    ]
                                },*/
                                {
                                    xtype: 'actioncolumn',
                                    width: 25,
                                    align: 'center',
                                    items: [
                                        {
                                            getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('documents') > 0) {
                                                    return 'icon-arrow_switch';
                                                }
                                            },
                                            tooltip: 'E\' stato generato un flusso da questa mail'
                                        }
                                    ]
                                }
                            ],
                            viewConfig: {
                                listeners: {
                                    itemdblclick: {
                                        fn: me.onViewItemDblClick,
                                        scope: me
                                    },
                                    itemcontextmenu: {
                                        fn: function (dataview, record, item, index, e, eOpts) {
                                            Ext.getCmp('ArchivePnl').generateAssignMn(record, ['M', 'A']).showAt(e.xy[0], e.xy[1]);
                                        },
                                        scope: me
                                    }
                                }
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: 3,
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ArchiveMailSentSearchTxt',
                                            fieldLabel: '',
                                            name: 'query',
                                            emptyText: 'Cerca ...',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextSentfieldChange,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        },
                                    ]
                                },
                                {
                                    xtype: 'pagingtoolbar',
                                    dock: 'bottom',
                                    id: 'ArchiveMailSentPagBar',
                                    width: 360,
                                    displayInfo: true,
                                    store: 'ArchiveSentMails'
                                }
                            ]

                        },
                    ]
                },
            ]
        });

        me.callParent(arguments);
    },

    onMailAccountPnlBoxReady: function (panel, eOpts) {
        Ext.getStore('ArchiveMails').load();
        Ext.getStore('ArchiveSentMails').load();
        Ext.getCmp('ArchiveMailPnl').countMail();
        Ext.getCmp('ArchiveMailSentPnl').countMail();
        Ext.getStore('Assignees').load();
    },

    onTextfieldChange: function (field, newValue, oldValue, eOpts) {
        var params = {};
        if (newValue) {
            //Ext.getCmp('ArchiveMailPagBar').disable();
            params = {
                query: newValue,
                is_sent: 0
                //limit: 0,
                //start: 0
            };

        } else {
            Ext.getCmp('ArchiveMailPagBar').enable();
        }

        Ext.getStore('ArchiveMails').load({
            params: params
        });
    },

    onTextSentfieldChange: function (field, newValue, oldValue, eOpts) {
        var params = {};
        if (newValue) {
            //Ext.getCmp('ArchiveMailPagBar').disable();
            params = {
                query: newValue,
                is_sent: 1
                //limit: 0,
                //start: 0
            };

        } else {
            Ext.getCmp('ArchiveMailSentPagBar').enable();
        }

        Ext.getStore('ArchiveSentMails').load({
            params: params
        });
    },

    onViewItemDblClick: function (dataview, record, item, index, e, eOpts) {
        // Ext.getCmp('ArchiveMailPnl').getSelectionModel().select(index);
        Ext.getCmp('MailAccountPnl').viewMail(record);

    },

    viewMail: function (record) {
        var av = Ext.widget('ArchiveMailViewWin').show();
        av.setHeight(window.outerHeight / 100 * 70);
        av.setWidth(window.outerWidth / 100 * 80);
        av.center();

        Ext.Ajax.request({
            url: Ext.getStore('ArchiveMails').getProxy().url + '/' + record.get('id'),
            success: function (res) {
                var r = Ext.decode(res.responseText);

                Ext.getCmp('ArchiveMailViewWin').setTitle(r.results.subject);

                Ext.getCmp('ArchiveMailFrm').getForm().setValues(r.results);
                Ext.getCmp('MessageMailCnt').getEl().setHTML(r.results.message);

                Ext.getStore('ArchiveMailAttachments').load({
                    params: {
                        account: r.results.account,
                        mail: r.results.id
                    }
                });

            }
        });
        // Ext.getCmp('ArchiveCreateFlowBtn').setText('Crea flusso dalla mail');


        var menu = new Ext.Button({
            xtype: 'button',
            text: 'Gestisci',
            iconCls: 'icon-sitemap',
            menu: Ext.getCmp('ArchivePnl').generateAssignMn(record, ['A', 'M'])
        });
        Ext.getCmp("ArchiveMailViewTb").add(menu);
    },

    openSendMailWin: function (id, type) {

        var isNewMail = (id === undefined || id === null || id === 0);

        Ext.widget('ArchiveMailSendWin').show();
        Ext.getStore('ArchiveDocumentFiles').removeAll();
        // Set ArchiveMailSendWin size and center it
        Ext.getCmp('ArchiveMailSendWin').setWidth(Ext.getBody().getViewSize().width * 0.5);
        Ext.getCmp('ArchiveMailSendWin').setHeight(Ext.getBody().getViewSize().height * 0.8);
        Ext.getCmp('ArchiveMailSendWin').center();

        if (isNewMail) {
            Ext.getCmp('ArchiveMailSendFilesCnt').hide()
            return;
        }

        Ext.Ajax.request({
            url: '/mc2-api/archive/mail/mail/' + id.toString(),
            method: 'GET',
            success: function (response) {
                var res = Ext.decode(response.responseText);
                if (res.success === true) {
                    var subject = 'Fwd: ';
                    // Add from user
                    if (type === 'reply') {
                        Ext.getStore('ArchiveMailSendContact').add({
                            type: 'U',
                            email: res.results.from_email,
                            name: res.results.from_name,
                        });
                        subject = 'Re: ';
                    }
                    subject += res.results.subject;

                    // Add mail data
                    var data = {
                        subject: subject,
                        mail_id: res.results.id,
                        mail_account_id: res.results.account,
                        message: '<br /><br /><br />----------------------' + res.results.message
                    };
                    console.log(res.results.attachments);
                    Ext.each(res.results.attachments, function (attachment) {
                        Ext.getStore('ArchiveDocumentFiles').add({
                            id: attachment.id,
                            filename: attachment.name,
                        });

                    });


                    Ext.getCmp('SendMailFrm').getForm().setValues(data);



                } else {
                    Ext.Msg.alert('ERRORE', 'Nessun file associato alla mail');
                }
            },

        });
    }

});