/*
 * File: app/view/InvoiceEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.InvoiceEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.InvoiceEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.File',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.form.field.ComboBox',
        'Ext.form.FieldSet',
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.column.Action'
    ],

    id: 'InvoiceEditWin',
    width: 400,
    layout: 'fit',
    title: 'Fattura in ingresso',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'InvoiceEditFrm',
                    bodyPadding: 10,
                    title: '',
                    method: 'POST',
                    url: '/mc2-api/ccp/pinvoice',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'filefield',
                            id: 'InvoiceEditFileUpload',
                            fieldLabel: '',
                            name: 'file[]',
                            emptyText: 'Carica xml ...',
                            buttonText: 'Sfoglia ...'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            id: 'InvoiceEditManualCnt',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'numberfield',
                                    flex: 1,
                                    fieldLabel: 'Numero',
                                    name: 'number',
                                    allowBlank: false,
                                    hideTrigger: true
                                },
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    fieldLabel: 'Data',
                                    name: 'date',
                                    allowBlank: false,
                                    format: 'd/m/Y',
                                    submitFormat: 'c'
                                },
                                {
                                    xtype: 'combobox',
                                    fieldLabel: 'Fornitore',
                                    autoSelect: false,
                                    displayField: 'description',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'Suppliers',
                                    typeAhead: true,
                                    valueField: 'fiscal_code',
                                    listeners: {
                                        change: {
                                            fn: me.onComboboxChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Nome',
                                    name: 'name'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Codice Fiscale',
                                    name: 'fiscal_code',
                                    allowBlank: false
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Partita IVA',
                                    name: 'vat_number'
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Sede',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            fieldLabel: 'Indirizzo',
                                            name: 'address'
                                        },
                                        {
                                            xtype: 'combobox',
                                            fieldLabel: 'Città',
                                            name: 'city_id',
                                            displayField: 'description',
                                            queryMode: 'local',
                                            store: 'CoreCities',
                                            typeAhead: true,
                                            valueField: 'city_id',
                                            listeners: {
                                                change: {
                                                    fn: me.onComboboxChange1,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            fieldLabel: 'Comune',
                                            name: 'city_name'
                                        },
                                        {
                                            xtype: 'textfield',
                                            fieldLabel: 'Provincia',
                                            name: 'city_province'
                                        },
                                        {
                                            xtype: 'textfield',
                                            fieldLabel: 'CAP',
                                            name: 'city_postal_code'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'numberfield',
                                    fieldLabel: 'Totale',
                                    name: 'total',
                                    allowBlank: false,
                                    hideTrigger: true
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'InvoiceExpirationGrd',
                                    minHeight: 200,
                                    title: 'Scadenze',
                                    store: 'InvoiceExpirations',
                                    columns: [
                                        {
                                            xtype: 'datecolumn',
                                            dataIndex: 'date',
                                            text: 'Data',
                                            flex: 1,
                                            format: 'd/m/Y',
                                            editor: {
                                                xtype: 'datefield',
                                                format: 'd/m/Y',
                                                submitFormat: 'c'
                                            }
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 50,
                                            align: 'center',
                                            iconCls: 'icon-delete',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.getStore('InvoiceExpirations').remove(record);
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'tbspacer',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getStore('InvoiceExpirations').add({date:new Date()});
                                                    },
                                                    iconCls: 'icon-add',
                                                    text: ''
                                                }
                                            ]
                                        }
                                    ],
                                    plugins: [
                                        Ext.create('Ext.grid.plugin.RowEditing', {

                                        })
                                    ]
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'bottom',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var expirations = [];
                                        Ext.each(Ext.getStore('InvoiceExpirations').getRange(), function(expiration){
                                            expirations.push({expiration:expiration.get('date')});
                                        });

                                        Ext.getCmp('InvoiceEditFrm').getForm().submit({
                                            params: {
                                                expirations: Ext.encode(expirations)
                                            },
                                            success: function(f, r) {
                                                Ext.getStore('Invoices').load();
                                                Ext.getCmp('InvoiceEditWin').close();
                                            },
                                            failure: function (f, r){
                                                var res = Ext.decode(r.response.responseText);
                                                Ext.Msg.alert('ERRORE', res.message);
                                            }
                                        });

                                    },
                                    text: 'Salva'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onInvoiceEditWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {
        if(newValue !== '') {
            Ext.Ajax.request({
                method: 'GET',
                url: '/mc2-api/ccp/suppliers/' + newValue,
                success: function (res) {
                    var r = Ext.decode(res.responseText);
                    Ext.getCmp('InvoiceEditFrm').getForm().setValues(r.results);
                }
            });
        }
    },

    onComboboxChange1: function(field, newValue, oldValue, eOpts) {
        if(newValue !== '') {
            Ext.Ajax.request({
                method: 'GET',
                url: '/mc2/applications/core/cities/read.php?city_id=' + newValue,
                success: function (res) {
                    var r = Ext.decode(res.responseText),
                        cityData={
                            city_name: r.results.description,
                            city_province: r.results.province,
                            city_postal_code: r.results.zip_code,
                        };
                    Ext.getCmp('InvoiceEditFrm').getForm().setValues(cityData);
                }
            });
        }
    },

    onInvoiceEditWinShow: function(component, eOpts) {
        var ss = Ext.getStore('Suppliers');
        ss.load({callback:function(){
            ss.insert(0, {fiscal_code:'', name:'---'});
        }});
        Ext.getStore('InvoiceExpirations').removeAll();
    }

});