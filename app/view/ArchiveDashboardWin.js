/*
 * File: app/view/ArchiveDashboardWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDashboardWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDashboardWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.grid.View'
    ],

    height: 363,
    width: 689,
    layout: 'fit',
    title: 'Dettaglio',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    forceAction: function(operation, record) {
                        Ext.Msg.show({
                            title : 'ATTENZIONE',
                            msg : "L'operazione da archivio è concessa a tutti. Nel caso in cui tu non sia l'utente abilitato, sarà registrato il fatto che l'operazione è stata fatta da un'altro utente",
                            buttons : Ext.Msg.YESCANCEL,
                            buttonText :
                            {
                                yes : 'Conferma',
                                cancel : 'Annulla'
                            },
                            fn : function(buttonValue, inputText, showConfig){
                                if(buttonValue == 'yes'){
                                    switch(operation){
                                        case 'PROTOCOL':
                                        var date = record.get('action_protocol_date');

                                        if (date === null) {
                                            Ext.widget('ProtocolProtocolNewWin').show();
                                            Ext.getCmp('ProtocolNewDate').setValue(new Date());
                                            Ext.getStore('ProtocolLinkedCorrespondentsForm').removeAll();
                                            Ext.getStore('ProtocolLinkedProtocolsForm').removeAll();
                                            Ext.getStore('ProtocolLinkedDocumentsForm').removeAll();
                                            // Ext.getStore('ProtocolLinkedDocumentsForm').insert(0, record);
                                            Ext.getCmp('ProtocolNewLinkedDocumentsGrid').getView().disable();
                                            Ext.getCmp('ProtocolDocumentToolBar').disable();

                                            Ext.Ajax.request({
                                                method: 'GET',
                                                url: '/mc2-api/archive/document_file',
                                                params:{
                                                    document: record.get('id')
                                                },
                                                success:function(res){
                                                    var r = Ext.decode(res.responseText);
                                                    Ext.getStore('ProtocolLinkedDocumentsForm').add(r.results);
                                                }
                                            });
                                        }
                                        break;
                                        case 'ALBO':
                                        var date = record.get('action_albo_date');

                                        if (date === null) {
                                            Ext.widget('AlboPublicationEditWin').show();
                                            Ext.getStore('AlboLinkedDocumentsForm').removeAll();

                                            Ext.getCmp('AlboPublicationEditLinkedTlb').disable();

                                            Ext.getStore('ArchiveDocumentFiles').load({
                                                params: {
                                                    document: record.get('id')
                                                },
                                                callback: function(res){
                                                    Ext.getStore('AlboLinkedDocumentsForm').add(res);
                                                    Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').getView().disable();
                                                }
                                            });
                                        }
                                        break;
                                        case 'TRASPARENZA':
                                        var date = record.get('action_trasparenza_date');

                                        if (date === null) {
                                            Ext.widget('TrasparenzaVoicePickerWin').show();
                                            Ext.getStore('ArchiveDocumentFiles').load({
                                                params: {
                                                    document: record.get('id')
                                                }
                                            });
                                        }
                                        break;
                                        case 'ARCHIVIO':
                                        var date = record.get('action_archive_date');

                                        if (date === null) {
                                            Ext.Msg.confirm('CONSERVAZIONE DIGITALE','Tutti i documenti di questo flusso verranno caricati in conservazione. Confermare?',
                                            function(btnText, sInput){
                                                if(btnText === 'yes'){
                                                    var dd = new Date();
                                                    record.set('action_archive_date', dd);
                                                    record.set('action_archive', true);
                                                }
                                            });
                                        }
                                        break;
                                    }
                                }
                            },
                            icon : Ext.Msg.WARNING
                        });
                    },
                    border: false,
                    id: 'ArchiveDocumentsGrid1',
                    itemId: 'ArchiveDocumentsGrid1',
                    header: false,
                    iconCls: 'icon-folder_page',
                    emptyText: 'Nessun documento archiviato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ArchiveDashboard',
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                var action = r.get('action_archive'),
                                    date = r.get('action_archive_date');

                                if (action) {
                                    if (date !== null) {
                                        return 'Archiviato in conservazione il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                    } else {
                                        return 'Da archiviare';
                                    }
                                }
                            },
                            width: 20,
                            text: 'C',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_archive'),
                                            date = r.get('action_archive_date');

                                        if (action) {
                                            if (date !== null) {
                                                return 'icon-accept';
                                            } else {
                                                return 'icon-control_blank';
                                            }
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            dataIndex: 'action_sign',
                            hideable: false,
                            text: 'F',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_sign'),
                                            date = r.get('action_sign_date');

                                        if (action) {
                                            if (date !== null) {
                                                return 'icon-accept';
                                            } else {
                                                return 'icon-control_blank';
                                            }
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_sign'),
                                            date = r.get('action_sign_date');

                                        if (action) {
                                            if (date !== null) {
                                                return 'Firmato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                            } else {
                                                return 'Da Firmare';
                                            }
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            align: 'center',
                            hideable: false,
                            text: 'P',
                            tooltip: 'Stato protocollazione',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_protocol'),
                                            date = r.get('action_protocol_date');

                                        if (action) {
                                            if (r.get('forced_protocol')){
                                                return 'icon-accept_warning';
                                            } else if (date !== null) {
                                                return 'icon-accept';
                                            } else {
                                                return 'icon-control_blank';
                                            }
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_protocol'),
                                            date = r.get('action_protocol_date');

                                        if (action) {
                                            if (date !== null) {
                                                if(r.get('forced_protocol')){
                                                    return r.get('forced_protocol');
                                                } else {
                                                    return 'Protocollato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                }
                                            } else {
                                                return 'Da protocollare';
                                            }
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            align: 'center',
                            hideable: false,
                            text: 'A',
                            tooltip: 'Stato pubblicazione su Albo',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_albo'),
                                            date = r.get('action_albo_date');

                                        if (action) {
                                            if (r.get('forced_albo')){
                                                return 'icon-accept_warning';
                                            } else if (date !== null) {
                                                return 'icon-accept';
                                            } else {
                                                return 'icon-control_blank';
                                            }
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_albo'),
                                            date = r.get('action_albo_date');

                                        if (action) {
                                            if (date !== null){
                                                if(r.get('forced_albo')){
                                                    return r.get('forced_albo');
                                                } else {
                                                    return 'Pubblicato su Albo Pretorio il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                }
                                            } else {
                                                return 'Da pubblicare su Albo';
                                            }
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            align: 'center',
                            hideable: false,
                            text: 'T',
                            tooltip: 'Stato abbinamento a Trasparenza',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_trasparenza'),
                                            date = r.get('action_trasparenza_date');

                                        if (action) {
                                            if (r.get('forced_trasparenza')){
                                                return 'icon-accept_warning';
                                            } else if (date !== null) {
                                                return 'icon-accept';
                                            } else {
                                                return 'icon-control_blank';
                                            }
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_trasparenza'),
                                            date = r.get('action_trasparenza_date');

                                        if (action) {
                                            if (date !== null) {
                                                if(r.get('forced_trasparenza')){
                                                    return r.get('forced_trasparenza');
                                                } else {
                                                    return 'Allegato a Trasparenza il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                                }
                                            } else {
                                                return 'Da allegare a Trasparenza';
                                            }
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if (record.get('to_check_users') == 0) {
                                    return '';
                                }
                                return record.get('checked_users') + '/' + record.get('to_check_users');
                            },
                            width: 80,
                            align: 'center',
                            dataIndex: 'to_check_users',
                            text: 'Presa visione'
                        },
                        {
                            xtype: 'gridcolumn',
                            align: 'center',
                            dataIndex: 'class_name',
                            text: 'Tipo di flusso'
                        },
                        {
                            xtype: 'datecolumn',
                            width: 109,
                            sortable: true,
                            align: 'center',
                            dataIndex: 'upload_date',
                            hideable: false,
                            text: 'Data caricamento',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'datecolumn',
                            width: 109,
                            sortable: true,
                            align: 'center',
                            dataIndex: 'expiration_date',
                            hideable: false,
                            text: 'Scadenza',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'short_description',
                            text: 'Descrizione breve',
                            flex: 1
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 50,
                            align: 'center',
                            hideable: false,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if(r.get('mail') > 0){
                                            return 'icon-mail';
                                        }
                                        return '';
                                    }
                                }
                            ]
                        }
                    ],
                    viewConfig: {
                        getRowClass: function(record, rowIndex, rowParams, store) {
                            if (record.get('completed')) {
                                return 'archive-completed';
                            }
                            return '';
                        }
                    },
                    listeners: {
                        itemdblclick: {
                            fn: me.onArchiveDocumentsGridItemDblClick1,
                            scope: me
                        }
                    }
                }
            ]
        });

        me.callParent(arguments);
    },

    onArchiveDocumentsGridItemDblClick1: function(dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').viewDocument(record);
    }

});