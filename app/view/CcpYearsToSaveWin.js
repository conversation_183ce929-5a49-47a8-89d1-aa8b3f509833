/*
 * File: app/view/CcpYearsToSaveWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpYearsToSaveWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpYearsToSaveWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button',
        'Ext.form.Label',
        'Ext.form.FieldSet'
    ],

    id: 'CcpYearsToSaveWin',
    width: 500,
    layout: 'fit',
    title: 'Selezione anni scolastici',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpYearsToSaveFrm',
                    bodyPadding: 10,
                    title: '',
                    method: 'POST',
                    url: '/mc2-api/ccp/student_save',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'bottom',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var url = Ext.getCmp('CcpYearsToSaveFrm').getForm().url;

                                        if(url === '/mc2-api/ccp/student_save') Ext.getCmp('CcpYearsToSaveWin').saveStudent();
                                        else Ext.getCmp('CcpYearsToSaveWin').saveParents();
                                    },
                                    id: 'CcpYearsToSaveSaveBtn',
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'label',
                            id: 'CcpYearsToSaveLbl'
                        },
                        {
                            xtype: 'fieldset',
                            id: 'CcpYearsToSaveFieldSet',
                            margin: '10 0',
                            padding: 10,
                            title: 'Anni scolastici'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    createCheckBox: function(years) {
        var selectedYear = Ext.getCmp('CcpStudentAddressSchoolYear').getValue();

        // Se è stato selezionato l'anno impostiamo quello corrente (mastercom)
        if(!selectedYear) {
            selectedYear = mc2ui.app.settings.mcDb.schoolYear;
        }

        if(!years.includes(selectedYear)) {
            years.unshift(selectedYear);
        }

        Ext.each(years,  function(year) {
            var checkbox = Ext.create('Ext.form.field.Checkbox', {
                boxLabel: year,
                name: 'subject_school_years[]',
                inputValue: year,
                checked: year == selectedYear ? true : false

            });

            Ext.getCmp('CcpYearsToSaveFieldSet').add(checkbox);
        });

        var html = "Stai salvando i dati per l'anno <b>" + selectedYear + "</b>. Esistono anche altri anni scolastici nei quali";
        html += " il/i soggetto/i sono presenti. <br /> E' possibile salvare su più anni questi dati selezionadoli dall'elenco sottostante. <br/>";
        Ext.getCmp('CcpYearsToSaveLbl').getEl().setHTML(html);

        Ext.getCmp('CcpYearsToSaveWin').setHeight().center();

        // Se c'è un solo anno fra cui scegliere parte in automatico il salvataggio
        if(years.length===1) {
            Ext.getCmp('CcpYearsToSaveSaveBtn').handler();
        }

    },

    saveParents: function(mantain, not_mantain) {

        var parentsData = Ext.getCmp('CcpParentFrm').getValues(),
            studentData = Ext.getCmp('CcpStudentsAnagraficaFrm').getValues();


        parentsData.id_studente = studentData.id_studente;

        if(mantain === true) {
            parentsData.mantain=true;
        }
        if(not_mantain === true) {
            parentsData.not_mantain=true;
        }

        Ext.getCmp('CcpYearsToSaveFrm').setLoading();

        Ext.getCmp('CcpYearsToSaveFrm').getForm().submit({
            params: parentsData,
            success: function(r, response) {
                var res = Ext.decode(response.response.responseText),
                    results = res.results,
                    errors = [];

                if(res.message) {
                    Ext.MessageBox.show({
                        title: 'ATTENZIONE',
                        msg: res.message,
                        buttons: [{
                            itemId: 'ok',
                            text: 'Send',
                            ui: 'action'
                        }, {
                            itemId: 'cancel',
                            text: 'Cancel'
                        }],
                        buttonText: {
                            yes: 'Mantieni',
                            no: 'Aggiorna',
                            cancel: 'Annulla'
                        },
                        fn:function(btn) {
                            if(btn=='yes') {
                                Ext.getCmp('CcpYearsToSaveWin').saveParents(true, false);
                                return;
                            }
                            if(btn=='no') {
                                Ext.getCmp('CcpYearsToSaveWin').saveParents(false, true);
                                return;
                            }
                            if(btn=='cancel') {
                                var studentIdMc=Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id');
                                Ext.getCmp('CcpStudentsGrd').filterByStudent(studentIdMc);
                            }
                        }
                    });

                    return;
                }

                // Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].set('stato_studente_personalizzato', student.stato_studente_personalizzato);

                Ext.each(results, function(rr){
                    if(rr.result!==true) {
                        errors.push(rr.subject_school_year + ' - ' + rr.result);
                    }
                });

                if(errors.length>0) {
                    Ext.Msg.alert('ERRORE', 'Per i seguenti anni scolastici non è stato possibile salvare i dati: <br> ' + errors.join(' <br />'));
                } else {
                    mc2ui.app.showNotifySave();
                }

                Ext.getCmp('CcpYearsToSaveFrm').setLoading(false);
                Ext.getCmp('CcpYearsToSaveWin').close();


            },
            failure: function(r, response) {
                var res = Ext.decode(response.response.responseText);
                Ext.Msg.alert('ERRORE', res.message);
                Ext.getCmp('CcpYearsToSaveFrm').setLoading(false);
            }

        });

        /*



        Ext.Ajax.request({
            method: 'POST',
            url: '/mc2-api/ccp/parent_save',
            params: tot,
            success: function(r) {
                var res = Ext.decode(r.responseText),
                    student = res.results;
                if(res.success===true) {
                    if(res.message) {
                        // Ext.Msg.alert('ATTENZIONE',res.message);
                        Ext.MessageBox.show({
                            title: 'ATTENZIONE',
                            msg: res.message,
                            buttons: [{
                                itemId: 'ok',
                                text: 'Send',
                                ui: 'action'
                            }, {
                                itemId: 'cancel',
                                text: 'Cancel'
                            }],
                            buttonText: {
                                yes: 'Mantieni',
                                no: 'Aggiorna',
                                cancel: 'Annulla'
                            },
                            fn:function(btn) {
                                if(btn=='yes') {
                                    Ext.getCmp('CcpParentsPnl').saveParents(true, false);
                                    return;
                                }
                                if(btn=='no') {
                                    Ext.getCmp('CcpParentsPnl').saveParents(false, true);
                                    return;
                                }
                                if(btn=='cancel') {
                                    var studentIdMc=Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id');
                                    Ext.getCmp('CcpStudentsGrd').filterByStudent(studentIdMc);
                                }
                            }
                        });


                    } else {
                        Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].set('stato_studente_personalizzato', student.stato_studente_personalizzato);
                        Ext.Msg.alert('SUCCESSO', 'Anagrafiche salvate correttamente');
                    }
                } else {
                    Ext.Msg.alert('ERRORE', res.message);

                }

                Ext.getCmp('CcpStudentsTab').setLoading(false);
            },
            failure: function() {
                Ext.Msg.alert('ERRORE', 'I dati potrebbero non essere stati salvati correttamente. Fare un controllo ricaricando lo studente');
                Ext.getCmp('CcpStudentsTab').setLoading(false);
            }
        });

        */
    },

    saveStudent: function() {
        var studentData = Ext.getCmp('CcpStudentsAnagraficaFrm').getValues();

        Ext.getCmp('CcpYearsToSaveFrm').setLoading();

        Ext.getCmp('CcpYearsToSaveFrm').getForm().submit({
            params: studentData,
            success: function(r, res) {
                var results = Ext.decode(res.response.responseText).results,
                    errors = [];

                Ext.each(results, function(rr){
                    if(rr.result!==true) {
                        errors.push(rr.subject_school_year + ' - ' + rr.result);
                    }
                });

                if(errors.length>0) {
                    Ext.Msg.alert('ERRORE', 'Per i seguenti anni scolastici non è stato possibile salvare i dati: <br> ' + errors.join(' <br />'));
                } else {
                    mc2ui.app.showNotifySave();
                }

                Ext.getCmp('CcpYearsToSaveFrm').setLoading(false);
                Ext.getCmp('CcpYearsToSaveWin').close();


            },
            failure: function() {
                Ext.Msg.alert('ERRORE', 'Errore genrico durante il salvataggio. Riprovare più tardi.');
                Ext.getCmp('CcpYearsToSaveFrm').setLoading(false);
            }

        });
    }

});