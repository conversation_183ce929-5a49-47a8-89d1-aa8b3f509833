/*
 * File: app/view/CcpPaymentMassiveEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpPaymentMassiveEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpPaymentMassiveEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.Label',
        'Ext.form.field.Date',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    id: 'CcpPaymentMassiveEditWin',
    width: 400,
    title: 'Modifica massiva',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'label',
                            height: 30,
                            id: 'CcpTotalPaymentEditingLbl',
                            text: ''
                        },
                        {
                            xtype: 'datefield',
                            flex: 1,
                            id: 'CcpPaymentMassiveUpdateNewDateOperation',
                            fieldLabel: 'Nuova data operazione',
                            labelWidth: 150,
                            name: 'operation_date_new',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        },
                        {
                            xtype: 'datefield',
                            id: 'CcpPaymentMassiveUpdateNewDateAccountable',
                            fieldLabel: 'Nuova data pagamento',
                            labelWidth: 150,
                            name: 'accountable_date_new',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'bottom',
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var rec = Ext.getCmp('CcpPaymentsFilterForm').getFilter(),
                                    selected = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection(),
                                    accountableNewDate=Ext.getCmp('CcpPaymentMassiveUpdateNewDateAccountable').getValue(),
                                    operationNewDate=Ext.getCmp('CcpPaymentMassiveUpdateNewDateOperation').getValue(),
                                    ids = [];

                                if (selected.length>0) {
                                    Ext.each(selected, function(val){
                                        ids.push(val.get('id'));
                                    });
                                    rec['ids[]'] = ids;
                                }
                                rec['accountable_date_new'] = Ext.Date.format(accountableNewDate, 'Y-m-d');
                                rec['operation_date_new'] = Ext.Date.format(operationNewDate, 'Y-m-d');

                                Ext.Ajax.request({
                                    url: '/mc2-api/ccp/massive_update_payments',
                                    params: rec,
                                    success: function(res) {
                                        var r = Ext.decode(res.responseText);
                                        if(r.success===true) {
                                            Ext.Msg.alert('INFO', 'Pagamenti aggiornati con successo');
                                            Ext.getStore('CcpPayments').load();
                                            Ext.getCmp('CcpPaymentMassiveEditWin').close();
                                        } else {
                                            Ext.Msg.alert('ERRORE', r.message);
                                        }
                                    }
                                });

                            },
                            text: 'Modifica'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onWindowShow: function(component, eOpts) {
        component.setLoading(true);
        var rec = Ext.getCmp('CcpPaymentsFilterForm').getFilter(),
            selected = Ext.getCmp('CcpPaymentsGrid').getSelectionModel().getSelection().length;

        if(selected) {
            Ext.getCmp('CcpTotalPaymentEditingLbl').setText('<b>Verranno modificati '+selected+' pagamenti</b>', false);
            component.setLoading(false);
        } else {
            Ext.Ajax.request({
                url:'/mc2-api/ccp/payment',
                params:rec,
                method:'GET',
                success:function(r) {
                    var res= Ext.decode(r.responseText);
                    if(res.success=== true) {
                        Ext.getCmp('CcpTotalPaymentEditingLbl').setText('<b>Verranno modificati '+res.total+' pagamenti</b>', false);
                    } else {
                        Ext.Msg.alert('ERRORE', 'Impossibile elaborare quanti pagamenti verranno modificati');
                        component.close();
                    }
                    component.setLoading(false);

                },
                failure: function() {
                       Ext.Msg.alert('ERRORE', 'Impossibile elaborare quanti pagamenti verranno modificati');
                       component.close();
                }
            });

        }


    }

});