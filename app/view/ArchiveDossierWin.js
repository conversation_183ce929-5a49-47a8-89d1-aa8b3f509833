/*
 * File: app/view/ArchiveDossierWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDossierWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDossierWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Spacer',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action'
    ],

    height: 556,
    id: 'ArchiveDossierWin',
    itemId: 'ArchiveDossierWin',
    width: 827,
    layout: 'fit',
    title: 'Fascicoli',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'ArchiveDossierFrm',
                    itemId: 'ArchiveDossierFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                padding: '0 0 5 0'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'ArchiveDossierCmb',
                                    itemId: 'ArchiveDossierCmb',
                                    padding: '0 5 0 0 ',
                                    fieldLabel: '',
                                    emptyText: 'Scegli fascicolo ...',
                                    displayField: 'name',
                                    minChars: 2,
                                    queryMode: 'local',
                                    store: 'ArchiveDossiers',
                                    typeAhead: true,
                                    listeners: {
                                        select: {
                                            fn: me.onArchiveDossierCmbSelect,
                                            scope: me
                                        },
                                        change: {
                                            fn: me.onArchiveDossierCmbChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'textfield',
                            hidden: true,
                            id: 'ArchiveDossierTxt',
                            itemId: 'ArchiveDossierTxt',
                            fieldLabel: 'Nome',
                            name: 'query',
                            allowBlank: false
                        },
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            title: 'Fascicoli abbinati',
                            hideHeaders: true,
                            store: 'ArchiveDocumentDossierLinked',
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'name',
                                    text: 'String',
                                    flex: 1
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 50,
                                    align: 'center',
                                    items: [
                                        {
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                Ext.getStore('ArchiveDocumentDossierLinked').remove(record);
                                            },
                                            iconCls: 'icon-delete'
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {

                                        var dossierRecords = Ext.getStore('ArchiveDocumentDossierLinked').getRange(),
                                            data = [];

                                        Ext.each(dossierRecords, function(d){
                                            data.push(d.get('id'));
                                        });

                                        Ext.Ajax.request({
                                            url: '/mc2-api/archive/document/' + Ext.getCmp('ArchiveDossierWin').archiveDocument.get('id'),
                                            method:'PUT',
                                            params: {
                                                dossier: Ext.encode(data)
                                            },
                                            success: function() {
                                                Ext.getCmp('ArchiveDossierWin').close();
                                                Ext.getStore('ArchiveDocumentDossier').load();
                                                if(Ext.getCmp('ArchiveArchivedTab').active){
                                                    Ext.getStore('ArchiveDocumentsArchived').load();
                                                } else {
                                                    Ext.getStore('ArchiveDocumentsUser').load();
                                                    Ext.getStore('ArchiveDocumentsOffice').load();
                                                }

                                            }

                                        });



                                    },
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ArchiveDossierNewWin').show();
                                    },
                                    id: 'DossierAddBtn',
                                    itemId: 'DossierAddBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onArchiveDossierWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onArchiveDossierCmbSelect: function(combo, records, eOpts) {
        var dossier = Ext.getStore('ArchiveDocumentDossierLinked');
        console.log(records[0]);
        if (!dossier.getById(records[0].get('id'))) {
            dossier.add(records[0]);
        }
        combo.reset();
        Ext.getStore('ArchiveDossiers').clearFilter();


    },

    onArchiveDossierCmbChange: function(field, newValue, oldValue, eOpts) {
        Ext.getStore('ArchiveDossiers').clearFilter();
        Ext.getStore('ArchiveDossiers').filter({
            property: 'name',
            value: newValue,
            anyMatch: true,
            caseSensitive: false
        });

    },

    onArchiveDossierWinShow: function(component, eOpts) {
        Ext.getStore('ArchiveDossiers').load();
    }

});