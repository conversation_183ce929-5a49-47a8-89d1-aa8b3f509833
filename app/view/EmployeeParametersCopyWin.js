/*
 * File: app/view/EmployeeParametersCopyWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeParametersCopyWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeParametersCopyWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 468,
    id: 'EmployeeParametersCopyWin',
    itemId: 'EmployeeParametersCopyWin',
    minHeight: 400,
    width: 354,
    title: 'Copia parametri',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeParametersCopyWin_Container',
                    itemId: 'EmployeeParametersCopyWin_Container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'toolbar',
                            padding: '5 0',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    disabled: true,
                                    id: 'EmployeeParametersCopyWinBtn',
                                    itemId: 'EmployeeParametersCopyWinBtn',
                                    iconCls: 'icon-page_copy',
                                    text: 'Copia',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            border: false,
                            height: 250,
                            id: 'EmployeeParametersCopyWinEmployees',
                            itemId: 'EmployeeParametersCopyWinEmployees',
                            width: 400,
                            autoScroll: true,
                            title: 'Personale',
                            titleAlign: 'center',
                            emptyText: 'Nessun Personale',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            hideHeaders: true,
                            sortableColumns: false,
                            store: 'EmployeesTreeActive',
                            displayField: 'denomination',
                            useArrows: true,
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'denomination',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onEmployeeParametersCopyWinEmployeesCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeeParametersCopyWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        // Take the merge id to print and put it in a JSON encoded array
        var sel = Ext.getCmp('EmployeeParametersCopyWinEmployees').getChecked(),
            mergeSelect = new Array();

        Ext.each(sel, function(a) {
            if (a.data.leaf === true) {
                mergeSelect = mergeSelect.concat(a.raw.employee_id);
            }
        });
        var mergeSelectJSON = Ext.JSON.encode(mergeSelect);

        Ext.MessageBox.show({
            title:'Salvataggio parametri',
            msg:'Se si procede, i parametri per la gestione delle presenze verranno salvati sul personale selezionato. Procedere?',
            buttons: Ext.Msg.YESNO,
            fn: function(r){
                if( r == 'yes' ){
                    Ext.getCmp('EmployeeParametersCopyWin').close();
                    Ext.getCmp('EmployeeSettingsMenuTab').saveParameters(mergeSelectJSON);
                }
            }
        });
    },

    onEmployeeParametersCopyWinEmployeesCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeParametersCopyWin').enableCopy();
    },

    onEmployeeParametersCopyWinActivate: function(window, eOpts) {
        var t = Ext.getCmp('EmployeeParametersCopyWinEmployees');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });
    },

    enableCopy: function() {
        var employees = Ext.getCmp('EmployeeParametersCopyWinEmployees').getChecked();

        if (employees.length > 0) {
            Ext.getCmp('EmployeeParametersCopyWinBtn').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeParametersCopyWinBtn').setDisabled(true);
        }
    }

});