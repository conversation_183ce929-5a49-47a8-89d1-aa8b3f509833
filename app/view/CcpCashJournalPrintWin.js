/*
 * File: app/view/CcpCashJournalPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpCashJournalPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpCashJournalPrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Date',
        'Ext.form.field.ComboBox',
        'Ext.form.FieldSet'
    ],

    height: 210,
    width: 262,
    resizable: false,
    title: 'Stampa giornale di cassa',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch',
        pack: 'center'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    height: 134,
                    id: 'CcpCashJournalPrintForm',
                    itemId: 'CcpCashJournalPrintForm',
                    width: 268,
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    title: 'My Form',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    formBind: true,
                                    iconCls: 'icon-printer',
                                    text: 'Stampa',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            id: 'CcpDateStartCJPrint',
                            itemId: 'CcpDateStartCJPrint',
                            fieldLabel: 'Da',
                            labelWidth: 25,
                            name: 'startDate',
                            allowBlank: false,
                            emptyText: 'Data inizio ...',
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        },
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            id: 'CcpDateEndCJPrint',
                            itemId: 'CcpDateEndCJPrint',
                            fieldLabel: 'A',
                            labelWidth: 25,
                            name: 'endDate',
                            allowBlank: false,
                            emptyText: 'Data fine ...',
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            formBind: false,
                            id: 'CcpBankCJPrint',
                            fieldLabel: 'C/C',
                            labelWidth: 25,
                            name: 'bankId',
                            allowBlank: false,
                            displayField: 'denomination',
                            queryMode: 'local',
                            store: 'CcpPaymentDestinations',
                            valueField: 'id'
                        },
                        {
                            xtype: 'fieldset',
                            title: 'Raggruppamento',
                            items: [
                                {
                                    xtype: 'combobox',
                                    anchor: '100%',
                                    fieldLabel: '',
                                    name: 'groupType',
                                    value: 'Nessuno',
                                    store: [
                                        'Nessuno',
                                        'Indirizzo'
                                    ],
                                    valueField: 'id'
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        var rec = {},
            values = Ext.getCmp('CcpCashJournalPrintForm').getForm().getValues();

        rec.newSpool = 1;
        rec.print = 'CashJournal';
        rec.namespace = 'CCP';
        rec.type = 'PDF';
        rec.mime = 'application/pdf';

        rec.startDate = values.startDate;
        rec.endDate = values.endDate;
        rec.bankId = values.bankId;


        if(values.groupType == 'Indirizzo') rec.groupType = 'schooladdress';

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params: rec,
            success: function(response, opts) {
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });
    },

    onWindowShow: function(component, eOpts) {
        var dd = new Date(),
            comboBank = Ext.getCmp('CcpBankCJPrint'),
            store = comboBank.getStore(),
            def;

        Ext.getCmp('CcpDateStartCJPrint').setValue('01/01/' + dd.getFullYear());
        Ext.getCmp('CcpDateEndCJPrint').setValue('31/12/' + dd.getFullYear());

        store.filter('type', 'P');
        if(store.getRange().length > 0){
            def = store.getRange()[0];
        }
        store.clearFilter();

        if (def) {
            comboBank.select(def);
        }
    }

});