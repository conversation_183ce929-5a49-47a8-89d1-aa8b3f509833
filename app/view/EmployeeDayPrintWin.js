/*
 * File: app/view/EmployeeDayPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeDayPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeDayPrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Date',
        'Ext.form.FieldSet',
        'Ext.form.field.ComboBox',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 468,
    id: 'EmployeeDayPrintWin',
    itemId: 'EmployeeDayPrintWin',
    minHeight: 400,
    width: 354,
    title: 'Stampa riepilogo giornaliero',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeDayPrint_Container',
                    itemId: 'EmployeeDayPrintGrid_Container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            border: false,
                            id: 'EmployeeDayPrint_Form',
                            itemId: 'EmployeeDayPrint_Form',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-box-layout-ct'
                            ],
                            bodyPadding: 10,
                            header: false,
                            title: 'My Form',
                            layout: {
                                type: 'vbox',
                                align: 'center'
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: '5 0',
                                    layout: {
                                        type: 'hbox',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            disabled: true,
                                            id: 'EmployeeDayPrintBtnPrint',
                                            itemId: 'EmployeeDayPrintBtnPrint',
                                            iconCls: 'icon-printer',
                                            text: 'Stampa',
                                            listeners: {
                                                click: {
                                                    fn: me.onEmployeeDayPrintBtnPrintClick,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'datefield',
                                    id: 'EmployeeDayPrint_FilterDay',
                                    itemId: 'EmployeeDayPrint_FilterDay',
                                    width: 200,
                                    fieldLabel: 'Giorno',
                                    labelAlign: 'right',
                                    inputId: 'day',
                                    allowBlank: false,
                                    editable: false,
                                    altFormats: 'm/d/Y',
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'd-m-Y'
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Filtri',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'EmployeeDayPrint_FilterTimetables',
                                            itemId: 'EmployeeDayPrint_FilterTimetables',
                                            fieldLabel: 'Orario',
                                            labelAlign: 'right',
                                            name: 'timetables',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            forceSelection: true,
                                            store: [
                                                [
                                                    2,
                                                    'Tutti'
                                                ],
                                                [
                                                    1,
                                                    'Con orario'
                                                ],
                                                [
                                                    0,
                                                    'Senza orario'
                                                ]
                                            ]
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'EmployeeDayPrint_FilterPresences',
                                            itemId: 'EmployeeDayPrint_FilterPresences',
                                            fieldLabel: 'Timbrature',
                                            labelAlign: 'right',
                                            name: 'presences',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            forceSelection: true,
                                            store: [
                                                [
                                                    2,
                                                    'Tutti'
                                                ],
                                                [
                                                    1,
                                                    'Con timbrature'
                                                ],
                                                [
                                                    0,
                                                    'Senza timbrature'
                                                ]
                                            ]
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'EmployeeDayPrint_FilterAbsences',
                                            itemId: 'EmployeeDayPrint_FilterAbsences',
                                            fieldLabel: 'Assenze',
                                            labelAlign: 'right',
                                            name: 'absences',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            forceSelection: true,
                                            store: [
                                                [
                                                    2,
                                                    'Tutti'
                                                ],
                                                [
                                                    1,
                                                    'Con assenze'
                                                ],
                                                [
                                                    0,
                                                    'Senza assenze'
                                                ]
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            border: false,
                            height: 250,
                            id: 'EmployeeDayPrintGrid',
                            itemId: 'EmployeeDayPrintGrid',
                            width: 400,
                            autoScroll: true,
                            title: 'Personale',
                            titleAlign: 'center',
                            emptyText: 'Nessun Personale',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            hideHeaders: true,
                            sortableColumns: false,
                            store: 'EmployeesTreeActive',
                            displayField: 'denomination',
                            useArrows: true,
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'denomination',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onEmployeeDayPrintGridCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeeDayPrintWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onEmployeeDayPrintBtnPrintClick: function(button, e, eOpts) {
        Ext.getCmp('EmployeeDayPrint_Container').setLoading();

        // Take filters
        var filters = Ext.getCmp('EmployeeDayPrint_Form').getForm().getValues();

        // Take the merge id to print and put it in a JSON encoded array
        var sel = Ext.getCmp('EmployeeDayPrintGrid').getChecked(),
            mergeSelect = new Array();

        Ext.each(sel, function(a) {
            if (a.data.leaf === true) {
                mergeSelect = mergeSelect.concat(a.raw.employee_id);
            }
        });
        var mergeSelectJSON = Ext.JSON.encode(mergeSelect);

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 0,
                print: 'Day',
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFDay',
                mime: 'application/pdf',
                day: filters.day,
                employees: mergeSelectJSON,
                timetables: filters.timetables,
                presences: filters.presences,
                absences: filters.absences
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeeDayPrint_Container').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });
    },

    onEmployeeDayPrintGridCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeDayPrintWin').enablePrint();
    },

    onEmployeeDayPrintWinActivate: function(window, eOpts) {
        var date = Ext.Date.format(new Date(),'d/m/Y');
        Ext.getCmp('EmployeeDayPrint_FilterDay').setValue(date);

        var t = Ext.getCmp('EmployeeDayPrintGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        var timetables = Ext.getCmp('EmployeeDayPrint_FilterTimetables').setValue(2),
            presences = Ext.getCmp('EmployeeDayPrint_FilterPresences').setValue(2),
            absences = Ext.getCmp('EmployeeDayPrint_FilterAbsences').setValue(2);

        Ext.getCmp('EmployeeDayPrintWin').enablePrint();
    },

    enablePrint: function() {
        var employees = Ext.getCmp('EmployeeDayPrintGrid').getChecked();

        if (employees.length > 0) {
            Ext.getCmp('EmployeeDayPrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeDayPrintBtnPrint').setDisabled(true);
        }
    }

});