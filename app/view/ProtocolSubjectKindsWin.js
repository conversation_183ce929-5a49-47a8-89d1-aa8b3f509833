/*
 * File: app/view/ProtocolSubjectKindsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolSubjectKindsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolSubjectKindsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 600,
    id: 'ProtocolSubjectKindsWin',
    itemId: 'ProtocolSubjectKindsWin',
    width: 700,
    resizable: false,
    title: 'Tip<PERSON> di Oggetto',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    permissible: true,
                    flex: 1,
                    border: false,
                    id: 'ProtocolSubjectKindsGrid',
                    itemId: 'ProtocolSubjectKindsGrid',
                    header: false,
                    emptyText: 'Nessun Tipo di Oggetto caricato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'ProtocolSubjectKinds',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'title',
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                allowBlank: false,
                                allowOnlyWhitespace: false
                            }
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onProtocolSubjectKindsGridItemContextMenu,
                            scope: me
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ProtocolSubjectKindEditWin').show();
                                    },
                                    id: 'ProtocolSubjetKindNewBtn',
                                    itemId: 'ProtocolSubjetKindNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'ProtocolSubjectKindsEditMn',
                    itemId: 'ProtocolSubjectKindsEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var pg = Ext.getCmp('ProtocolSubjectKindsGrid'),
                                    record = pg.getSelectionModel().getSelection()[0];

                                Ext.widget('ProtocolSubjectKindEditWin').show();

                                Ext.getCmp('ProtocolSubjectKindEditForm').getForm().loadRecord(record);
                            },
                            id: 'contextProtocolSubjectKindEdit',
                            itemId: 'contextProtocolSubjectKindEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('ProtocolSubjectKindsGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('denomination'),
                                    msg: 'Sei sicuro di voler eliminare questo Tipo di Oggetto?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('ProtocolSubjectKinds');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Tipo di Oggetto eliminato');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Tipo di Oggetto NON eliminato');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextProtocolSubjectKindDelete',
                            itemId: 'contextProtocolSubjectKindDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onProtocolSubjectKindsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        var menu = Ext.getCmp('ProtocolSubjectKindsEditMn');
        menu.showAt([newX,newY]);

        // Already in use send methods cannot be deleted
        if (record.get('locked')) {
            menu.items.get('contextProtocolSubjectKindDelete').setDisabled(true);
        } else {
            menu.items.get('contextProtocolSubjectKindDelete').setDisabled(false);
        }
    }

});