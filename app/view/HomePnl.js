/*
 * File: app/view/HomePnl.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.HomePnl', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.HomePnl',

    requires: [
        'Ext.menu.Menu',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.View',
        'Ext.selection.RowModel',
        'Ext.menu.Item',
        'Ext.XTemplate'
    ],

    border: false,
    id: 'HomePnl',
    itemId: 'HomePnl',
    header: false,
    title: 'Informazioni & Stampe',
    titleAlign: 'center',

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    layout: {
                        type: 'hbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'panel',
                            flex: 1,
                            id: 'ReleaseNotesListMn',
                            itemId: 'ReleaseNotesListMn',
                            padding: '4 2 2 4',
                            iconCls: 'icon-note',
                            title: 'Note di rilascio',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            listeners: {
                                boxready: {
                                    fn: me.onPanelBoxReady,
                                    scope: me
                                }
                            },
                            items: [
                                {
                                    xtype: 'menu',
                                    border: 0,
                                    floating: false,
                                    id: 'ReleaseNotesVersionMenu',
                                    itemId: 'ReleaseNotesVersionMenu'
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    html: '<h1>Seleziona una nota di rilascio</h1>',
                                    id: 'ReleaseNoteTextCnt',
                                    itemId: 'ReleaseNoteTextCnt',
                                    padding: 10,
                                    autoScroll: true
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            flex: 1,
                            padding: '4 4 2 2',
                            layout: 'fit',
                            iconCls: 'icon-printer',
                            title: 'Stampe',
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    border: 0,
                                    id: 'PrintSpoolPnl',
                                    itemId: 'PrintSpoolPnl',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    sortableColumns: false,
                                    store: 'CorePrintSpool',
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            width: 30,
                                            align: 'center',
                                            dataIndex: 'completed',
                                            tooltip: 'Stato produzione della stampa',
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (v === true) {
                                                            return 'icon-tick';
                                                        } else {
                                                            return 'icon-time';
                                                        }
                                                    },
                                                    disabled: false,
                                                    iconCls: 'icon-time'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (record.get('completed')) {
                                                    return '<a href="#null">' + value + '</a>';
                                                } else {
                                                    return value;
                                                }
                                            },
                                            dataIndex: 'name',
                                            text: 'Nome',
                                            flex: 1
                                        }
                                    ],
                                    viewConfig: {
                                        getRowClass: function(record, rowIndex, rowParams, store) {
                                            if (record.raw.status === false) {
                                                return 'x-item-disabled';
                                            }
                                        },
                                        id: 'PrintSpoolView',
                                        itemId: 'PrintSpoolView',
                                        emptyText: 'Nessuna stampa in produzione',
                                        loadMask: false
                                    },
                                    selModel: Ext.create('Ext.selection.RowModel', {
                                        mode: 'SINGLE'
                                    }),
                                    listeners: {
                                        itemclick: {
                                            fn: me.onPrintSpoolPnlItemClick,
                                            scope: me
                                        },
                                        itemcontextmenu: {
                                            fn: me.onPrintSpoolPnlItemContextMenu,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'menu',
                                    id: 'PrintSpoolMn',
                                    itemId: 'PrintSpoolMn',
                                    items: [
                                        {
                                            xtype: 'menuitem',
                                            handler: function(item, e) {
                                                var record = Ext.getCmp('PrintSpoolPnl').getSelectionModel().getSelection()[0],
                                                    store = Ext.getStore('CorePrintSpool');

                                                store.remove(record);
                                                store.sync();
                                            },
                                            id: 'contextPrintSpoolCancel',
                                            itemId: 'contextPrintSpoolCancel',
                                            iconCls: 'icon-cancel',
                                            text: 'Annulla/Rimuovi Stampa'
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                show: {
                                    fn: me.onPanelShow1,
                                    scope: me
                                }
                            }
                        }
                    ]
                },
                {
                    xtype: 'container',
                    flex: 1,
                    layout: {
                        type: 'hbox',
                        align: 'stretch',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'panel',
                            flex: 1,
                            padding: '2 2 4 4',
                            iconCls: 'icon-information',
                            title: 'Info',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'dataview',
                                    id: 'InfoView',
                                    itemId: 'InfoView',
                                    itemSelector: 'div.contact-box',
                                    itemTpl: [
                                        '',
                                        '',
                                        '<div class="contact" style="height:30px;">',
                                        '    {title}: <b> {text} </b>    ',
                                        '</div>'
                                    ],
                                    store: 'HomeInfos'
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            flex: 1,
                            padding: '2 4 4 2',
                            iconCls: 'icon-mail',
                            title: 'Contatti',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'dataview',
                                    id: 'ContactsView',
                                    itemId: 'ContactsView',
                                    itemSelector: 'div.contact-box',
                                    itemTpl: [
                                        '<div class="contact" style="height:30px;">',
                                        '    {label}: <b> {text} </b>    ',
                                        '</div>'
                                    ],
                                    store: 'MastertrainingContacts'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onPanelBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('HomeReleaseNotes').load({
            callback: function(response){
                if (!response) {
                    Ext.getCmp('ReleaseNotesListMn').setDisabled(true);
                    return;
                }
                var rnm = Ext.getCmp('ReleaseNotesVersionMenu'),
                    last = response[0];
                Ext.each(response, function(rn) {
                    var icon = 'icon-email_open';
                    if (rn.get('is_new')) {
                        icon = 'icon-asterisk_yellow';
                    }
                    var menuItem = Ext.create('Ext.menu.Item', {
                        itemId: rn.get('version'),
                        text: 'Versione: ' + rn.get('version'),
                        iconCls: icon,
                        handler: function() {
                            Ext.get('ReleaseNoteTextCnt').setHTML(rn.get('text'));
                            Ext.getStore('HomeReleaseNotes').update({
                                params: {
                                    version: rn.get('version')
                                }
                            });
                            this.setIconCls('icon-email_open');
                        }
                    });
                    rnm.add(menuItem);
                });
                Ext.get('ReleaseNoteTextCnt').setHTML('<b>Ultima nota di rilascio disponibile: ' + last.get('version') + '</b>');
            }
        });
    },

    onPrintSpoolPnlItemClick: function(dataview, record, item, index, e, eOpts) {
        if (record.get('completed')) {
            mc2ui.app.openPrint(record.get('id'));
        }
    },

    onPrintSpoolPnlItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('PrintSpoolMn').showAt([newX,newY]);
    },

    onPanelShow1: function(component, eOpts) {
        Ext.getStore('PrintSpool').load();
    }

});