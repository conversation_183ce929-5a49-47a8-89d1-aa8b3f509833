/*
 * File: app/view/ArchiveSignRemoteLogin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveSignRemoteLogin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveSignRemoteLogin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Text',
        'Ext.form.field.Hidden',
        'Ext.button.Button'
    ],

    id: 'ArchiveSignRemoteLogin',
    width: 400,
    layout: 'fit',
    title: 'Login firma remota',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'ArchiveSignRemoteLoginFrm',
                    itemId: 'ArchiveSignRemoteLoginFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/archive/sign/credentials',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Alias',
                            name: 'alias',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Pin',
                            name: 'pin',
                            inputType: 'password',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            fieldLabel: 'Label',
                            name: 'format'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var format = Ext.getCmp('ArchiveSignRemoteLoginFrm').getValues();

                                        if (Ext.getCmp('ArchiveSignRemoteLoginFrm').isValid()) {
                                            Ext.getCmp('ArchiveSignRemoteLogin').setLoading();
                                            Ext.getCmp('ArchiveSignRemoteLoginFrm').submit({
                                                failure: function(a,r) {
                                                    Ext.Msg.alert('ATTENZIONE', r.result.message);
                                                    Ext.getCmp('ArchiveSignRemoteLogin').setLoading(false);
                                                },
                                                success: function() {
                                                    // Ext.widget('ArchiveSignRemoteOtpWin').show();
                                                    // Ext.getCmp('ArchiveSignRemoteLogin').close();
                                                    // Ext.getCmp('ArchiveSignRemoteOtpFrm').getForm().setValues(format);

                                                    if (mc2ui.app.settings.signType === 'MASSIVE') {
                                                        var formValues = Ext.getCmp('ArchiveSignRemoteLoginFrm').getValues();
                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/archive/sign/sign/',
                                                            method: 'POST',
                                                            params: {
                                                                folders: Ext.encode([Ext.getCmp('ArchiveSignRemoteLogin').record.get('id')]),
                                                                format: formValues.format,
                                                                alias: formValues.alias,
                                                                pin: formValues.pin,
                                                                massive: false
                                                            },
                                                            success: function(r) {
                                                                var res = Ext.decode(r.responseText);
                                                                if (res.status === 200) {
                                                                    Ext.getCmp('ArchiveSignRemoteLogin').close();
                                                                    Ext.Msg.alert('FIRMA', 'Documenti firmati correttamente');
                                                                    Ext.getStore('ArchiveDocumentsArchived').load();
                                                                    Ext.getStore('ArchiveDocumentsUser').load();
                                                                    Ext.getStore('ArchiveDocumentsOffice').load();
                                                                } else {
                                                                    Ext.Msg.alert('ATTENZIONE','Errore durante l\'invio dei dati. Si prega di rieffettuare la procedura. Se l\'errore dovesse persistere contattare l\'assistenza');
                                                                    Ext.getCmp('ArchiveSignRemoteLogin').setLoading(false);
                                                                }
                                                            },
                                                            failure: function(form, res) {
                                                                if (res.result.status === 307) {
                                                                    Ext.widget('ArchiveSignRemoteLogin').show();
                                                                    Ext.getCmp('ArchiveSignRemoteOtpWin').close();
                                                                    Ext.getCmp('ArchiveSignRemoteLoginFrm').getForm().setValues(format);
                                                                } else {
                                                                    Ext.Msg.alert('ATTENZIONE','Errore durante l\'invio dei dati. Si prega di rieffettuare la procedura. Se l\'errore dovesse persistere contattare l\'assistenza');
                                                                    Ext.getCmp('ArchiveSignRemoteOtpWin').close();
                                                                }
                                                            }
                                                        });
                                                    } else {

                                                        Ext.Ajax.request({
                                                            method: 'GET',
                                                            url: '/mc2-api/archive/sign/otp',
                                                            success: function(r) {
                                                                var res = Ext.decode(r.responseText);

                                                                if(res.success){
                                                                    Ext.widget('ArchiveSignRemoteOtpWin').show();
                                                                    Ext.getCmp('ArchiveSignRemoteOtpWin').record = Ext.getCmp('ArchiveSignRemoteLogin').record;
                                                                    Ext.getCmp('ArchiveSignRemoteLogin').close();
                                                                    Ext.getCmp('ArchiveSignRemoteOtpFrm').getForm().setValues(format);
                                                                } else if (res.status === 307) {
                                                                    Ext.getCmp('ArchiveSignRemoteLogin').setLoading(false);
                                                                    // Ext.widget('ArchiveSignRemoteLogin').show();
                                                                    // Ext.getCmp('ArchiveSignRemoteOptWin').close();
                                                                } else {
                                                                    Ext.getCmp('ArchiveSignRemoteLogin').setLoading(false);
                                                                    Ext.Msg.alert('ATTENZIONE','Errore durante l\'invio dei dati. Si prega di rieffettuare la procedura. Se l\'errore dovesse persistere contattare l\'assistenza');
                                                                }
                                                            }
                                                        });
                                                    }
                                                }
                                            });
                                        }


                                    },
                                    text: 'Login'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});