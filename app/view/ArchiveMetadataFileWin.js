/*
 * File: app/view/ArchiveMetadataFileWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveMetadataFileWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveMetadataFileWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.grid.plugin.CellEditing',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Spacer',
        'Ext.form.field.ComboBox'
    ],

    height: 305,
    id: 'ArchiveMetadataFileWin',
    width: 499,
    layout: 'fit',
    title: 'Metadati',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'ArchiveMetadataFileGrid',
                    itemId: 'ArchiveMetadataFileGrid',
                    title: '',
                    store: 'ArchiveMetadataFiles',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            width: 200,
                            align: 'right',
                            dataIndex: 'name',
                            text: 'Nome'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'value',
                            text: 'Valore',
                            flex: 1,
                            editor: {
                                xtype: 'textfield'
                            }
                        }
                    ],
                    features: [
                        {
                            ftype: 'grouping',
                            groupHeaderTpl: [
                                '{name}'
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.CellEditing', {
                            clicksToEdit: 1
                        })
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            id: 'ArchiveMetadataFileTb',
                            itemId: 'ArchiveMetadataFileTb',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var id = Ext.getCmp('ArchiveMetadataFileWin').record.get('id'),
                                            amfs = Ext.getStore('ArchiveMetadataFiles'),
                                            data = [];


                                        Ext.getCmp('ArchiveMetadataFileWin').setLoading();
                                        Ext.each(amfs.getRange(), function(rec){
                                            data.push(rec.data);
                                        });

                                        Ext.Ajax.request({
                                            url: '/mc2-api/archive/document/' + id + '/remote',
                                            method: 'POST',
                                            params: {data: Ext.encode(data)},
                                            success: function(r){
                                                var res = Ext.decode(r.responseText);
                                                if (res.success) {
                                                    Ext.getCmp('ArchiveMetadataFileWin').close();
                                                    Ext.Msg.alert('SUCCESSO', 'Flusso archiviato in corservazione correttamente');
                                                    Ext.getCmp('ArchiveFilterForm').applyFilter();
                                                    Ext.getStore('ArchiveDocumentsUser').load();
                                                    Ext.getStore('ArchiveDocumentsOffice').load();
                                                    Ext.getStore('Register').load();
                                                } else {
                                                    Ext.getCmp('ArchiveMetadataFileWin').setLoading(false);
                                                    Ext.Msg.alert('ERRORE', res.message);
                                                }
                                            }
                                        });
                                    },
                                    iconCls: 'icon-database',
                                    text: 'Carica in conservazione'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'combobox',
                                    id: 'ArchiveRemoteMetadataCmb',
                                    itemId: 'ArchiveRemoteMetadataCmb',
                                    fieldLabel: '',
                                    displayField: 'name',
                                    store: 'RemoteClass',
                                    valueField: 'code',
                                    listeners: {
                                        change: {
                                            fn: me.onComboboxChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {
        var amfs = Ext.getStore('ArchiveMetadataFiles'),
            record = Ext.getCmp('ArchiveMetadataFileWin').record;

        amfs.removeAll();
        Ext.Ajax.request({
            url: '/mc2-api/archive/metadata_build?document=' + record.get('id') + '&remote_class=' + newValue,
            success: function(r){
                res = Ext.decode(r.responseText);
                Ext.each(res.results, function(a){
                    amfs.add(a.metadata);
                });
            }
        });
    }

});