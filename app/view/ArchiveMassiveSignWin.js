/*
 * File: app/view/ArchiveMassiveSignWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveMassiveSignWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveMassiveSignWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Spacer'
    ],

    height: 313,
    id: 'ArchiveMassiveSignWin',
    itemId: 'ArchiveMassiveSignWin',
    width: 562,
    layout: 'fit',
    title: 'Firma massiva',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    border: false,
                    id: 'ArchiveMassiveSignGrid',
                    itemId: 'ArchiveMassiveSignGrid',
                    header: false,
                    iconCls: 'icon-folder_page',
                    emptyText: 'Nessun documento archiviato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ArchiveDashboard',
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            dataIndex: 'action_sign',
                            hideable: false,
                            text: 'F',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_sign'),
                                            date = r.get('action_sign_date');

                                        if (action) {
                                            if (date !== null) {
                                                return 'icon-accept';
                                            } else {
                                                return 'icon-control_blank';
                                            }
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var action = r.get('action_sign'),
                                            date = r.get('action_sign_date');

                                        if (action) {
                                            if (date !== null) {
                                                return 'Firmato il: ' + Ext.util.Format.date(date, 'd/m/Y H:i');
                                            } else {
                                                return 'Da Firmare';
                                            }
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            align: 'center',
                            dataIndex: 'class_name',
                            text: 'Tipo di flusso'
                        },
                        {
                            xtype: 'datecolumn',
                            width: 109,
                            sortable: true,
                            align: 'center',
                            dataIndex: 'upload_date',
                            hideable: false,
                            text: 'Data caricamento',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'datecolumn',
                            width: 109,
                            sortable: true,
                            align: 'center',
                            dataIndex: 'expiration_date',
                            hideable: false,
                            text: 'Scadenza',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'short_description',
                            text: 'Descrizione breve',
                            flex: 1
                        }
                    ],
                    viewConfig: {
                        getRowClass: function(record, rowIndex, rowParams, store) {
                            if (record.get('completed')) {
                                return 'archive-completed';
                            }
                            return '';
                        }
                    },
                    listeners: {
                        itemdblclick: {
                            fn: me.onArchiveDocumentsGridItemDblClick11,
                            scope: me
                        }
                    },
                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                    })
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var selections = Ext.getCmp('ArchiveMassiveSignGrid').getSelectionModel().getSelection(),
                                    store = Ext.getStore('ArchiveSignTypes'),
                                    data = {};

                                if (selections.length === 0) {
                                    Ext.Msg.alert('ATTENZIONE', 'Seleziona almeno un elemento da firmare');
                                    return;
                                }

                                store.removeFilter();

                                Ext.widget('ArchiveSignRemoteWin').show();

                                Ext.getCmp('ArchiveSignRemoteWin').setTitle('Firma massiva');

                                Ext.getCmp('ArchiveMassiveSignAliasTxt').show();
                                Ext.getCmp('ArchiveMassiveSignPinTxt').show();

                                Ext.getCmp('ArchiveSignRemoteWin').records = selections;
                                Ext.getCmp('ArchiveSignRemoteWin').massive = true;

                                Ext.each(selections, function(record) {
                                    if (record.get('only_pdf') === false) {
                                        Ext.getCmp('ArchiveSignRemoteLabel').show();
                                        store.filter('id', 'CADES');
                                    }
                                });




                            },
                            iconCls: 'icon-pencil',
                            text: 'Firma le i documenti selezionati'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var dossier = Ext.getCmp('ArchiveMassiveSignWin').record.get('name');

                                Ext.getStore('ArchiveDashboard').load({
                                    params: {
                                        dossier: dossier
                                    }
                                });
                            },
                            iconCls: 'icon-arrow_refresh',
                            text: ''
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onArchiveDocumentsGridItemDblClick11: function(dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchivePnl').viewDocument(record);
    }

});