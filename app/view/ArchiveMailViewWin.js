/*
 * File: app/view/ArchiveMailViewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveMailViewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveMailViewWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.TextArea',
        'Ext.form.Label',
        'Ext.view.View',
        'Ext.XTemplate',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: '50%',
    id: 'ArchiveMailViewWin',
    width: '80%',
    layout: 'fit',
    title: '',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'ArchiveMailFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Da',
                            name: 'from',
                            readOnly: true
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: 'A',
                            name: 'to',
                            readOnly: true
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Data',
                            name: 'date',
                            readOnly: true
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            height: 135,
                            hidden: true,
                            fieldLabel: 'Messaggio',
                            name: 'message',
                            readOnly: true
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: '10 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'label',
                                    width: 105,
                                    text: 'Messaggio'
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    height: 150,
                                    id: 'MessageMailCnt',
                                    itemId: 'MessageMailCnt',
                                    autoScroll: true
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'label',
                                    width: 105,
                                    text: 'Allegati:'
                                },
                                {
                                    xtype: 'dataview',
                                    flex: 1,
                                    itemSelector: 'div',
                                    itemTpl: [
                                        '<spam style="font-style:italic;"><a href="/mc2-api/archive/mail/attachment/{id}">{name}</a></spam>'
                                    ],
                                    store: 'ArchiveMailAttachments'
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    id: 'ArchiveMailViewTb',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var record = Ext.getCmp('ArchiveMailPnl').getSelectionModel().getSelection()[0];

                                Ext.getCmp('ArchiveMailViewWin').close();

                                Ext.widget('ArchiveDocumentUploadWin').show();
                                Ext.getCmp('ArchiveAttachmentCnt').show();
                                Ext.getCmp('ArchiveUploadFile').hide();

                                Ext.getCmp('ArchiveUploadShortDescription').setValue(record.get('from') + ' - ' + record.get('subject'));
                                Ext.getCmp('ArchiveUploadOriginId').setValue(2); // Mail origin
                                Ext.getCmp('ArchiveUploadMailIdAccount').setValue(record.get('id') + '_' + record.get('account')); // Mail id_account

                            },
                            hidden: true,
                            id: 'ArchiveCreateFlowBtn',
                            iconCls: 'icon-sitemap',
                            text: 'Crea flusso'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }



});