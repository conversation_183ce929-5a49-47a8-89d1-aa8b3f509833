/*
 * File: app/view/CcpAdditionalsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpAdditionalsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpAdditionalsWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.View',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 300,
    id: 'CcpAdditionalsWin',
    itemId: 'CcpAdditionalsWin',
    width: 400,
    resizable: false,
    title: 'Addizionali',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    flex: 1,
                    dock: 'top',
                    id: 'CcpAdditionalsToolbar',
                    itemId: 'CcpAdditionalsToolbar',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpAdditionalEditWin').show();
                            },
                            id: 'CcpAdditionalNewBtn',
                            itemId: 'CcpAdditionalNewBtn',
                            iconCls: 'icon-add',
                            text: 'Nuova'
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'CcpAdditionalsGrid',
                    itemId: 'CcpAdditionalsGrid',
                    emptyText: 'Nessuna Addizionale presente.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'CcpAdditionals',
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            width: 40,
                            resizable: false,
                            hideable: false,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('payment')) {
                                            return 'icon-creditcards';
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('payment')) {
                                            return 'Addizionale relativa ai pagamenti';
                                        }
                                    }
                                },
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('positive')) {
                                            return 'icon-control_add';
                                        } else {
                                            return 'icon-control_remove';
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('positive')) {
                                            return 'Addizionale positiva';
                                        } else {
                                            return 'Addizionale negativa';
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return '%';
                                } else {
                                    return '';
                                }
                            },
                            width: 20,
                            resizable: false,
                            align: 'center',
                            dataIndex: 'percentual',
                            hideable: false
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 35,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if(r.get('positive') === false && r.get('percentual') === true && r.get('payment') === false) return 'icon-application_form_edit';
                                    },
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        if(record.get('positive') === false && record.get('percentual') === true && record.get('payment') === false) {
                                            Ext.getCmp('CcpAdditionalsGrid').getSelectionModel().select(record);
                                            Ext.widget('CcpAdditionalTemplateWin').show();
                                        }
                                    },
                                    tooltip: 'Modifica template sconto'
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onCcpAdditionalsGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'CcpAdditionalEditMn',
                    itemId: 'CcpAdditionalEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('CcpAdditionalsGrid').getSelectionModel().getSelection()[0];

                                Ext.widget('CcpAdditionalEditWin').show();

                                if (r.get('positive') === true) r.data.positive =  'on';
                                else r.data.positive =  'off';

                                if (r.get('percentual') === true) r.data.percentual =  'on';
                                else r.data.percentual =  'off';

                                if (r.get('payment') === true) r.data.payment =  'on';
                                else r.data.payment =  'off';


                                Ext.getCmp('CcpAdditionalEditForm').getForm().loadRecord(r);
                            },
                            id: 'contextCcpAdditionalEdit',
                            itemId: 'contextCcpAdditionalEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('CcpAdditionalsGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questa Addizionale?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('CcpAdditionals');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load({params: {type: 'D'}});
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Addizionale eliminata');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Addizionale NON eliminata');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextCcpAdditionalDelete',
                            itemId: 'contextCcpAdditionalDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpAdditionalsWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCcpAdditionalsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('CcpAdditionalEditMn').showAt([newX,newY]);

        if (record.get('locked')) {
            Ext.getCmp('contextCcpAdditionalEdit').setDisabled(true);
            Ext.getCmp('contextCcpAdditionalDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextCcpAdditionalEdit').setDisabled(false);
            Ext.getCmp('contextCcpAdditionalDelete').setDisabled(false);
        }
    },

    onCcpAdditionalsWinShow: function(component, eOpts) {
        Ext.getStore('CcpAdditionals').load({
            params: {
                type:'D'
            }
        });
    }

});