/*
 * File: app/view/TrasparenzaVoiceEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TrasparenzaVoiceEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.TrasparenzaVoiceEditWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.Panel',
        'Ext.form.field.HtmlEditor',
        'Ext.form.field.Checkbox',
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.toolbar.Fill',
        'Ext.form.field.File',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 600,
    id: 'TrasparenzaVoiceEditWin',
    itemId: 'TrasparenzaVoiceEditWin',
    width: 700,
    resizable: false,
    title: 'Voce',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var data = Ext.getCmp('TrasparenzaVoiceEditForm').getForm().getValues(),
                                    docs = Ext.getStore('TrasparenzaLinkedDocumentsForm').getRange(),
                                    store = Ext.getStore('TrasparenzaVoicesTree'),
                                    record = Ext.getCmp('TrasparenzaGrid').getSelectionModel().getSelection()[0];

                                data.linked_documents = [];

                                docs.forEach(function(doc) {
                                    data.linked_documents.push(doc.get('id'));
                                });

                                record.set('title', data.title);
                                record.set('content', data.content);
                                record.set('reference', data.reference);
                                record.set('published', data.published == 'on' ? true : false);
                                record.set('linked_documents', data.linked_documents);

                                store.sync({
                                    callback: function() {
                                        store.load();
                                        Ext.getStore('ArchiveDocumentsArchived').load();
                                    },
                                    success: function(form, action) {
                                        Ext.getCmp('TrasparenzaGrid').getSelectionModel().deselectAll();
                                        Ext.getCmp('TrasparenzaVoiceEditWin').close();
                                        Ext.Msg.alert('Successo', 'Voce Trasparenza salvata');
                                    },
                                    failure: function(form, action) {
                                        Ext.Msg.alert('Attenzione', 'Voce Trasparenza NON salvata');
                                    }
                                });
                            },
                            iconCls: 'icon-disk',
                            text: 'Salva'
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'TrasparenzaVoiceEditForm',
                    itemId: 'TrasparenzaVoiceEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            flex: 1,
                            fieldLabel: 'Titolo',
                            labelAlign: 'right',
                            name: 'title',
                            readOnly: true
                        },
                        {
                            xtype: 'htmleditor',
                            height: 150,
                            id: 'TrasparenzaVoiceEditContent',
                            itemId: 'TrasparenzaVoiceEditContent',
                            fieldLabel: 'Contenuto',
                            labelAlign: 'right',
                            name: 'content'
                        },
                        {
                            xtype: 'htmleditor',
                            height: 150,
                            id: 'TrasparenzaVoiceEditReference',
                            itemId: 'TrasparenzaVoiceEditReference',
                            fieldLabel: 'Riferimenti',
                            labelAlign: 'right',
                            name: 'reference'
                        },
                        {
                            xtype: 'checkboxfield',
                            fieldLabel: 'Pubblicato',
                            labelAlign: 'right',
                            name: 'published',
                            uncheckedValue: 'off'
                        }
                    ]
                },
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'TrasparenzaVoiceEditLinkedDocumentsGrid',
                    itemId: 'TrasparenzaVoiceEditLinkedDocumentsGrid',
                    title: 'Documenti',
                    titleAlign: 'center',
                    emptyText: 'Nessun documento allegato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'TrasparenzaLinkedDocumentsForm',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'filename',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('TrasparenzaLinkedDocumentsPickerWin').show();

                                        Ext.getStore('TrasparenzaDocumentsForm').load();
                                    },
                                    hidden: true,
                                    iconCls: 'icon-attach',
                                    text: 'Allega Documenti'
                                },
                                {
                                    xtype: 'tbfill'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ArchiveDocumentUploadWin').archiveFrom = 'T';
                                        Ext.getCmp('ArchiveDocumentUploadWin').show();
                                    },
                                    hidden: true,
                                    id: 'TrasparenzaVoiceEditArchiveNewBtn',
                                    itemId: 'TrasparenzaVoiceEditArchiveNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Archivia nuovo'
                                },
                                {
                                    xtype: 'form',
                                    id: 'UploadFromTrasparenzaFrm',
                                    layout: 'fit',
                                    url: '/mc2-api/archive/document_file',
                                    items: [
                                        {
                                            xtype: 'filefield',
                                            name: 'file',
                                            buttonOnly: true,
                                            buttonText: 'Carica nuovo',
                                            listeners: {
                                                change: {
                                                    fn: me.onFilefieldChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onTrasparenzaVoiceEditLinkedDocumentsGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    flex: 1,
                    hidden: true,
                    id: 'TrasparenzaVoiceEditLinkedDocumentEditMn',
                    itemId: 'TrasparenzaVoiceEditLinkedDocumentEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('TrasparenzaVoiceEditLinkedDocumentsGrid').getSelectionModel().getSelection()[0],
                                    storeDocs = Ext.getStore('TrasparenzaLinkedDocumentsForm');

                                storeDocs.remove(record);
                            },
                            id: 'contextTrasparenzaVoiceEditLinkedDocumentDelete',
                            itemId: 'contextTrasparenzaVoiceEditLinkedDocumentDelete',
                            iconCls: 'icon-cancel',
                            text: 'Rimuovi'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onFilefieldChange: function(filefield, value, eOpts) {
        Ext.getCmp('UploadFromTrasparenzaFrm').submit({
            success: function(form, action) {
                var r = Ext.decode(action.response.responseText);
                Ext.getCmp('TrasparenzaVoiceEditLinkedDocumentsGrid').getStore().add(r.results);
            }
        });
    },

    onTrasparenzaVoiceEditLinkedDocumentsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('TrasparenzaVoiceEditLinkedDocumentEditMn').showAt([newX,newY]);
    }

});