/*
 * File: app/view/ArchiveDossierNewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDossierNewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDossierNewWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    id: 'ArchiveDossierNewWin',
    width: 400,
    layout: 'fit',
    title: 'Nuovo fascicolo',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'DossierNewFrm',
                    itemId: 'DossierNewFrm',
                    bodyPadding: 10,
                    title: '',
                    items: [
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            fieldLabel: 'Nome',
                            name: 'name',
                            allowBlank: false,
                            regex: /^[A-Za-z0-9 ]+$/,
                            regexText: 'Sono ammessi solo lettere e numeri'
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            id: 'dossierPathCmb',
                            itemId: 'dossierPathCmb',
                            fieldLabel: 'Fascicolo padre',
                            name: 'path',
                            displayField: 'name',
                            store: 'ArchiveDossiers'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var form = Ext.getCmp('DossierNewFrm').getForm(),
                                            fromDossier=typeof Ext.getCmp('ArchiveDossierNewWin').path !== 'undefined',
                                            path;

                                        if (form.isValid()) {
                                            path = form.getValues().path + '/';
                                            if(!form.getValues().path) {
                                                path = '';
                                            }
                                            Ext.Ajax.request({
                                                url: '/mc2-api/archive/dossier',
                                                method: 'POST',
                                                params: {
                                                    name: path + form.getValues().name
                                                },
                                                success:  function(res) {
                                                    var r = Ext.decode(res.responseText);
                                                    if (r.success === true) {
                                                        if(!fromDossier){
                                                            Ext.getStore('ArchiveDocumentDossierLinked').add(r.results);
                                                            Ext.getCmp('ArchiveDossierNewWin').close();
                                                        } else {
                                                            var pathId = '/' + Ext.getCmp('ArchiveDossierNewWin').path;
                                                            if(Ext.getCmp('ArchiveDossierNewWin').path<0) {
                                                                pathId = '';
                                                            }
                                                            Ext.getStore('ArchiveDocumentDossier').load({
                                                                url: Ext.getStore('ArchiveDocumentDossier').getProxy().url + pathId
                                                            });
                                                            Ext.getCmp('ArchiveDossierNewWin').close();
                                                        }
                                                    }
                                                }
                                            });
                                        }
                                    },
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});