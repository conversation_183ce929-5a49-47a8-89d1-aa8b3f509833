/*
 * File: app/view/ProtocolCorrespondentEditOriginPickerWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolCorrespondentEditOriginPickerWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolCorrespondentEditOriginPickerWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.toolbar.Paging',
        'Ext.button.Button',
        'Ext.toolbar.Fill',
        'Ext.form.field.Text',
        'Ext.selection.CheckboxModel'
    ],

    height: 250,
    id: 'ProtocolCorrespondentEditOriginPickerWin',
    itemId: 'ProtocolCorrespondentEditOriginPickerWin',
    width: 400,
    resizable: false,
    layout: 'fit',
    title: 'Seleziona un record',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    border: false,
                    id: 'ProtocolCorrespondentEditOriginPickerGrid',
                    itemId: 'ProtocolCorrespondentEditOriginPickerGrid',
                    emptyText: 'Nessun record trovato',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ProtocolCorrespondentsOriginsForm',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'title',
                            text: 'Denominazione',
                            flex: 1
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'pagingtoolbar',
                            dock: 'bottom',
                            displayInfo: true,
                            store: 'ProtocolCorrespondentsOriginsForm'
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        // If a record is selected, set record data into form
                                        var form = Ext.getCmp('ProtocolCorrespondentEditForm').getForm(),
                                            record = Ext.getCmp('ProtocolCorrespondentEditOriginPickerGrid').getSelectionModel().getSelection()[0];

                                        Ext.getCmp('ProtocolCorrespondentEditWin').setLoading(true);
                                        Ext.Ajax.request({
                                            url: '/mc2/applications/employees/employee/read.php?q_id=' + record.get('correspondent_type_id'),
                                            success: function(response)
                                            {
                                                r = Ext.decode(response.responseText);
                                                r = r.results[0];
                                                r.title = r.surname + ' ' + r.name;
                                                r.address = r.res_address;
                                                r.city_id = r.res_city_id;
                                                r.zipcode = r.res_cap;
                                                r.phone = r.res_phone_num;
                                                r.fax = r.res_fax;
                                                r.email = r.res_email;
                                                r.web = r.web;

                                                Ext.getCmp('ProtocolCorrespondentEditForm').getForm().setValues(r);
                                                Ext.getCmp('ProtocolCorrespondentEditCorrespondentTypeId').setValue(record.get('correspondent_type_id'));

                                                if (record.get('note')) {
                                                    Ext.getCmp('ProtocolCorrespondentEditNote').setValue(record.get('note'));
                                                }

                                                legal = Ext.getCmp('ProtocolCorrespondentEditLegalPerson').items;
                                                if (record.get('legal_person')) {
                                                    legal = legal.items[1];
                                                } else {
                                                    legal = legal.items[0];
                                                }
                                                legal.setValue(true);

                                                Ext.getCmp('ProtocolCorrespondentEditOriginPickerWin').close();
                                                Ext.getCmp('ProtocolCorrespondentEditWin').setLoading(false);
                                            },
                                        });



                                    },
                                    disabled: true,
                                    id: 'ProtocolCorrespondentEditOriginPickerBindBtn',
                                    itemId: 'ProtocolCorrespondentEditOriginPickerBindBtn',
                                    iconCls: 'icon-link',
                                    text: 'Abbina'
                                },
                                {
                                    xtype: 'tbfill'
                                },
                                {
                                    xtype: 'textfield',
                                    emptyText: 'Ricerca...',
                                    listeners: {
                                        change: {
                                            fn: me.onTextfieldChange,
                                            buffer: 1500,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        mode: 'SINGLE',
                        checkOnly: true
                    }),
                    listeners: {
                        selectionchange: {
                            fn: me.onProtocolCorrespondentEditBindPickerGridSelectionChange,
                            scope: me
                        }
                    }
                }
            ],
            listeners: {
                close: {
                    fn: me.onProtocolCorrespondentEditOriginPickerWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onTextfieldChange: function(field, newValue, oldValue, eOpts) {
        var store = Ext.getStore('ProtocolCorrespondentsOriginsForm');

        store.clearFilter(true);

        if (newValue) {
            store.filter('title', newValue);
        } else {
            store.clearFilter();
        }
    },

    onProtocolCorrespondentEditBindPickerGridSelectionChange: function(model, selected, eOpts) {
        if (selected.length < 1) {
            Ext.getCmp('ProtocolCorrespondentEditOriginPickerBindBtn').setDisabled(true);
        } else {
            Ext.getCmp('ProtocolCorrespondentEditOriginPickerBindBtn').setDisabled(false);
        }
    },

    onProtocolCorrespondentEditOriginPickerWinClose: function(panel, eOpts) {
        Ext.getStore('ProtocolCorrespondentsOriginsForm').clearFilter(true);
        Ext.getStore('ProtocolCorrespondentsOriginsForm').removeAll();
    }

});