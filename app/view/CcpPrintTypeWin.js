/*
 * File: app/view/CcpPrintTypeWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpPrintTypeWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpPrintTypeWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Date'
    ],

    print: null,
    height: 135,
    id: 'CcpPrintTypeWin',
    width: 324,
    resizable: false,
    title: 'Stampa riepilogo dati filtrati',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'center'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var rec = {},
                                    gridName = 'CcpMovementsStudentGrid';

                                rec.newSpool = 1;
                                rec.namespace = 'CCP';
                                rec.type = 'PDF';
                                rec.mime = 'application/pdf';
                                rec.print = Ext.getCmp('CcpPrintTypeGrouping').getValue();
                                rec.kind = Ext.getCmp('CcpPrintTypeType').getValue();

                                rec.subject_type = 'S';
                                rec.subject_id = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id');
                                rec.payment_accountable_date_end = Ext.getCmp('CcpPrintLastPaymentDate').getValue();


                                rec.sort = [];
                                Ext.each(Ext.getCmp(gridName).getStore().getSorters(), function(sorter){
                                    rec.sort = rec.sort.concat({
                                        "property": sorter.property,
                                        "direction": sorter.direction
                                    });
                                });
                                rec.sort = Ext.encode(rec.sort);

                                Ext.Ajax.request({
                                    url: '/mc2-api/core/print',
                                    params: rec,
                                    success: function(response, opts) {
                                        var res = Ext.decode(response.responseText);
                                        mc2ui.app.showNotifyPrint(res);
                                    }
                                });
                            },
                            hidden: true,
                            id: 'CcpPrintMovementFromStudentBtn',
                            text: 'Stampa dettaglio'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var rec = {},
                                    gridName;

                                if (Ext.getCmp('CcpPrintTypeType').getValue() === 'M' || Ext.getCmp('CcpPrintTypeType').getValue() === 'R') {
                                    rec = Ext.getCmp('CcpMovementsFilterForm').getForm().getValues();

                                    if(Ext.getCmp('CcpPrintTypeType').getValue() === 'M'){
                                        gridName = 'CcpMovementsGrid';
                                    } else {
                                        gridName = 'CcpReceiptsGrid';
                                    }
                                } else {
                                    rec = Ext.getCmp('CcpPaymentsFilterForm').getForm().getValues();
                                    gridName = 'CcpPaymentsGrid';
                                }

                                rec.newSpool = 1;
                                rec.namespace = 'CCP';
                                rec.type = 'PDF';
                                rec.mime = 'application/pdf';
                                rec.print = Ext.getCmp('CcpPrintTypeGrouping').getValue();
                                rec.kind = Ext.getCmp('CcpPrintTypeType').getValue();
                                rec.payment_accountable_date_end = Ext.getCmp('CcpPrintLastPaymentDate').getValue();

                                if (rec.subject_type === 'O') {
                                    rec.subject_data = rec.query;
                                }

                                rec.sort = [];
                                Ext.each(Ext.getCmp(gridName).getStore().getSorters(), function(sorter){
                                    rec.sort = rec.sort.concat({
                                        "property": sorter.property,
                                        "direction": sorter.direction
                                    });
                                });
                                rec.sort = Ext.encode(rec.sort);

                                if(rec.kind == 'R'){
                                    rec.print = 'Residuals' + rec.print;
                                }

                                Ext.Ajax.request({
                                    url: '/mc2-api/core/print',
                                    params: rec,
                                    success: function(response, opts) {
                                        var res = Ext.decode(response.responseText);
                                        mc2ui.app.showNotifyPrint(res);
                                    }
                                });
                            },
                            id: 'CcpPrintMovementFromMovementBtn',
                            iconCls: 'icon-printer',
                            text: 'Stampa'
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'combobox',
                    hidden: true,
                    id: 'CcpPrintTypeType',
                    margin: 10,
                    width: 135,
                    fieldLabel: 'Dati',
                    labelAlign: 'right',
                    labelWidth: 40,
                    name: 'kind',
                    value: 'M',
                    allowBlank: false,
                    allowOnlyWhitespace: false,
                    editable: false,
                    forceSelection: true,
                    queryMode: 'local',
                    store: 'CcpPrintTypes',
                    valueField: 'id',
                    listeners: {
                        change: {
                            fn: me.onCcpPrintTypeTypeChange,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'combobox',
                    id: 'CcpPrintTypeGrouping',
                    margin: 10,
                    width: 280,
                    fieldLabel: 'Raggruppamento',
                    labelAlign: 'right',
                    labelWidth: 130,
                    name: 'grouping',
                    value: 'Filtered',
                    allowBlank: false,
                    allowOnlyWhitespace: false,
                    editable: false,
                    forceSelection: true,
                    queryMode: 'local',
                    store: 'CcpPrintGroupings',
                    valueField: 'id'
                },
                {
                    xtype: 'datefield',
                    id: 'CcpPrintLastPaymentDate',
                    width: 280,
                    fieldLabel: 'Data finale pagamenti',
                    labelWidth: 130,
                    name: 'payment_accountable_date_end',
                    format: 'd/m/Y',
                    submitFormat: 'Y-m-d'
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpPrintTypeWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCcpPrintTypeTypeChange: function(field, newValue, oldValue, eOpts) {
        var store = Ext.getCmp('CcpPrintTypeGrouping').getStore();
        store.load();
        if(Ext.getCmp('CcpPrintTypeType').getValue() == 'R'){
            Ext.getCmp('CcpPrintTypeGrouping').setValue('Filtered');
            // For residuals print are also able "Nessuno" and "Debitore"
            store.removeAt(4);
            store.removeAt(3);
            store.removeAt(1);
        }
    },

    onCcpPrintTypeWinShow: function(component, eOpts) {
        Ext.getCmp('CcpPrintTypeGrouping').getStore().load();

    }

});