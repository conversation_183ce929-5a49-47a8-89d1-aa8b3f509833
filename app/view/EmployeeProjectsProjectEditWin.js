/*
 * File: app/view/EmployeeProjectsProjectEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeProjectsProjectEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeProjectsProjectEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Hidden',
        'Ext.form.FieldSet',
        'Ext.form.field.Number',
        'Ext.form.field.Checkbox',
        'Ext.form.field.Date',
        'Ext.form.field.TextArea'
    ],

    id: 'EmployeeProjectsProjectEditWin',
    width: 500,
    resizable: false,
    title: 'Progetto',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'EmployeeProjectsProjectEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var data = Ext.getCmp('EmployeeProjectsProjectEditForm').getForm().getValues(),
                                            sProjects = Ext.getStore('PersonnelProjects'),
                                            grid = Ext.getCmp('EmployeeProjectsManagementProjectsGrid'),
                                            a = 'aggiunto',
                                            newProject = data.id === "";

                                        // Saves the data
                                        if (!newProject) {
                                            a = 'aggiornato';
                                            var r = grid.getSelectionModel().getSelection()[0];
                                            r.set('name', data.name);
                                            r.set('aggregate_code', data.aggregate_code);
                                            r.set('aggregate_number', data.aggregate_number);
                                            r.set('start_date', data.start_date);
                                            r.set('end_date', data.end_date);
                                            r.set('hour_insertions_end_date', data.hour_insertions_end_date);
                                            r.set('year', data.year);
                                            r.set('suspended', data.suspended);
                                            r.set('description', data.description);
                                            r.set('objectives', data.objectives);
                                            r.set('responsibles', data.responsibles);
                                            r.set('goods_services', data.goods_services);
                                            r.set('human_resources', data.human_resources);
                                        } else {
                                            sProjects.add(data);
                                        }

                                        // Syncs the record
                                        sProjects.sync({
                                            callback: function() {
                                                sProjects.load();
                                            },
                                            success: function(form, action) {
                                                grid.getSelectionModel().deselectAll();
                                                Ext.getCmp('EmployeeProjectsProjectEditWin').close();
                                                Ext.Msg.alert('Successo', 'Progetto ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Progetto NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'hiddenfield',
                            name: 'id'
                        },
                        {
                            xtype: 'textfield',
                            flex: 1,
                            padding: '0 0 0 10',
                            fieldLabel: 'Denominazione',
                            labelAlign: 'right',
                            name: 'name'
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            title: 'Voce aggregato (bilancio)',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch',
                                        padding: '0 0 5 0'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            width: 130,
                                            fieldLabel: 'Codice',
                                            labelAlign: 'right',
                                            name: 'aggregate_code',
                                            maxLength: 1
                                        },
                                        {
                                            xtype: 'numberfield',
                                            padding: '0 0 0 95',
                                            width: 220,
                                            fieldLabel: 'Numero',
                                            labelAlign: 'right',
                                            name: 'aggregate_number',
                                            hideTrigger: true,
                                            minValue: 0
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                padding: '0 0 5 0'
                            },
                            items: [
                                {
                                    xtype: 'numberfield',
                                    padding: '0 160 0 10',
                                    width: 170,
                                    fieldLabel: 'Anno',
                                    labelAlign: 'right',
                                    name: 'year',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    hideTrigger: true,
                                    allowDecimals: false,
                                    allowExponential: false,
                                    maxValue: 2099,
                                    minValue: 2000
                                },
                                {
                                    xtype: 'checkboxfield',
                                    name: 'suspended',
                                    boxLabel: 'Sospeso'
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            layout: 'vbox',
                            title: 'Periodo validità',
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch',
                                        padding: '0 0 5 0'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            EndDateField: 'EmployeeProjectsProjectEditEndDate',
                                            width: 220,
                                            fieldLabel: 'Inizio',
                                            labelAlign: 'right',
                                            name: 'start_date',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c'
                                        },
                                        {
                                            xtype: 'datefield',
                                            StartDateField: 'EmployeeProjectsProjectEditStartDate',
                                            padding: '0 0 0 5',
                                            width: 220,
                                            fieldLabel: 'Termine',
                                            labelAlign: 'right',
                                            name: 'end_date',
                                            vtype: 'daterange',
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'c'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'datefield',
                                    width: 220,
                                    fieldLabel: 'Fine Inserimento',
                                    labelAlign: 'right',
                                    name: 'hour_insertions_end_date',
                                    editable: false,
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'c'
                                }
                            ]
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            padding: '0 0 0 10',
                            fieldLabel: 'Descrizione',
                            labelAlign: 'right',
                            name: 'description'
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            title: 'Riferimenti',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textareafield',
                                            flex: 1,
                                            padding: '0 5 0 0',
                                            fieldLabel: 'Obiettivi',
                                            labelAlign: 'top',
                                            name: 'objectives'
                                        },
                                        {
                                            xtype: 'textareafield',
                                            flex: 1,
                                            padding: '0 0 0 5',
                                            fieldLabel: 'Responsabili',
                                            labelAlign: 'top',
                                            name: 'responsibles'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textareafield',
                                            flex: 1,
                                            padding: '0 5 0 0',
                                            fieldLabel: 'Beni e Servizi',
                                            labelAlign: 'top',
                                            name: 'goods_services'
                                        },
                                        {
                                            xtype: 'textareafield',
                                            flex: 1,
                                            padding: '0 0 0 5',
                                            fieldLabel: 'Risorse umane',
                                            labelAlign: 'top',
                                            name: 'human_resources'
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});