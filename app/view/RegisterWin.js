/*
 * File: app/view/RegisterWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.RegisterWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.RegisterWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Number',
        'Ext.grid.column.Action',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 442,
    id: 'RegistroWin',
    itemId: 'RegistroWin',
    width: 750,
    layout: 'fit',
    title: 'Registro protocolli',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    title: '',
                    store: 'Register',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'register_number',
                            text: 'Numero'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'code',
                            text: 'Codice',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                var dd = new Date(value);

                                return Ext.Date.format(dd, 'd/m/Y');
                            },
                            dataIndex: 'close_date',
                            text: 'Data'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'creator_person',
                            text: 'Creato da'
                        },
                        {
                            xtype: 'numbercolumn',
                            dataIndex: 'first_registration_number',
                            text: 'Da numero',
                            format: '0'
                        },
                        {
                            xtype: 'numbercolumn',
                            dataIndex: 'last_registration_number',
                            text: 'A numero',
                            format: '0'
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 50,
                            defaultWidth: 50,
                            align: 'center',
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        Ext.widget('RegisterDetailWin').show();
                                        Ext.getCmp('RegisterDetailWin').showDetail(record.get('id'));
                                    },
                                    iconCls: 'icon-information'
                                },
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        var date = record.get('archived'),
                                            archive_document = record.get('archive_document_id'),
                                            register_id = record.get('id'),
                                            amfs = Ext.getStore('ArchiveMetadataFiles');


                                        if(!archive_document) {
                                            // Generazione della cartella in segreteria digitale
                                            Ext.MessageBox.confirm(
                                            'CONFERMA',
                                            'Verrà creata in segreteria digitale una cartella con il documento in questione. Continuare?',
                                            function(res){
                                                if(res == 'yes') {
                                                    var dd = new Date(record.get('close_date'));

                                                    Ext.getCmp('RegistroWin').setLoading(true);
                                                    Ext.Ajax.request({
                                                        method: 'POST',
                                                        url: '/mc2-api/archive/document',
                                                        params: {
                                                            origin_id: 4,
                                                            assign_to_user: mc2ui.app.settings.uid,
                                                            assign_from_user: mc2ui.app.settings.uid,
                                                            short_description: 'Registro di protocollo del ' + Ext.Date.format(dd, 'd/m/Y'),
                                                            protocol_register_id: register_id
                                                        },
                                                        success: function(res) {
                                                            Ext.getCmp('RegistroWin').setLoading(false);
                                                            var r = Ext.decode(res.responseText),
                                                                remoteRecord;
                                                            if (r.success === true){

                                                                // Creare oggetto document
                                                                var rr = Ext.create('mc2ui.model.ArchiveDocument', {
                                                                    id : r.results.id
                                                                });

                                                                Ext.widget('ArchiveMetadataFileWin').show();
                                                                Ext.getCmp('ArchiveMetadataFileWin').record = rr;
                                                                Ext.getStore('RemoteClass').filter('code', 'cad_regprot');
                                                                remoteRecord = Ext.getStore('RemoteClass').getRange()[0];
                                                                Ext.getStore('RemoteClass').clearFilter();
                                                                Ext.getCmp('ArchiveRemoteMetadataCmb').select(remoteRecord);

                                                            } else {
                                                                Ext.Msg.alert('ERROR', r.message);
                                                            }
                                                        }
                                                    });
                                                }
                                            }
                                            );


                                        } else {
                                            // Già conservata => Vedi dettaglio
                                            if(date){
                                                Ext.Ajax.request({
                                                    url: '/mc2-api/archive/document_file?document=' + archive_document,
                                                    success: function(r){
                                                        Ext.widget('ArchiveMetadataFileWin').show();
                                                        Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                                        Ext.getCmp('ArchiveMetadataFileTb').disable();
                                                        Ext.getCmp('ArchiveMetadataFileGrid').disable();
                                                        res = Ext.decode(r.responseText);
                                                        Ext.each(res.results, function(a){
                                                            a.metadata.name = a.filename;
                                                            amfs.add(a.metadata);
                                                        });
                                                    }
                                                });
                                            } else { // Da conservare => setta il record precedente nella finestra
                                                Ext.Ajax.request({
                                                    method: 'GET',
                                                    url: '/mc2-api/archive/document/' + archive_document,
                                                    success: function(res) {

                                                        var r = Ext.decode(res.responseText);
                                                        if (r.success === true){

                                                            // Creare oggetto document
                                                            var rr = Ext.create('mc2ui.model.ArchiveDocument', r.results);

                                                            Ext.widget('ArchiveMetadataFileWin').show();
                                                            Ext.getCmp('ArchiveMetadataFileWin').record = rr;
                                                        } else {
                                                            Ext.Msg.alert('ERROR', r.message);
                                                        }
                                                    }
                                                });

                                            }

                                        }

                                        /*
                                        if ((archive_document && !date) || !archive_document) {

                                        amfs.removeAll();
                                        if(date){
                                        Ext.Ajax.request({
                                        url: '/mc2-api/archive/document_file?document=' + archive_document,
                                        success: function(r){
                                        Ext.widget('ArchiveMetadataFileWin').show();
                                        Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                        Ext.getCmp('ArchiveMetadataFileTb').disable();
                                        Ext.getCmp('ArchiveMetadataFileGrid').disable();
                                        res = Ext.decode(r.responseText);
                                        Ext.each(res.results, function(a){
                                        a.metadata.name = a.filename;
                                        amfs.add(a.metadata);
                                        });
                                        }
                                        });
                                        } else {
                                        Ext.widget('ArchiveMetadataFileWin').show();
                                        Ext.getCmp('ArchiveMetadataFileWin').record = record;
                                        }
                                        }*/
                                    },
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var date = r.get('archived'),
                                            archive_document = r.get('archive_document_id');

                                        if (archive_document) {
                                            if (!date) {
                                                return 'icon-database';
                                            } else {
                                                return 'icon-accept';
                                            }
                                        } else {
                                            return 'icon-database';
                                        }
                                    },
                                    iconCls: 'icon-information'
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('RegisterAddWin').show();
                            },
                            iconCls: 'icon-add',
                            text: 'Nuovo'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onRegistroWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onRegistroWinShow: function(component, eOpts) {
        Ext.getStore('RemoteClass').load();
    }

});