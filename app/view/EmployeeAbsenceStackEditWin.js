/*
 * File: app/view/EmployeeAbsenceStackEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeAbsenceStackEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeAbsenceStackEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Checkbox',
        'Ext.form.field.Hidden',
        'Ext.form.FieldSet',
        'Ext.form.field.Date',
        'Ext.form.field.Number',
        'Ext.form.Label',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    id: 'EmployeeAbsenceStackEditWin',
    itemId: 'EmployeeAbsenceStackEditWin',
    width: 300,
    autoScroll: true,
    resizable: false,
    layout: 'fit',
    title: 'Monteore',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'EmployeeAbsenceStackEditForm',
                    itemId: 'EmployeeAbsenceStackEditForm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default'
                    ],
                    bodyPadding: 10,
                    header: false,
                    url: '/mc2/applications/employees/absence_stacks/write.php',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            id: 'EmployeeAbsenceStackEditDenomination',
                            itemId: 'EmployeeAbsenceStackEditDenomination',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            inputId: 'denomination',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'combobox',
                            id: 'EmployeeAbsenceStackEditUnit',
                            itemId: 'EmployeeAbsenceStackEditUnit',
                            fieldLabel: 'Unità',
                            labelAlign: 'right',
                            value: 'h',
                            inputId: 'unit',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            editable: false,
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'AbsenceStackUnits',
                            valueField: 'id',
                            listeners: {
                                change: {
                                    fn: me.onComboboxChange1,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'checkboxfield',
                            id: 'EmployeeAbsenceStackEditRecover',
                            itemId: 'EmployeeAbsenceStackEditRecover',
                            fieldLabel: 'Recupero',
                            labelAlign: 'right',
                            inputId: 'recover',
                            inputValue: 't',
                            uncheckedValue: 'f'
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            id: 'EmployeeAbsenceStackEditId',
                            itemId: 'EmployeeAbsenceStackEditId',
                            fieldLabel: 'Label',
                            inputId: 'id'
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            title: 'Reset',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    id: 'EmployeeAbsenceStackEditResetType',
                                    itemId: 'EmployeeAbsenceStackEditResetType',
                                    fieldLabel: 'Tipo',
                                    labelAlign: 'right',
                                    labelWidth: 90,
                                    name: 'reset_type',
                                    value: 0,
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'StackResetTypes',
                                    valueField: 'id',
                                    listeners: {
                                        change: {
                                            fn: me.onComboboxChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'combobox',
                                    disabled: true,
                                    hidden: true,
                                    id: 'EmployeeAbsenceStackEditResetToStackId',
                                    itemId: 'EmployeeAbsenceStackEditResetToStackId',
                                    fieldLabel: 'Destinazione',
                                    labelWidth: 90,
                                    name: 'reset_to_stack_id',
                                    editable: false,
                                    displayField: 'denomination',
                                    forceSelection: true,
                                    store: 'AbsenceStacks',
                                    valueField: 'id'
                                },
                                {
                                    xtype: 'datefield',
                                    disabled: true,
                                    id: 'EmployeeAbsenceStackEditResetDate',
                                    itemId: 'EmployeeAbsenceStackEditResetDate',
                                    fieldLabel: 'Data',
                                    labelAlign: 'right',
                                    labelWidth: 90,
                                    name: 'reset_date',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    altFormats: 'Y-m-d',
                                    format: 'F',
                                    startDay: 1,
                                    submitFormat: 'Y-m-d'
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'middle'
                                    },
                                    items: [
                                        {
                                            xtype: 'numberfield',
                                            disabled: true,
                                            id: 'EmployeeAbsenceStackEditResetDefaultQuota',
                                            itemId: 'EmployeeAbsenceStackEditResetDefaultQuota',
                                            width: 180,
                                            fieldLabel: 'Quota',
                                            labelAlign: 'right',
                                            labelWidth: 90,
                                            name: 'reset_default_quota',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            hideTrigger: true,
                                            allowExponential: false,
                                            autoStripChars: true,
                                            minValue: 0,
                                            listeners: {
                                                change: {
                                                    fn: me.onEmployeeAbsenceStackEditResetDefaultQuotaChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'label',
                                            disabled: true,
                                            id: 'EmployeeAbsenceStackEditUnitLabel',
                                            itemId: 'EmployeeAbsenceStackEditUnitLabel',
                                            padding: '0 0 0 5',
                                            text: 'minuti'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'checkboxfield',
                                    disabled: true,
                                    id: 'EmployeeAbsenceStackEditResetUpdateQuota',
                                    itemId: 'EmployeeAbsenceStackEditResetUpdateQuota',
                                    labelWidth: 225,
                                    name: 'update_quota',
                                    boxLabel: 'Aggiornare quota su tutto il personale',
                                    uncheckedValue: 'off'
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onComboboxChange1: function(field, newValue, oldValue, eOpts) {
        var unitLabel = Ext.getCmp('EmployeeAbsenceStackEditUnitLabel'),
            quota = Ext.getCmp('EmployeeAbsenceStackEditResetDefaultQuota');

        if (newValue === 'h') {
            unitLabel.setText('minuti');
            quota.fireEvent('change', quota, quota.getValue());
        } else if (newValue === 'd') {
            unitLabel.setText('giorni');
        } else {
            unitLabel.setText('()');
        }
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {
        var date = Ext.getCmp('EmployeeAbsenceStackEditResetDate'),
        //    toStack = Ext.getCmp('EmployeeAbsenceStackEditResetToStackId'),
            amount = Ext.getCmp('EmployeeAbsenceStackEditResetDefaultQuota'),
            update = Ext.getCmp('EmployeeAbsenceStackEditResetUpdateQuota'),
            unitLabel = Ext.getCmp('EmployeeAbsenceStackEditUnitLabel');

        if (newValue === 0) {
            date.setDisabled(true);
            date.setValue(null);
            amount.setValue(null);
            amount.setDisabled(true);
            update.setValue(false);
            update.setDisabled(true);
            unitLabel.setDisabled(true);
        //    toStack.setValue(null);
        //    toStack.setDisabled(true);
        } else {
            if (newValue === 1 || newValue === 3 || newValue === 5) {
                date.setDisabled(false);
            } else {
                date.setDisabled(true);
                date.setValue(null);
            }
            // TODO: Checks if the default amount was previously set and set it to this DB value
            amount.setValue(0);
            amount.setDisabled(false);
            update.setDisabled(false);
            unitLabel.setDisabled(false);
        //    toStack.setDisabled(false);
        }

        Ext.getCmp('EmployeeAbsenceStackEditForm').isValid();
    },

    onEmployeeAbsenceStackEditResetDefaultQuotaChange: function(field, newValue, oldValue, eOpts) {
        var unit = Ext.getCmp('EmployeeAbsenceStackEditUnit');
        if (unit.getValue() === 'h' && newValue !== parseInt(newValue)) {
            field.setValue(parseInt(newValue));
        }
    },

    onButtonClick: function(button, e, eOpts) {
        var kinds_store = Ext.getCmp('AbsenceKindLinkedGrid').getStore(),
            stacks_store = Ext.getCmp('AbsenceStackGrid').getStore(),
            pnl = Ext.getCmp('EmployeeAbsenceStackEditForm'),
            form = pnl.getForm();

        pnl.setLoading(true);
        form.submit({
            success: function(action, response) {
                Ext.getCmp('AbsenceStackGrid').getSelectionModel().deselectAll();
                Ext.getCmp('AbsenceKindLinkedGrid').getSelectionModel().deselectAll();
                Ext.getCmp('AbsenceKindLinkedGrid').setDisabled(true);
                stacks_store.load();
                pnl.setLoading(false);
                Ext.getCmp('EmployeeAbsenceStackEditWin').close();
            }
        });
    }

});