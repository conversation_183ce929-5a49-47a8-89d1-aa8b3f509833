/*
 * File: app/view/CcpLinkedAdditionalsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpLinkedAdditionalsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpLinkedAdditionalsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.grid.column.Number'
    ],

    permissible: true,
    id: 'CcpLinkedAdditionalsWin',
    minHeight: 200,
    width: 400,
    resizable: false,
    title: 'Addizionali abbinate',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'CcpLinkedAdditionalsGrid',
                    itemId: 'CcpLinkedAdditionalsGrid',
                    header: false,
                    emptyText: 'Nessuna Addizionale presente.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'CcpLinkedAdditionals',
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('positive')) {
                                            return 'icon-control_add';
                                        } else {
                                            return 'icon-control_remove';
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('positive')) {
                                            return 'Addizionale positiva';
                                        } else {
                                            return 'Addizionale negativa';
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'numbercolumn',
                            draggable: false,
                            width: 110,
                            resizable: false,
                            align: 'right',
                            dataIndex: 'amount',
                            hideable: false,
                            text: 'Valore'
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return '%';
                                } else {
                                    return '';
                                }
                            },
                            width: 20,
                            dataIndex: 'percentual'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});