/*
 * File: app/view/CcpServiziWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpServiziWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpServiziWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 457,
    width: 480,
    layout: 'fit',
    title: 'Stampa servizi',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpServicePrintFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        if(Ext.getCmp('CcpServicePrintFrm').isValid()) {
                                            var filter = Ext.getCmp('CcpServicePrintFrm').getValues(),
                                                students = Ext.getCmp('CcpStudentsPrint').getChecked(),
                                                //filter = {},
                                                s = [],
                                                last;

                                            //filter.printSettings = Ext.encode(rec);

                                            for (var k in students) {
                                                if (students[k].data.leaf === true) {
                                                    last = students[k].data.id.split('/').length - 1;
                                                    s.push(students[k].data.id.split('/')[last]);
                                                }
                                            }

                                            filter.students = Ext.encode(s);
                                            filter.services = Ext.encode(filter.services);
                                            filter.marketplace_students = filter.marketplace_students == 'tutti';


                                            filter.newSpool = 1;
                                            filter.print = 'Marketplace';
                                            filter.namespace = 'CCP';
                                            filter.type = 'PDF';
                                            filter.mime = 'application/pdf';

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: filter,
                                                success: function(response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        }
                                    },
                                    text: 'Stampa'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'combobox',
                            fieldLabel: 'Servizi',
                            name: 'services',
                            allowBlank: false,
                            emptyText: 'Seleziona uno o più servizi ...',
                            displayField: 'descrizione',
                            forceSelection: true,
                            multiSelect: true,
                            queryMode: 'local',
                            store: 'CcpServizi',
                            valueField: 'id_marketplace'
                        },
                        {
                            xtype: 'combobox',
                            fieldLabel: 'Raggruppamento',
                            name: 'grouping',
                            value: 'NESSUNO',
                            allowBlank: false,
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'CcpServiceGrouping',
                            valueField: 'id'
                        },
                        {
                            xtype: 'combobox',
                            fieldLabel: 'Tutti gli studenti',
                            name: 'marketplace_students',
                            value: 'tutti',
                            allowBlank: false,
                            forceSelection: true,
                            store: [
                                [
                                    'tutti',
                                    'Tutti'
                                ],
                                [
                                    'servizi',
                                    'Solo studenti con servizi'
                                ]
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            height: 250,
                            id: 'CcpStudentsPrint',
                            width: 400,
                            title: 'Studenti',
                            store: 'CcpStudentsTree',
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    dataIndex: 'text',
                                    text: 'Nodes',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'value',
                                    text: 'Value'
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onCcpStudentsPrintCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCcpStudentsPrintCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);
    },

    onWindowShow: function(component, eOpts) {
        Ext.getStore('CcpStudentsTree').load();
    }

});