/*
 * File: app/view/CcpExcelMovementExportWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpExcelMovementExportWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpExcelMovementExportWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    layout: 'fit',
    title: 'Esportazione excel',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpExportMovementExcelFrm',
                    bodyPadding: 10,
                    title: '',
                    items: [
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            fieldLabel: 'Data iniziale pagamenti',
                            labelWidth: 150,
                            name: 'payment_accountable_date_start',
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        },
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            fieldLabel: 'Data finale pagamenti',
                            labelWidth: 150,
                            name: 'payment_accountable_date_end',
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var rec = {},
                                    gridName,
                                    data = Ext.getCmp('CcpExportMovementExcelFrm').getValues();

                                rec = Ext.getCmp('CcpMovementsFilterForm').getForm().getValues();

                                if(data.payment_accountable_date_end) {
                                    rec.payment_accountable_date_end = data.payment_accountable_date_end;
                                }
                                if(data.payment_accountable_date_start) {
                                    rec.payment_accountable_date_start = data.payment_accountable_date_start;
                                }

                                gridName = 'CcpMovementsGrid';


                                rec.newSpool = 1;
                                rec.namespace = 'CCP';
                                rec.type = 'XLS';
                                rec.mime = 'application/vnd.ms-excel';
                                rec.print = 'ExportMovements';

                                if (rec.subject_type === 'O') {
                                    rec.subject_data = rec.query;
                                }

                                rec.sort = [];
                                Ext.each(Ext.getCmp(gridName).getStore().getSorters(), function(sorter){
                                    rec.sort = rec.sort.concat({
                                        "property": sorter.property,
                                        "direction": sorter.direction
                                    });
                                });
                                rec.sort = Ext.encode(rec.sort);

                                if(rec.kind == 'R'){
                                    rec.print = 'Residuals' + rec.print;
                                }

                                Ext.Ajax.request({
                                    url: '/mc2-api/core/print',
                                    params: rec,
                                    success: function(response, opts) {
                                        var res = Ext.decode(response.responseText);
                                        mc2ui.app.showNotifyPrint(res);
                                    }
                                });
                            },
                            iconCls: 'icon-printer',
                            text: 'Stampa'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});