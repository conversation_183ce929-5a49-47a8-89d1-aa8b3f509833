/*
 * File: app/view/CcpDepositWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpDepositWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpDepositWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.form.field.Date',
        'Ext.form.field.ComboBox',
        'Ext.grid.column.Number',
        'Ext.form.field.Number',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.plugin.RowEditing'
    ],

    height: 309,
    id: 'CcpDepositWin',
    itemId: 'CcpDepositWin',
    width: 1067,
    autoScroll: true,
    layout: 'fit',
    title: 'Storico Versamenti',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'CcpDepositsGrd',
                    autoScroll: true,
                    title: '',
                    store: 'CcpDeposits',
                    columns: [
                        {
                            xtype: 'datecolumn',
                            dataIndex: 'accountable_date',
                            text: '<b>Date</b>',
                            format: 'd/m/Y',
                            editor: {
                                xtype: 'datefield',
                                name: 'accountable_date',
                                format: 'd/m/Y',
                                submitFormat: 'c',
                                listeners: {
                                    change: {
                                        fn: me.onDatefieldChange,
                                        scope: me
                                    }
                                }
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 100,
                            align: 'center',
                            dataIndex: 'payment_method_text',
                            text: 'Metodo',
                            editor: {
                                xtype: 'combobox',
                                name: 'payment_method_id',
                                displayField: 'name',
                                queryMode: 'local',
                                store: 'CcpPaymentMethods',
                                valueField: 'id'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 126,
                            dataIndex: 'payer_surname',
                            text: 'Cognome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                name: 'payer_surname'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 134,
                            dataIndex: 'payer_name',
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                name: 'payer_name'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 129,
                            dataIndex: 'payer_fiscal_code',
                            text: 'Codice fiscale',
                            editor: {
                                xtype: 'textfield',
                                name: 'payer_fiscal_code'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'payer_address',
                            text: 'Indirizzo',
                            editor: {
                                xtype: 'textfield',
                                name: 'payer_address'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'payer_city',
                            text: 'Città',
                            editor: {
                                xtype: 'textfield',
                                name: 'payer_city'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 64,
                            dataIndex: 'payer_province',
                            text: 'Provincia',
                            editor: {
                                xtype: 'textfield',
                                name: 'payer_province'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 54,
                            align: 'center',
                            dataIndex: 'payer_zip_code',
                            text: 'CAP',
                            editor: {
                                xtype: 'textfield',
                                name: 'payer_zip_code'
                            }
                        },
                        {
                            xtype: 'numbercolumn',
                            align: 'right',
                            dataIndex: 'amount',
                            text: '<b>Totale</b>',
                            editor: {
                                xtype: 'numberfield',
                                width: 100,
                                name: 'amount'
                            }
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 35,
                            align: 'center',
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        /*if(record.get('locked') === true) {
                                        Ext.Msg.alert('ERRORE',  'Non è possibile eliminare il deposito perchè importato');
                                        return;
                                        }*/

                                        Ext.getStore('CcpDeposits').remove(record);
                                        Ext.getStore('CcpDeposits').sync({
                                            success: function(r) {
                                                var res = Ext.decode(r.operations[0].response.responseText);

                                                if(res.success === false) {
                                                    Ext.Msg.alert('ERRORE',  res.message);
                                                }
                                            }

                                        });
                                        setTimeout(function() {
                                            Ext.getCmp('CcpStudentsGrd').filterByStudent(Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id'));
                                        }, 500);
                                    },
                                    iconCls: 'icon-cancel'
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var today = new Date();

                                        Ext.getStore('CcpDeposits').insert(0, {
                                            amount: 0,
                                            credits_id: Ext.getCmp('CcpCreditsGrd').getSelectionModel().getSelection()[0].get('id'),
                                            payer_type: 'S',
                                            operation_date: today,
                                            accountable_date: today
                                        });

                                        Ext.getCmp('CcpDepositsGrd').editingPlugin.startEdit(0);
                                    },
                                    iconCls: 'icon-add',
                                    text: 'Aggiungi deposito'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                },
                                beforeedit: {
                                    fn: me.onRowEditingBeforeEdit,
                                    scope: me
                                },
                                canceledit: {
                                    fn: me.onRowEditingCanceledit,
                                    scope: me
                                }
                            }
                        })
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onDatefieldChange: function(field, newValue, oldValue, eOpts) {
        if(newValue) field.set('operation_date', newValue);
    },

    onRowEditingEdit: function(editor, context, eOpts) {

        Ext.getStore('CcpDeposits').sync();

        setTimeout(function() {
            Ext.getCmp('CcpStudentsGrd').filterByStudent(Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id'));
            Ext.getStore('CcpDeposits').load({
                params: {
                    credit_id: Ext.getCmp('CcpDepositWin').credit_id
                }
            });
        }, 500);
    },

    onRowEditingBeforeEdit: function(editor, context, eOpts) {
        if(context.record.get('id')) {
            return false;
        }
    },

    onRowEditingCanceledit: function(editor, context, eOpts) {
        if(!context.record.get('id')) Ext.getStore('CcpDeposits').remove(context.record);
    }

});