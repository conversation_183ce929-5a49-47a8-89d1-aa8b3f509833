/*
 * File: app/view/EmployeeProjectsEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeProjectsEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeProjectsEditWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.grid.column.Column',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 240,
    id: 'EmployeeProjectsEditWin',
    itemId: 'EmployeeProjectsEditWin',
    width: 400,
    title: 'Modifica i progetti collegati alla persona',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'BudgetActivitiesGrid',
                    itemId: 'BudgetActivitiesGrid',
                    header: false,
                    title: 'My Grid Panel',
                    store: 'BudgetActivities',
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        enableKeyNav: false,
                        checkOnly: true
                    }),
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            width: 48,
                            align: 'center',
                            dataIndex: 'aggreg_code',
                            text: 'Cod'
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 42,
                            align: 'center',
                            dataIndex: 'aggreg_nr',
                            text: 'Num'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            text: 'Descrizione',
                            flex: 1
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        var activities = Ext.getCmp('BudgetActivitiesGrid').getSelectionModel().getSelection();

        var activitie_ids = [];

        activities.forEach(function(el){
            activitie_ids = activitie_ids.concat(el.get('activ_id'));
        });


        Ext.getCmp('EmployeeProjectsGrid').setLoading();
        Ext.Ajax.request({
            url: '/mc2/applications/employees/projects/write.php',
            params: {
                activities: Ext.encode(activitie_ids),
                employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id
            },
            success: function(){
                Ext.getStore('EmployeeProjects').load({
                    params:{
                        employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id
                    }
                });
                Ext.getCmp('EmployeeProjectsEditWin').close();
                Ext.getCmp('EmployeeProjectsGrid').setLoading(false);
            }
        });
    }

});