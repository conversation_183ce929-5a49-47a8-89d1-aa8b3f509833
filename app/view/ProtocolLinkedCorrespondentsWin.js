/*
 * File: app/view/ProtocolLinkedCorrespondentsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolLinkedCorrespondentsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolLinkedCorrespondentsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View'
    ],

    height: 250,
    id: 'ProtocolProtocolLinkedCorrespondentsWin',
    itemId: 'ProtocolProtocolLinkedCorrespondentsWin',
    width: 400,
    resizable: false,
    title: '<PERSON><PERSON><PERSON> / <PERSON><PERSON>tari abbinati',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'ProtocolLinkedCorrespondentsGrid',
                    itemId: 'ProtocolLinkedCorrespondentsGrid',
                    emptyText: 'Nessun corrispondente abbinato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ProtocolLinkedCorrespondents',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'title',
                            hideable: false,
                            text: 'Nominativo',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'correspondent_type_text',
                            hideable: false,
                            text: 'Tipo'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});