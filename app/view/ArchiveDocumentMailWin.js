/*
 * File: app/view/ArchiveDocumentMailWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDocumentMailWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDocumentMailWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.grid.View'
    ],

    height: 359,
    width: 833,
    layout: 'fit',
    title: 'Elenco mail',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    store: 'ArchiveDocumentMails',
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            width: 40,
                            dataIndex: 'out',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (v === true){
                                            return 'icon-email_go';
                                        }
                                        return '';
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'from',
                            text: 'Da',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 277,
                            dataIndex: 'subject',
                            text: 'Oggetto'
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if(value > 0){
                                    return '<img src="./resources/icons/attach.png">';
                                }
                                return '';
                            },
                            width: 60,
                            dataIndex: 'attachments',
                            text: 'Allegato'
                        },
                        {
                            xtype: 'datecolumn',
                            align: 'center',
                            dataIndex: 'date',
                            text: 'Data'
                        },
                        {
                            xtype: 'datecolumn',
                            align: 'center',
                            dataIndex: 'sent',
                            text: 'Data invio'
                        }
                    ],
                    viewConfig: {
                        listeners: {
                            itemdblclick: {
                                fn: me.onViewItemDblClick,
                                scope: me
                            }
                        }
                    }
                }
            ]
        });

        me.callParent(arguments);
    },

    onViewItemDblClick: function(dataview, record, item, index, e, eOpts) {
        Ext.widget('ArchiveMailViewWin').show();
        /*
        Ext.getCmp('ArchiveMailViewWin').setTitle(record.get('subject'));
        Ext.getCmp('ArchiveMailFrm').getForm().loadRecord(record);
        Ext.getCmp('MessageMailCnt').getEl().setHTML(record.get('message_text'));
        Ext.getStore('ArchiveMailAttachments').load({params: {account: record.get('account'), mail: record.get('id')}});
        */


        Ext.Ajax.request({
            url: Ext.getStore('ArchiveMails').getProxy().url + '/' + record.get('id'),
            success: function(res){
                var r = Ext.decode(res.responseText);

                Ext.getCmp('ArchiveMailViewWin').setTitle(r.results.subject);

                Ext.getCmp('ArchiveMailFrm').getForm().setValues(r.results);
                Ext.getCmp('MessageMailCnt').getEl().setHTML(r.results.message);

                Ext.getStore('ArchiveMailAttachments').load({
                    params: {
                        account: r.results.account,
                        mail: r.results.id
                    }
                });

            }
        });
    }

});