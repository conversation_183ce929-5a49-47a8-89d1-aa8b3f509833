/*
 * File: app/view/CcpAttestazionePrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpAttestazionePrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpAttestazionePrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.form.FieldSet',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    height: 226,
    width: 448,
    layout: 'fit',
    title: 'Attestazione/Dichiarazione',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpAttestazionePrintFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'numberfield',
                            fieldLabel: 'Numero',
                            name: 'number',
                            hideTrigger: true
                        },
                        {
                            xtype: 'datefield',
                            fieldLabel: 'Data emissione',
                            name: 'creation_date',
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d',
                            listeners: {
                                render: {
                                    fn: me.onDatefieldRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            title: 'Periodo',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'datefield',
                                    fieldLabel: 'Inizio',
                                    name: 'start_date',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d'
                                },
                                {
                                    xtype: 'datefield',
                                    fieldLabel: 'Fine',
                                    name: 'end_date',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d',
                                    listeners: {
                                        render: {
                                            fn: me.onDatefieldRender1,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'bottom',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var filter = Ext.getCmp('CcpAttestazionePrintFrm').getForm().getValues();


                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/print',
                                            params: {
                                                newSpool: 1,
                                                print: mc2ui.app.settings.prints.declaration,
                                                namespace: 'CCP',
                                                type: 'PDF',
                                                mime: 'application/pdf',
                                                number: filter.number,
                                                creation_date: filter.creation_date,
                                                start_date: filter.start_date,
                                                end_date: filter.end_date,
                                                subject_id: Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id')
                                            },
                                            success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                            }
                                        });
                                    },
                                    text: 'Stampa'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onDatefieldRender: function(component, eOpts) {
        var dd = new Date();
        component.setValue(dd);
    },

    onDatefieldRender1: function(component, eOpts) {
        var dd = new Date();
        component.setValue(dd);
    }

});