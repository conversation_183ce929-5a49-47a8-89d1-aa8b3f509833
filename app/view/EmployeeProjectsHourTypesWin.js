/*
 * File: app/view/EmployeeProjectsHourTypesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeProjectsHourTypesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeProjectsHourTypesWin',

    requires: [
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.toolbar.Fill',
        'Ext.form.field.Text',
        'Ext.toolbar.Paging',
        'Ext.grid.column.Number',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 400,
    id: 'EmployeeProjectsHourTypesWin',
    itemId: 'EmployeeProjectsHourTypesWin',
    width: 700,
    resizable: false,
    layout: 'fit',
    title: 'Progetti e Tipi di Ora',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'tabpanel',
                    border: false,
                    activeTab: 0,
                    items: [
                        {
                            xtype: 'panel',
                            border: false,
                            layout: 'fit',
                            title: 'Progetti',
                            tabConfig: {
                                xtype: 'tab',
                                iconCls: 'icon-application_osx_cascade'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    border: false,
                                    id: 'EmployeeProjectsManagementProjectsGrid',
                                    header: false,
                                    emptyText: 'Nessun Progetto presente.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    sortableColumns: false,
                                    store: 'PersonnelProjects',
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onViewItemContextMenu,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            width: 40,
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('suspended')) {
                                                            return 'icon-control_pause';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        if (r.get('suspended')) {
                                                            return 'Il progetto è attualmente sospeso';
                                                        }
                                                    }
                                                },
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('expired')) {
                                                            return 'icon-clock_red';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        if (r.get('expired')) {
                                                            return 'Il progetto è terminato';
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 50,
                                            defaultWidth: 50,
                                            align: 'center',
                                            dataIndex: 'aggregate_code',
                                            text: 'Cod.'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 50,
                                            defaultWidth: 50,
                                            align: 'right',
                                            dataIndex: 'aggregate_number',
                                            text: 'Num.'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'name',
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'start_date',
                                            text: 'Inizio',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'end_date',
                                            text: 'Termine',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 60,
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                                    },
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('count_personnel') > 0) {
                                                            return 'icon-group_go';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        var cp = r.get('count_personnel');
                                                        if (cp > 0) {
                                                            return 'Il progetto è abbinato a ' + cp + 'Docent' + (cp > 1 ? 'i' : 'e');
                                                        }
                                                    }
                                                },
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                                    },
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('count_hour_types') > 0) {
                                                            return 'icon-clock_go';
                                                        }
                                                    },
                                                    getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                        var cht = r.get('count_hour_types');
                                                        if (cht > 0) {
                                                            return 'Il progetto contiene ' + cht + 'Tip' + (cht > 1 ? 'i' : 'o') + ' di Ora';
                                                        }
                                                    }
                                                },
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        var msg = '<div style="display: table">' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Anno:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('year') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Termine inserimento:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('hour_insertions_end_date') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Descrizione:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('description') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Obiettivi:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('objectives') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Responsabili:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('responsibles') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Beni e Servizi:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('goods_services') + '</div>' +
                                                        '</div>' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Risorse u:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('human_resources') + '</div>' +
                                                        '</div>' +
                                                        '</div>';
                                                        Ext.Msg.alert('Dettagli Progetto "' + record.get('name') + '"', msg);
                                                    },
                                                    iconCls: 'icon-information',
                                                    tooltip: 'Dettagli'
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('EmployeeProjectsProjectEditWin').show();
                                                    },
                                                    id: 'EmployeeProjectNewBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Nuovo'
                                                },
                                                {
                                                    xtype: 'tbfill'
                                                },
                                                {
                                                    xtype: 'textfield',
                                                    width: 200,
                                                    emptyText: 'Ricerca...',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onTextfieldChange,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'pagingtoolbar',
                                            dock: 'bottom',
                                            displayInfo: true,
                                            displayMsg: 'Record {0} - {1} di {2}',
                                            emptyMsg: 'Nessun Progetto presente',
                                            store: 'PersonnelProjects'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'panel',
                            border: false,
                            layout: 'fit',
                            title: 'Tipi di Ora',
                            tabConfig: {
                                xtype: 'tab',
                                iconCls: 'icon-time_go'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    permissible: true,
                                    border: false,
                                    id: 'EmployeeProjectsManagementHourTypesGrid',
                                    header: false,
                                    emptyText: 'Nessun Tipo di Ora definito.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    store: 'PersonnelHourTypes',
                                    viewConfig: {
                                        listeners: {
                                            itemcontextmenu: {
                                                fn: me.onViewItemContextMenu1,
                                                scope: me
                                            }
                                        }
                                    },
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            resizable: false,
                                            sortable: true,
                                            dataIndex: 'name',
                                            hideable: false,
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            width: 110,
                                            resizable: false,
                                            sortable: true,
                                            align: 'right',
                                            dataIndex: 'price',
                                            hideable: false,
                                            text: 'Retribuzione'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            resizable: false,
                                            sortable: true,
                                            align: 'right',
                                            dataIndex: 'inpdap_perc',
                                            hideable: false,
                                            text: 'INPDAP (%)'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            resizable: false,
                                            sortable: true,
                                            align: 'right',
                                            dataIndex: 'inps_perc',
                                            hideable: false,
                                            text: 'INPS (%)'
                                        },
                                        {
                                            xtype: 'numbercolumn',
                                            resizable: false,
                                            sortable: true,
                                            align: 'right',
                                            dataIndex: 'irap_perc',
                                            hideable: false,
                                            text: 'IRAP (%)'
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 20,
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        var msg = '<div style="display: table">' +
                                                        '<div style="display: table-row">' +
                                                        '<div style="display: table-cell"><b>Descrizione:&nbsp;</b></div>' +
                                                        '<div style="display: table-cell">' + record.get('description') + '</div>' +
                                                        '</div>' +
                                                        '</div>';
                                                        Ext.Msg.alert('Dettagli Tipo Ora "' + record.get('name') + '"', msg);
                                                    },
                                                    iconCls: 'icon-information',
                                                    tooltip: 'Dettagli'
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.widget('EmployeeProjectsHourTypeEditWin').show();
                                                    },
                                                    id: 'EmployeeHourTypeNewBtn',
                                                    iconCls: 'icon-add',
                                                    text: 'Nuovo'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    id: 'EmployeeProjectsManagementProjectMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('EmployeeProjectsManagementProjectsGrid').getSelectionModel().getSelection()[0];

                                Ext.widget('EmployeeProjectsProjectEditWin').show();
                                Ext.getCmp('EmployeeProjectsProjectEditForm').getForm().loadRecord(r);
                            },
                            id: 'contextEmployeeProjectEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                Ext.widget('EmployeeProjectsProjectHourTypeLink').show();
                            },
                            id: 'contextEmployeeProjectLink',
                            iconCls: 'icon-link',
                            text: 'Abbina Tipi di Ora'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('EmployeeProjectsManagementHourTypesGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: 'Eliminazione Progetto ' + r.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questo Progetto? ',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(a){
                                        if (a == 'yes') {
                                            sProjects = Ext.getStore('PersonnelProjects');
                                            sProjects.remove(r);
                                            sProjects.sync({
                                                callback: function () {
                                                    sProjects.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Progetto eliminato');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Progetto NON eliminato');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextEmployeeProjectDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    id: 'EmployeeProjectsManagementHourTypeMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('EmployeeProjectsManagementHourTypesGrid').getSelectionModel().getSelection()[0];

                                Ext.widget('EmployeeProjectsHourTypeEditWin').show();
                                Ext.getCmp('EmployeeProjectsHourTypeEditForm').getForm().loadRecord(r);
                            },
                            id: 'contextEmployeeHourTypeEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('EmployeeProjectsManagementHourTypesGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: 'Eliminazione Tipo di Ora ' + r.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questo Tipo di Ora?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(a){
                                        if (a == 'yes') {
                                            sHourTypes = Ext.getStore('PersonnelHourTypes');
                                            sHourTypes.remove(r);
                                            sHourTypes.sync({
                                                callback: function () {
                                                    sHourTypes.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Tipo di Ora eliminato');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Tipo di Ora NON eliminato');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextEmployeeHourTypeDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onViewItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('EmployeeProjectsManagementProjectMn').showAt([newX,newY]);
    },

    onTextfieldChange: function(field, newValue, oldValue, eOpts) {

    },

    onViewItemContextMenu1: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('EmployeeProjectsManagementHourTypeMn').showAt([newX,newY]);

        if (record.get('locked')) {
            Ext.getCmp('contextEmployeeHourTypeEdit').setDisabled(true);
            Ext.getCmp('contextEmployeeHourTypeDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextEmployeeHourTypeEdit').setDisabled(false);
            Ext.getCmp('contextEmployeeHourTypeDelete').setDisabled(false);
        }
    }

});