/*
 * File: app/view/CcpExtraTimeWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpExtraTimeWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpExtraTimeWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.form.field.ComboBox',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.grid.Panel',
        'Ext.grid.column.Number',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    height: 553,
    id: 'CcpExtraTimeWin',
    width: 615,
    layout: 'fit',
    title: 'Consolidamento',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpExtraTimeFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'container',
                            padding: '10 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    id: 'CcpExtratimeDateStart',
                                    margin: '0 10 0 0',
                                    fieldLabel: 'Data inizio',
                                    labelWidth: 120,
                                    name: 'start',
                                    allowBlank: false,
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d',
                                    listeners: {
                                        render: {
                                            fn: me.onDatefieldRender1,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    id: 'CcpExtratimeDateEnd',
                                    fieldLabel: 'Data fine',
                                    labelWidth: 120,
                                    name: 'end',
                                    allowBlank: false,
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d',
                                    listeners: {
                                        render: {
                                            fn: me.onDatefieldRender,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            id: 'CcpExtratimeServiceCmb',
                            fieldLabel: 'Servizio',
                            labelWidth: 120,
                            name: 'marketplace_id',
                            allowBlank: false,
                            displayField: 'descrizione',
                            queryMode: 'local',
                            store: 'CcpServizi',
                            valueField: 'id_marketplace',
                            listeners: {
                                select: {
                                    fn: me.onCcpExtratimeServiceCmbSelect,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            id: 'CcpExtraTimeStudents',
                            title: 'Studenti',
                            hideHeaders: true,
                            store: 'CcpStudentsTree',
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    dataIndex: 'text',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onCcpExtraTimeStudentsCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                },
                {
                    xtype: 'form',
                    hidden: true,
                    id: 'CcpExtraTimeMovementFrm',
                    bodyPadding: 10,
                    title: 'Informazioni movimento',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            id: 'CcpConsolidationmovementTypeCmb',
                            fieldLabel: 'Tipo movimento',
                            allowBlank: false,
                            displayField: 'name_school_year',
                            queryMode: 'local',
                            store: 'CcpTypes',
                            valueField: 'id'
                        },
                        {
                            xtype: 'datefield',
                            id: 'CcpConsolidationExpirationDate',
                            fieldLabel: 'Scadenza',
                            allowBlank: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d',
                            listeners: {
                                render: {
                                    fn: me.onCcpConsolidationExpirationDateRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'textfield',
                            id: 'CcpDescriptionMensaTxt',
                            fieldLabel: 'Descrizione',
                            name: 'description'
                        },
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            title: 'Riepilogo',
                            store: 'CcpConsolidations',
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'subject_data',
                                    text: 'Studente',
                                    flex: 1
                                },
                                {
                                    xtype: 'numbercolumn',
                                    align: 'right',
                                    dataIndex: 'value',
                                    text: 'Importo',
                                    format: '0000.00'
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'bottom',
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var form = Ext.getCmp('CcpExtraTimeFrm').getForm(),
                                    students = Ext.getCmp('CcpExtraTimeStudents').getChecked();

                                if(form.isValid()) {


                                    var s = [];
                                    for (var k in students) {
                                        if (students[k].data.leaf === true) {
                                            last = students[k].data.id.split('/').length - 1;
                                            s.push(students[k].data.id.split('/')[last]);
                                        }
                                    }

                                    if(s.length === 0) {
                                        Ext.Msg.alert('ERRORE', 'Seleziona almeno uno studente');
                                        return;
                                    }

                                    var params = form.getValues();
                                    params.students = Ext.encode(s);

                                    var record = Ext.getCmp('CcpExtratimeServiceCmb').findRecordByValue(params.marketplace_id).data,
                                        isMensa = record.tipo === 'CONSOLIDAMENTO_MENSA',
                                        url = !isMensa ? '/mc2-api/ccp/consolidation' : '/mc2-api/ccp/consolidation/mensa',
                                        method = !isMensa ? 'POST' : 'GET';

                                    if(isMensa) {
                                        var ss = [];
                                        Ext.each(students, function(v) {
                                            if (v.data.leaf === true) {
                                                var root = v.get('id').split('/'),
                                                    id = root[root.length-1];

                                                var movement_type_id = record.caratteristiche.standard;
                                                if(record.caratteristiche[v.get('school_address_id')]) {
                                                    movement_type_id = record.caratteristiche[v.get('school_address_id')];
                                                }
                                                ss.push({
                                                    subject_id: id,
                                                    subject_type: 'S',
                                                    subject_class: v.get('class') + v.get('section'),
                                                    subject_data: v.get('text'),
                                                    subject_school_address: v.get('school_address'),
                                                    subject_school_address_code: v.get('school_address_code'),
                                                    subject_school_address_id: v.get('school_address_id'),
                                                    value: 0,
                                                    movement_type_id: parseInt(movement_type_id)
                                                });
                                            }
                                        });
                                    }

                                    Ext.getCmp('CcpExtraTimeWin').setLoading(true);
                                    Ext.Ajax.request({
                                        url: url,
                                        method: method,
                                        params: params,
                                        timeout: 99999999,
                                        success: function(r) {
                                            Ext.getCmp('CcpExtraTimeWin').setLoading(false);

                                            var res = Ext.decode(r.responseText);
                                            if(res.success === true) {
                                                Ext.getCmp('CcpExtraTimeMovementFrm').show();
                                                Ext.getCmp('CcpExtraTimeFrm').hide();

                                                Ext.getCmp('CcpExtraTimeStep1Btn').hide();
                                                Ext.getCmp('CcpExtraTimeStep2Btn').show();

                                                Ext.getStore('CcpConsolidations').removeAll();
                                                if(!isMensa) {
                                                    Ext.getStore('CcpTypes').load({params: {section: 'EXTRATIME'}});
                                                    Ext.each(res.results, function(v) {
                                                        Ext.getStore('CcpConsolidations').add(v);
                                                    });
                                                } else {
                                                    Ext.getCmp('CcpConsolidationmovementTypeCmb').hide();
                                                    Ext.each(ss, function(s) {
                                                        var v = s;
                                                        v.value = res.results[v.subject_id].data.credito_rimasto;
                                                        if(v.value < 0) {
                                                            v.value *= -1;
                                                            Ext.getStore('CcpConsolidations').add(v);
                                                        }
                                                    });

                                                    var prevValues=Ext.getCmp('CcpExtraTimeFrm').getForm().getFieldValues(),
                                                        start=Ext.Date.format(prevValues.start,'d/m/Y'),
                                                        end=Ext.Date.format(prevValues.end,'d/m/Y');
                                                    Ext.getCmp('CcpDescriptionMensaTxt').setValue("Dal " + start + " al " + end);
                                                }



                                            }
                                        }
                                    });


                                }
                            },
                            id: 'CcpExtraTimeStep1Btn',
                            text: 'Procedi alla generazione delle anteprime dei movimenti'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var prevValues=Ext.getCmp('CcpExtraTimeFrm').getForm().getFieldValues(),
                                    start=Ext.Date.format(prevValues.start,'d/m/Y'),
                                    end=Ext.Date.format(prevValues.end,'d/m/Y'),
                                    //description = "Dal " + start + " al " + end,
                                    description = Ext.getCmp('CcpDescriptionMensaTxt').getValue(),
                                    isMensa = Ext.getCmp('CcpConsolidationmovementTypeCmb').isHidden() ? true : false,
                                    params={
                                        school_year: mc2ui.app.settings.mcDb.schoolYear,
                                        subject_school_year: mc2ui.app.settings.mcDb.schoolYear,
                                        creation_date: Ext.Date.format(new Date(), 'Y-m-d'),
                                        expirations: [
                                        {
                                            expiration: Ext.getCmp('CcpConsolidationExpirationDate').getSubmitValue(),
                                            description: description
                                        }
                                        ],
                                        type_id: Ext.getCmp('CcpConsolidationmovementTypeCmb').getValue(),
                                        subjects: []
                                    };

                                if (isMensa) delete params.type_id;

                                var ids = [];
                                Ext.each(Ext.getStore('CcpConsolidations').getRange(), function(v) {
                                    var sub = {
                                        id: v.get('subject_id'),
                                        type: v.get('subject_type'),
                                        class: v.get('subject_class'),
                                        data: v.get('subject_data'),
                                        school_address: v.get('subject_school_address'),
                                        school_address_code: v.get('subject_school_address_code'),
                                        value: v.get('value')
                                    };
                                    if (isMensa) sub.type_id = v.get('movement_type_id');
                                    ids.push(v.get('subject_id'));

                                    params.subjects.push(sub);
                                });

                                Ext.getCmp('CcpExtraTimeWin').setLoading(true);

                                Ext.Ajax.request({
                                    method:'POST',
                                    url: '/mc2-api/ccp/movement',
                                    jsonData: params,
                                    success: function(r){
                                        var res = Ext.decode(r.responseText);
                                        if(res.success === true) {
                                            Ext.getCmp('CcpExtraTimeWin').close();
                                            Ext.Msg.alert('SUCCESSO', 'Movimenti inseriti correttamente');

                                            var selectedStudents = Ext.getCmp("CcpStudentsGrd").getSelectionModel().getSelection();

                                            if(selectedStudents.length > 0) {
                                                Ext.getCmp("CcpStudentsGrd").filterByStudent(selectedStudents[0].get('db_id'));
                                            }
                                        }
                                    }
                                });
                            },
                            hidden: true,
                            id: 'CcpExtraTimeStep2Btn',
                            text: 'Inserisci movimenti'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpExtraTimeWinShow,
                    scope: me
                },
                close: {
                    fn: me.onCcpExtraTimeWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onDatefieldRender1: function(component, eOpts) {
        var dd = new Date();
        dd.setDate(dd.getDate() - 30);
        component.setValue(dd);
    },

    onDatefieldRender: function(component, eOpts) {
        component.setValue(new Date());
    },

    onCcpExtratimeServiceCmbSelect: function(combo, records, eOpts) {
        /*if(records[0].get('tipo') == 'CONSOLIDAMENTO_MENSA') {
            Ext.getCmp('CcpExtratimeDateStart').hide();
            Ext.getCmp('CcpExtratimeDateEnd').setFieldLabel('Data consolidamento');
        } else {
            Ext.getCmp('CcpExtratimeDateEnd').setFieldLabel('Data fine');
            Ext.getCmp('CcpExtratimeDateStart').show();
        }*/
    },

    onCcpExtraTimeStudentsCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);
    },

    onCcpConsolidationExpirationDateRender: function(component, eOpts) {
        var dd = new Date();
        dd.setDate(dd.getDate() - 1);
        component.setValue(dd);
    },

    onCcpExtraTimeWinShow: function(component, eOpts) {
        Ext.getStore('CcpServizi').load({
            params: {
                tipo: 'EXTRATIME,CONSOLIDAMENTO_MENSA'
            }
        });
        Ext.getCmp('CcpExtraTimeStudents').getStore().load();
    },

    onCcpExtraTimeWinClose: function(panel, eOpts) {
        Ext.getStore('CcpServizi').load();
        Ext.getStore('CcpTypes').load();
    }

});