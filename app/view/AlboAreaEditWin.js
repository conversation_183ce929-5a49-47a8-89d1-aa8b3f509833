/*
 * File: app/view/AlboAreaEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboAreaEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboAreaEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.TextArea',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 200,
    id: 'AlboAreaEditWin',
    itemId: 'AlboAreaEditWin',
    width: 400,
    title: 'Area',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'AlboAreaEditForm',
                    itemId: 'AlboAreaEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'AlboAreaEditId',
                            itemId: 'AlboAreaEditId',
                            name: 'id'
                        },
                        {
                            xtype: 'textfield',
                            id: 'AlboAreaEditName',
                            itemId: 'AlboAreaEditName',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            name: 'name',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            id: 'AlboAreaEditDescription',
                            itemId: 'AlboAreaEditDescription',
                            fieldLabel: 'Descrizione',
                            labelAlign: 'right',
                            name: 'description'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('AlboAreas'),
                                            form = Ext.getCmp('AlboAreaEditForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvata';

                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornata';
                                            record = store.getById(parseInt(values.id));
                                            record.set('name', values.name);
                                            record.set('description', values.description);
                                        } else {
                                            store.add({
                                                name: values.name,
                                                description: values.description
                                            });
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('AlboAreaEditWin').close();
                                                Ext.Msg.alert('Successo', 'Area ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Area NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});