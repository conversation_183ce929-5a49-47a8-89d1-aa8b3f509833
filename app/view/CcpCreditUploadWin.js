/*
 * File: app/view/CcpCreditUploadWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpCreditUploadWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpCreditUploadWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.File',
        'Ext.form.FieldSet',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 409,
    id: 'CcpCreditUploadWin',
    width: 618,
    layout: 'fit',
    title: 'Importa crediti',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpUploadCreditsFrm',
                    bodyPadding: 10,
                    title: '',
                    method: 'POST',
                    url: '/mc2-api/ccp/credits/import',
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            fieldLabel: 'Tipo di credito',
                            name: 'creditType',
                            value: 'Dote',
                            allowBlank: false,
                            displayField: 'description',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'CreditsType',
                            valueField: 'description'
                        },
                        {
                            xtype: 'filefield',
                            fieldLabel: '',
                            name: 'creditsFileUpload',
                            emptyText: 'Carica file ...'
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            id: 'CreditImportErrors',
                            autoScroll: true,
                            title: 'Report',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.getCmp('CreditImportErrors').removeAll();
                                Ext.getCmp('CcpCreditUploadWin').setLoading();
                                Ext.getCmp('CcpUploadCreditsFrm').getForm().submit({
                                    success: function(form, action) {


                                        if(action.result.error_report.path) {
                                            lb = Ext.create('Ext.form.Label', {
                                                html: action.result.error_report.date_time + ': <a href="/mc2-api/core/print/0?path='+action.result.error_report.path+'">Scarica report errori</a>',
                                                margin: '2 0'
                                            });
                                            Ext.getCmp('CreditImportErrors').add(lb);
                                        }


                                        if(action.result.success_report.path) {
                                            lb = Ext.create('Ext.form.Label', {
                                                html: action.result.success_report.date_time + ': <a href="/mc2-api/core/print/0?path='+action.result.success_report.path+'">Scarica report inserimenti</a>',
                                                margin: '2 0'
                                            });
                                            Ext.getCmp('CreditImportErrors').add(lb);
                                        }


                                        Ext.getCmp('CcpCreditUploadWin').setLoading(false);

                                    },
                                    failure: function(form, action) {
                                        Ext.getCmp('CcpCreditUploadWin').setLoading(false);
                                    }

                                });
                            },
                            text: 'Importa'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpCreditUploadWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCcpCreditUploadWinShow: function(component, eOpts) {
        Ext.getCmp('CreditImportErrors').removeAll();
        Ext.Ajax.request({
            url: '/mc2-api/core/reports',
            success: function(res) {
                var r = Ext.decode(res.responseText);
                if(r.success === true) {
                    if(r.results.IMPORTA_CREDITI_SUCCESS) {
                        lb = Ext.create('Ext.form.Label', {
                            html: r.results.IMPORTA_CREDITI_SUCCESS.xlsx.date_time + ': <a href="/mc2-api/core/print/0?path='+r.results.IMPORTA_CREDITI_SUCCESS.xlsx.path+'">Scarica report inserimenti</a>',
                            margin: '2 0'
                        });
                        Ext.getCmp('CreditImportErrors').add(lb);
                    }
                    if(r.results.IMPORTA_CREDITI_ERRORS) {
                        lb = Ext.create('Ext.form.Label', {
                            html: r.esults.IMPORTA_CREDITI_ERRORS.xlsx.date_time + ': <a href="/mc2-api/core/print/0?path='+r.results.IMPORTA_CREDITI_ERRORS.xlsx.path+'">Scarica report errori</a>',
                            margin: '2 0'
                        });
                        Ext.getCmp('CreditImportErrors').add(lb);
                    }
                }
            }
        });
    }

});