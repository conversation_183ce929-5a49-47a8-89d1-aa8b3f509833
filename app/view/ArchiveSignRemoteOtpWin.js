/*
 * File: app/view/ArchiveSignRemoteOtpWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveSignRemoteOtpWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveSignRemoteOtpWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Text',
        'Ext.form.field.Hidden',
        'Ext.button.Button'
    ],

    id: 'ArchiveSignRemoteOtpWin',
    itemId: 'ArchiveSignRemoteOtpWin',
    width: 400,
    layout: 'fit',
    title: 'Richiesta OTP',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'ArchiveSignRemoteOtpFrm',
                    itemId: 'ArchiveSignRemoteOtpFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            fieldLabel: 'OTP',
                            name: 'otp',
                            allowBlank: false
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            fieldLabel: 'Label',
                            name: 'format'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'vbox',
                                align: 'stretch',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var format = Ext.getCmp('ArchiveSignRemoteOtpFrm').getValues();

                                        Ext.getCmp('ArchiveSignRemoteOtpWin').setLoading();
                                        Ext.getCmp('ArchiveSignRemoteOtpFrm').submit({
                                            url: '/mc2-api/archive/sign/sign/' + Ext.getCmp('ArchiveSignRemoteOtpWin').record.get('id'),
                                            success: function() {
                                                Ext.getCmp('ArchiveSignRemoteOtpWin').close();
                                                Ext.Msg.alert('FIRMA', 'Documenti firmati correttamente');
                                                Ext.getStore('ArchiveDocumentsArchived').load();
                                                Ext.getStore('ArchiveDocumentsUser').load();
                                                Ext.getStore('ArchiveDocumentsOffice').load();
                                            },
                                            failure: function(form, res) {
                                                if (res.result.status === 307) {
                                                    Ext.widget('ArchiveSignRemoteLogin').show();
                                                    Ext.getCmp('ArchiveSignRemoteOtpWin').close();
                                                    Ext.getCmp('ArchiveSignRemoteLoginFrm').getForm().setValues(format);
                                                } else {
                                                    Ext.Msg.alert('ATTENZIONE','Errore durante l\'invio dei dati. Si prega di rieffettuare la procedura. Se l\'errore dovesse persistere contattare l\'assistenza');
                                                    Ext.getCmp('ArchiveSignRemoteOtpWin').close();
                                                }
                                            }
                                        });
                                    },
                                    flex: 1,
                                    formBind: true,
                                    id: 'ArchivveSignOtpRequestWin',
                                    itemId: 'ArchivveSignOtpRequestWin',
                                    text: 'Firma'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});