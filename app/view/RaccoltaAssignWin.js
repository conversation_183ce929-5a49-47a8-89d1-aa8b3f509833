/*
 * File: app/view/RaccoltaAssignWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.RaccoltaAssignWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.RaccoltaAssignWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.grid.column.Number',
        'Ext.toolbar.Toolbar',
        'Ext.form.Label',
        'Ext.toolbar.Spacer',
        'Ext.button.Button',
        'Ext.selection.CheckboxModel'
    ],

    height: 250,
    width: 400,
    layout: 'fit',
    title: 'Assegna a raccolta',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    title: '',
                    store: '<PERSON><PERSON><PERSON><PERSON>',
                    viewConfig: {
                        listeners: {
                            itemdblclick: {
                                fn: me.onViewItemClick1,
                                scope: me
                            }
                        }
                    },
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            width: 39,
                            align: 'center',
                            dataIndex: 'type',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if(v === 'R') {
                                            return 'icon-folder';
                                        }

                                        if(v === 'F') {
                                            return 'icon-book_addresses';
                                        }

                                        if(v === 'C') {
                                            return 'icon-application_view_list';
                                        }

                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'Name',
                            flex: 1
                        },
                        {
                            xtype: 'numbercolumn',
                            dataIndex: 'elements',
                            text: 'Elementi',
                            format: '00'
                        }
                    ],
                    listeners: {
                        boxready: {
                            fn: me.onGridpanelBoxReady1,
                            scope: me
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'label',
                                    padding: 5,
                                    text: 'My Label'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                    })
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onViewItemClick1: function(dataview, record, item, index, e, eOpts) {
        Ext.getStore('Raccoltes').clearFilter();
        Ext.getStore('Raccoltes').filter('parent', record.get('id'));
    },

    onGridpanelBoxReady1: function(component, width, height, eOpts) {
        Ext.getStore('Raccoltes').filter('parent', '0');
    },

    onWindowShow: function(component, eOpts) {
        Ext.getStore('Raccoltes').clearFilter();
        Ext.getStore('Raccoltes').filter('parent', '0');
    }

});