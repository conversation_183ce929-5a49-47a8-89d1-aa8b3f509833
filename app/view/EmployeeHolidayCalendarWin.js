/*
 * File: app/view/EmployeeHolidayCalendarWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeHolidayCalendarWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeHolidayCalendarWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.form.field.ComboBox',
        'Ext.form.Label',
        'Ext.view.View',
        'Ext.XTemplate'
    ],

    id: 'EmployeeHolidayCalendarWin',
    itemId: 'EmployeeHolidayCalendarWin',
    minHeight: 430,
    width: 720,
    resizable: false,
    title: 'Calendario festività',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch',
        pack: 'center'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    padding: '5 0 5 0',
                    layout: {
                        type: 'hbox',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            id: 'HolidayMonthCmb',
                            itemId: 'HolidayMonthCmb',
                            width: 80,
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            editable: false,
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'Months',
                            valueField: 'number',
                            listeners: {
                                change: {
                                    fn: me.onHolidayMonthCmbChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'label',
                            id: 'HolidayCalendarYearLbl',
                            itemId: 'HolidayCalendarYearLbl',
                            margin: '0 0 0 5'
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'container',
                    permissible: true,
                    flex: 1,
                    id: 'HolidayCalendarPnl',
                    itemId: 'HolidayCalendarPnl',
                    layout: {
                        type: 'vbox',
                        align: 'center'
                    },
                    items: [
                        {
                            xtype: 'dataview',
                            flex: 1,
                            id: 'HolidayCalendarView',
                            itemId: 'HolidayCalendarView',
                            padding: 10,
                            width: 664,
                            itemSelector: 'div.day-box',
                            itemTpl: [
                                '',
                                '<div class="day-box {css_class}" style="max-height:50px;">',
                                '    <p class="day-header {css_header}">{day}</p> ',
                                '</div>',
                                ''
                            ],
                            store: 'CalendarHoliday',
                            listeners: {
                                itemclick: {
                                    fn: me.onHolidayCalendarViewItemClick,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onEmployeeHolidayCalendarWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onHolidayMonthCmbChange: function(field, newValue, oldValue, eOpts) {
        var month = Ext.getCmp('HolidayMonthCmb').getValue();
        Ext.getStore('CalendarHoliday').load({
            params: {
                month: month
            }
        });
    },

    onHolidayCalendarViewItemClick: function(dataview, record, item, index, e, eOpts) {
        var month = Ext.getCmp('HolidayMonthCmb').getValue();
        if (record.raw.month == month) {
            Ext.getStore('CalendarHoliday').update({
            	params:{
                	day: record.get('day'),
                	month: record.get('month')
            	},
            	callback: function(){
        	        Ext.getStore('CalendarHoliday').load({
        	            params: {
        	                month: parseInt(Ext.getCmp('HolidayMonthCmb').getValue())
        	            }
        	        });
        	    }
        	});
        }
    },

    onEmployeeHolidayCalendarWinShow: function(component, eOpts) {
        var current_date = new Date();
        var current_month = parseInt(Ext.Date.format(current_date,'n'));
        Ext.getCmp('HolidayMonthCmb').setValue(current_month);
        Ext.getCmp('HolidayCalendarYearLbl').setText(mc2ui.app.settings.year);
    }

});