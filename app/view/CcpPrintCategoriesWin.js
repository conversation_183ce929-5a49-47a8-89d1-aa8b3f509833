/*
 * File: app/view/CcpPrintCategoriesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpPrintCategoriesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpPrintCategoriesWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Number',
        'Ext.form.field.Number',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.column.Action'
    ],

    height: 250,
    width: 400,
    layout: 'fit',
    title: 'Categorie di stampa',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'CcpPrintCategoriesGrd',
                    title: '',
                    store: 'CcpPrintOfCategories',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                name: 'name'
                            }
                        },
                        {
                            xtype: 'numbercolumn',
                            width: 70,
                            align: 'center',
                            dataIndex: 'ordering',
                            text: 'Ordine',
                            format: '0',
                            editor: {
                                xtype: 'numberfield',
                                name: 'ordering'
                            }
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 80,
                            align: 'center',
                            iconCls: '',
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        Ext.widget('CcpPrintCategoryMovementTypeWin').show();
                                        Ext.getCmp('CcpPrintCategoryMovementTypeWin').categoryId = record.get('id');
                                        Ext.getStore('CcpPrintCategoryMovementTypes').load({
                                            params: {
                                                ccp_print_category_id: record.get('id'),
                                            },
                                            callback: function (res, status) {
                                                if (status.success) {
                                                    Ext.each(res, function(value){
                                                        if (value.raw.selected === true) {
                                                            Ext.getCmp('CcpCategoryPrintTypeSelectedGrd').getSelectionModel().select(value, true);
                                                        }
                                                    });
                                                }

                                            }
                                        });
                                    },
                                    iconCls: 'icon-application_view_list'
                                },
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        Ext.Msg.confirm('ATTENZIONE', 'Cancellando la categoria verranno cancellati anche tutti i legami precedentemente salvati. Continuare?', function(btn) {
                                            if(btn=='yes') {
                                                Ext.getStore('CcpPrintOfCategories').remove(record);
                                                Ext.getStore('CcpPrintOfCategories').sync({
                                                    callback: function(res, res2) {
                                                        Ext.getStore('CcpPrintOfCategories').load();
                                                    }
                                                });
                                            }
                                        });
                                    },
                                    iconCls: 'icon-delete'
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getStore('CcpPrintOfCategories').add({});
                                        Ext.getCmp('CcpPrintCategoriesGrd').editingPlugin.startEdit(Ext.getStore('CcpPrintOfCategories').getRange().length-1);

                                    },
                                    text: 'Nuova'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                canceledit: {
                                    fn: me.onRowEditingCanceledit,
                                    scope: me
                                },
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                }
                            }
                        })
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onRowEditingCanceledit: function(editor, context, eOpts) {
        if(!context.record.get('id')) {
            Ext.getStore('CcpPrintOfCategories').load();
        }
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        Ext.getStore('CcpPrintOfCategories').sync({
            callback: function () {
                Ext.getStore('CcpPrintOfCategories').load();
            }
        });

    }

});