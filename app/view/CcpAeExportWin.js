/*
 * File: app/view/CcpAeExportWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpAeExportWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpAeExportWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.FieldSet',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    width: 415,
    layout: 'fit',
    title: 'Esportazione Agenzia delle Entrate',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpAeExportFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            flex: 1,
                            fieldLabel: 'Tipologia',
                            name: 'school_category',
                            value: 'scuola',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            forceSelection: true,
                            store: [
                                [
                                    'scuola',
                                    'Altre scuole'
                                ],
                                [
                                    'nido',
                                    'Asili nido'
                                ]
                            ],
                            listeners: {
                                change: {
                                    fn: me.onComboboxChange1,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            id: 'CcpAeExportYearCmb',
                            fieldLabel: 'Anno',
                            name: 'year',
                            editable: false,
                            displayField: 'year',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'Years',
                            valueField: 'year',
                            listeners: {
                                afterrender: {
                                    fn: me.onComboboxAfterRender,
                                    scope: me
                                },
                                change: {
                                    fn: me.onCcpAeExportYearCmbChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            fieldLabel: 'Tipo invio',
                            name: 'type',
                            value: 'ordinaria',
                            store: [
                                [
                                    'ordinaria',
                                    'Ordinaria'
                                ],
                                [
                                    'sostitutiva',
                                    'Sostitutiva'
                                ],
                                [
                                    'annullamento',
                                    'Annullamento'
                                ]
                            ],
                            listeners: {
                                change: {
                                    fn: me.onComboboxChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'textfield',
                            hidden: true,
                            id: 'CcpAeExportProtocolCodeTxt',
                            fieldLabel: 'Codice protocollo',
                            name: 'code'
                        },
                        {
                            xtype: 'fieldset',
                            id: 'CcpAeExportPeriodsFs',
                            padding: 5,
                            title: 'Periodo',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    id: 'CcpAeExportFromDt',
                                    fieldLabel: 'Da',
                                    labelWidth: 30,
                                    name: 'from',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d'
                                },
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    id: 'CcpAeExportToDt',
                                    margin: '0 0 0 5',
                                    fieldLabel: 'A',
                                    labelWidth: 30,
                                    name: 'to',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d'
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            id: 'CcpAeExportPaymentMethodsCmb',
                            fieldLabel: 'Tipo pagamento da includere',
                            name: 'payment_methods[]',
                            displayField: 'name',
                            multiSelect: true,
                            store: 'CcpPaymentMethods',
                            valueField: 'id',
                            listeners: {
                                afterrender: {
                                    fn: me.onCcpAeExportPaymentMethodsCmbAfterRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'checkboxfield',
                            flex: 1,
                            id: 'CcpExportAdeEmptyFiscalCodeCh',
                            fieldLabel: '',
                            name: 'empty_fiscal_code',
                            boxLabel: 'Impostare tutti i paganti con codice fiscale vuoto',
                            inputValue: '1',
                            uncheckedValue: '0'
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var params = Ext.getCmp('CcpAeExportFrm').getValues();
                                Ext.Ajax.request({
                                    headers: { 'Content-Type': 'application/octet-stream' },
                                    method: 'GET',
                                    url: '/mc2-api/ccp/payment_ae_export',
                                    timeout: 999999,
                                    params: params,
                                    success: function(res){
                                        var r = Ext.decode(res.responseText),
                                            zip = r.result,
                                            link = document.createElement('a');
                                        link.setAttribute('href', 'data:application/octet-stream;base64,' + zip);
                                        link.setAttribute('download', r.file_name);
                                        link.click();

                                    }
                                });
                            },
                            text: 'Esporta'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onComboboxChange1: function(field, newValue, oldValue, eOpts) {

        Ext.getCmp('CcpExportAdeEmptyFiscalCodeCh').show();
        if(newValue == 'nido') {
            Ext.getCmp('CcpExportAdeEmptyFiscalCodeCh').hide();
        }
    },

    onComboboxAfterRender: function(component, eOpts) {
        Ext.getCmp('CcpAeExportYearCmb').select(mc2ui.app.settings.year);
    },

    onCcpAeExportYearCmbChange: function(field, newValue, oldValue, eOpts) {

        if(newValue !== oldValue) {

            var from = new Date(newValue+'-01-01'),
                to = new Date(newValue+'-12-31');

            setTimeout(function(){
                Ext.getCmp('CcpAeExportFromDt').setValue(from);
                Ext.getCmp('CcpAeExportToDt').setValue(to);
            }, 500);


        }
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {

        if(newValue === 'annullamento') {
            Ext.getCmp('CcpAeExportPeriodsFs').hide();
            Ext.getCmp('CcpAeExportPaymentMethodsCmb').hide();
            Ext.getCmp('CcpAeExportProtocolCodeTxt').show();
        }
        if(newValue === 'sostitutiva') {
            Ext.getCmp('CcpAeExportProtocolCodeTxt').show();
        }
        if(newValue === 'ordinaria') {
            Ext.getCmp('CcpAeExportProtocolCodeTxt').hide();
            Ext.getCmp('CcpAeExportPeriodsFs').show();
            Ext.getCmp('CcpAeExportPaymentMethodsCmb').show();


        }
    },

    onCcpAeExportPaymentMethodsCmbAfterRender: function(component, eOpts) {
        Ext.getCmp('CcpAeExportPaymentMethodsCmb').select(Ext.getCmp('CcpAeExportPaymentMethodsCmb').getStore().getRange());
    }

});