/*
 * File: app/view/PaswdEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.PaswdEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.PaswdEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Text',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 135,
    id: 'PaswdEditWin',
    itemId: 'PaswdEditWin',
    width: 341,
    layout: 'fit',
    title: 'Modifica Password',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'EditUserPaswdFrm',
                    itemId: 'EditUserPaswdFrm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default'
                    ],
                    bodyPadding: 10,
                    url: '/mc2/applications/core/users/change_paswd.php',
                    items: [
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            id: 'EditUserPaswd',
                            itemId: 'EditUserPaswd',
                            fieldLabel: 'Nuova password',
                            labelAlign: 'right',
                            labelWidth: 150,
                            msgTarget: 'side',
                            inputId: 'passwd',
                            inputType: 'password',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            minLength: 8
                        },
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            id: 'EditUserPaswdR',
                            itemId: 'EditUserPaswdR',
                            fieldLabel: 'Ripeti nuova password',
                            labelAlign: 'right',
                            labelWidth: 150,
                            msgTarget: 'side',
                            inputId: 'passwdRepeat',
                            inputType: 'password',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            minLength: 8
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        var form = Ext.getCmp('EditUserPaswdFrm').getForm();
        form.submit({
            params:{
                user_id: Ext.getCmp('UsersGrid').getSelectionModel().getSelection()[0].get('user_id')
            },
            success : function(){
                Ext.getCmp('PaswdEditWin').close();
            }
        });
    }

});