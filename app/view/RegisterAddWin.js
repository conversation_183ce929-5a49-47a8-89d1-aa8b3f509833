/*
 * File: app/view/RegisterAddWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.RegisterAddWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.RegisterAddWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 121,
    id: 'RegisterAddWin',
    width: 342,
    layout: 'fit',
    title: 'Registra una giornata',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'RegisterAddForm',
                    itemId: 'RegisterAddForm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/protocol/register',
                    layout: {
                        type: 'hbox',
                        align: 'middle',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'datefield',
                            id: 'RegisterDate',
                            itemId: 'RegisterDate',
                            fieldLabel: 'Giorno',
                            name: 'date',
                            allowBlank: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('RegisterAddForm').setLoading();
                                        Ext.getCmp('RegisterAddForm').submit({
                                            success: function(e, res){
                                                Ext.getCmp('RegisterAddForm').setLoading(false);
                                                var r = Ext.decode(res.response.responseText);
                                                if (r.success === true){
                                                    Ext.getCmp('RegisterAddWin').close();
                                                    Ext.getStore('Register').load();
                                                }
                                            },
                                            failure: function(e, res){
                                                Ext.getCmp('RegisterAddForm').setLoading(false);
                                                var r = Ext.decode(res.response.responseText);
                                                Ext.Msg.alert('ERRORE', r.message);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onWindowShow: function(component, eOpts) {
        var sec = new Date() - 1000*60*60*24,
            dd = new Date(sec),
            dateStr = Ext.Date.format(dd, 'd/m/Y');

        Ext.getCmp('RegisterDate').setValue(dateStr);
    }

});