/*
 * File: app/view/CcpTypeEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpTypeEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpTypeEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Number',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Toolbar',
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.form.field.Date',
        'Ext.grid.column.Number',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.form.Label',
        'Ext.toolbar.Spacer',
        'Ext.grid.column.Action',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 683,
    id: 'CcpTypeEditWin',
    itemId: 'CcpTypeEditWin',
    width: 902,
    resizable: false,
    title: 'Tipo di movimento',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'CcpTypeEditForm',
                    itemId: 'CcpTypeEditForm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default'
                    ],
                    bodyPadding: 10,
                    header: false,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'container',
                            flex: 1,
                            id: 'CcpTypeEditableCnt',
                            itemId: 'CcpTypeEditableCnt',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Descrizione',
                                    labelAlign: 'right',
                                    name: 'name',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false
                                },
                                {
                                    xtype: 'radiogroup',
                                    padding: '0 0 0 100',
                                    fieldLabel: '',
                                    items: [
                                        {
                                            xtype: 'radiofield',
                                            name: 'incoming',
                                            boxLabel: 'Entrata',
                                            checked: true
                                        },
                                        {
                                            xtype: 'radiofield',
                                            name: 'incoming',
                                            boxLabel: 'Uscita',
                                            inputValue: 'off'
                                        }
                                    ],
                                    listeners: {
                                        change: {
                                            fn: me.onRadiogroupChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    maxWidth: 250,
                                    fieldLabel: 'Anno Scolastico',
                                    labelAlign: 'right',
                                    name: 'school_year',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    displayField: 'value',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'CcpTypeSchoolYears',
                                    valueField: 'value'
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    fieldLabel: 'Categoria',
                                    labelAlign: 'right',
                                    name: 'category_id',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    displayField: 'name',
                                    queryMode: 'local',
                                    store: 'CcpCategories',
                                    valueField: 'id'
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'CcpTypeSectionCmb',
                                    fieldLabel: 'Tipologia servizio',
                                    labelAlign: 'right',
                                    name: 'section',
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    autoSelect: false,
                                    forceSelection: true,
                                    store: [
                                        [
                                            '',
                                            '---'
                                        ],
                                        [
                                            'MCM',
                                            'Mensa'
                                        ]
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'checkboxfield',
                            flex: 1,
                            margins: '0 0 0 105',
                            hidden: true,
                            fieldLabel: '',
                            name: 'online_payment',
                            boxLabel: 'Abilita pagamento online tramite l\'app',
                            uncheckedValue: 'off'
                        },
                        {
                            xtype: 'combobox',
                            flex: 1,
                            fieldLabel: 'Pagamento online',
                            labelAlign: 'right',
                            name: 'online_payment_status',
                            store: [
                                [
                                    0,
                                    'Non abilitato'
                                ],
                                [
                                    1,
                                    'Abilitato sia per tipo che per movimenti'
                                ],
                                [
                                    2,
                                    'Abilitato solo per tipo'
                                ],
                                [
                                    3,
                                    'Abilitato solo per movimenti'
                                ]
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            id: 'CcpTypeContainerIvaCnt',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    fieldLabel: 'IVA',
                                    labelAlign: 'right',
                                    name: 'include_vat',
                                    boxLabel: '',
                                    inputValue: '1',
                                    uncheckedValue: '0',
                                    listeners: {
                                        change: {
                                            fn: me.onCheckboxfieldChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'numberfield',
                                    disabled: true,
                                    id: 'CcpTypeIvaNum',
                                    margin: '0 0 0 5',
                                    width: 35,
                                    name: 'vat',
                                    value: 22,
                                    hideTrigger: true
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            id: 'CcpBolloExempionCnt',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'CcpNoBolloCmb',
                                    fieldLabel: 'Natura esenzione IVA',
                                    labelAlign: 'right',
                                    name: 'vat_code_id',
                                    editable: false,
                                    displayField: 'description',
                                    forceSelection: true,
                                    store: 'CcpVatCodes',
                                    valueField: 'id',
                                    listeners: {
                                        afterrender: {
                                            fn: me.onCcpNoBolloCmbAfterRender,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            id: 'CcpBolloCalcCheck',
                                            fieldLabel: 'Bollo',
                                            labelAlign: 'right',
                                            name: 'bollo',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0'
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            margin: '0 0 0 15',
                                            fieldLabel: 'Bollo incluso',
                                            name: 'include_bollo',
                                            inputValue: '1',
                                            uncheckedValue: '0'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: '5 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    fieldLabel: 'Codice fattura',
                                    labelAlign: 'right',
                                    name: 'invoice_code'
                                },
                                {
                                    xtype: 'textfield',
                                    flex: 1.2,
                                    margin: '0 0 0 5',
                                    fieldLabel: 'Piano conti - Cod. Easy',
                                    labelAlign: 'right',
                                    labelWidth: 130,
                                    name: 'easy_code'
                                },
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    fieldLabel: 'Contropartita',
                                    labelAlign: 'right',
                                    labelWidth: 90,
                                    name: 'easy_code_contropartita'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            }
                        },
                        {
                            xtype: 'hiddenfield',
                            name: 'id'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: '5 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    id: 'CcpTypePaymentMail',
                                    fieldLabel: 'Mail pagamento',
                                    labelAlign: 'right',
                                    boxLabel: '',
                                    listeners: {
                                        change: {
                                            fn: me.onCcpTypePaymentMailChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'textfield',
                                    disabled: true,
                                    id: 'CcpTypePaymentMailTxt',
                                    margin: '0 0 0 5',
                                    fieldLabel: '',
                                    name: 'payment_mail'
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    fieldLabel: 'Credito',
                                    labelWidth: 50,
                                    name: 'ccp_credits_type',
                                    displayField: 'description',
                                    store: 'CreditsType',
                                    valueField: 'id'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    id: 'CcpExcludeCorrispettiviCheck',
                                    fieldLabel: 'Escludi dai corrispettivi',
                                    labelAlign: 'right',
                                    labelWidth: 150,
                                    name: 'exclude_corrispettivi',
                                    boxLabel: '',
                                    inputValue: '1',
                                    uncheckedValue: '0'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: 'fit',
                            items: [
                                {
                                    xtype: 'combobox',
                                    id: 'CcpAeCategoryCmb',
                                    fieldLabel: 'Categoria  per agenzia delle entrate',
                                    labelAlign: 'right',
                                    name: 'ccp_ae_category_id',
                                    value: 0,
                                    displayField: 'description',
                                    queryMode: 'local',
                                    store: 'CcpAeCategories',
                                    valueField: 'id',
                                    listeners: {
                                        afterrender: {
                                            fn: me.onCcpAeCategoryCmbAfterRender,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'tabpanel',
                            flex: 1,
                            activeTab: 0,
                            items: [
                                {
                                    xtype: 'panel',
                                    layout: 'fit',
                                    title: 'Rateizzazioni',
                                    items: [
                                        {
                                            xtype: 'gridpanel',
                                            height: 200,
                                            margin: '5 0 0 0',
                                            autoScroll: true,
                                            title: '',
                                            titleAlign: 'center',
                                            enableColumnHide: false,
                                            enableColumnMove: false,
                                            sortableColumns: false,
                                            store: 'CcpTypeSteps',
                                            columns: [
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'expiration',
                                                    text: 'Data',
                                                    editor: {
                                                        xtype: 'textfield'
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'description',
                                                    text: 'Descrizione',
                                                    flex: 1,
                                                    editor: {
                                                        xtype: 'textfield'
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'conto_risconti',
                                                    text: 'Conto risconti',
                                                    listeners: {
                                                        afterrender: function (component) {
                                                            if (mc2ui.app.settings.easyContiRicaviRisconti !== true) {
                                                                component.hide();
                                                            }
                                                        }
                                                    },
                                                    editor: {
                                                        xtype: 'textfield',
                                                        name: 'conto_risconti'
                                                    }
                                                },
                                                {
                                                    xtype: 'datecolumn',
                                                    align: 'center',
                                                    dataIndex: 'da_ratei',
                                                    text: 'Inizio ratei',
                                                    format: 'd/m/Y',
                                                    editor: {
                                                        xtype: 'datefield',
                                                        format: 'd/m/Y'
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'conto_crediti',
                                                    text: 'Conto crediti',
                                                    editor: {
                                                        xtype: 'textfield',
                                                        name: 'conto_crediti'
                                                    },
                                                    listeners: {
                                                        afterrender: function (component) {
                                                            if (mc2ui.app.settings.easyContiRicaviRisconti !== true) {
                                                                component.hide();
                                                            }
                                                        }
                                                    },
                                                },
                                                {
                                                    xtype: 'datecolumn',
                                                    align: 'center',
                                                    dataIndex: 'a_ratei',
                                                    text: 'Fine ratei',
                                                    format: 'd/m/Y',
                                                    editor: {
                                                        xtype: 'datefield',
                                                        format: 'd/m/Y'
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    dataIndex: 'conto_ricavi',
                                                    text: 'Conto ricavi',
                                                    editor: {
                                                        xtype: 'textfield'
                                                    },
                                                    listeners: {
                                                        afterrender: function (component) {
                                                            if (mc2ui.app.settings.easyContiRicaviRisconti !== true) {
                                                                component.hide();
                                                            }
                                                        }
                                                    },
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    dataIndex: 'value',
                                                    text: 'Importo',
                                                    editor: {
                                                        xtype: 'numberfield'
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    hidden: true,
                                                    dataIndex: 'ccp_type',
                                                    text: 'MyColumn40'
                                                },
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 25,
                                                    items: [
                                                        {
                                                            handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                                var typeId = Ext.getCmp('CcpTypeEditForm').getValues().id;

                                                                Ext.getStore('CcpTypeSteps').remove(record);

                                                                if (typeId) {
                                                                    Ext.getStore('CcpTypeSteps').sync();
                                                                }
                                                            },
                                                            iconCls: 'icon-delete'
                                                        }
                                                    ]
                                                }
                                            ],
                                            plugins: [
                                                Ext.create('Ext.grid.plugin.RowEditing', {
                                                    listeners: {
                                                        edit: {
                                                            fn: me.onRowEditingEdit,
                                                            scope: me
                                                        }
                                                    }
                                                })
                                            ],
                                            dockedItems: [
                                                {
                                                    xtype: 'toolbar',
                                                    dock: 'top',
                                                    items: [
                                                        {
                                                            xtype: 'label',
                                                            html: '<i style="color:red">Se la data non viene inserita, verrà assegnata la data di creazione del movimento</i>',
                                                            text: ''
                                                        },
                                                        {
                                                            xtype: 'tbspacer',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function (button, e) {
                                                                var typeId = Ext.getCmp('CcpTypeEditForm').getValues().id,
                                                                    data = { expiration: null, value: 0 };


                                                                if (typeId) {
                                                                    data.ccp_type = parseInt(typeId);
                                                                    Ext.getStore('CcpTypeSteps').add(data);
                                                                    Ext.getStore('CcpTypeSteps').sync();
                                                                } else {
                                                                    Ext.getStore('CcpTypeSteps').add(data);
                                                                }



                                                            },
                                                            iconCls: 'icon-add',
                                                            text: 'Aggiungi'
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'panel',
                                    layout: 'fit',
                                    title: 'Addizionali/Deduzioni',
                                    items: [
                                        {
                                            xtype: 'gridpanel',
                                            height: 200,
                                            id: 'CcpTypeEditLinkedAdditionalsGrid',
                                            itemId: 'CcpTypeEditLinkedAdditionalsGrid',
                                            title: '',
                                            titleAlign: 'center',
                                            emptyText: 'Nessuna Addizionale abbinata.',
                                            enableColumnHide: false,
                                            enableColumnMove: false,
                                            sortableColumns: false,
                                            store: 'CcpLinkedAdditionalsForm',
                                            columns: [
                                                {
                                                    xtype: 'actioncolumn',
                                                    width: 20,
                                                    resizable: false,
                                                    hideable: false,
                                                    items: [
                                                        {
                                                            getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('positive')) {
                                                                    return 'icon-control_add';
                                                                } else {
                                                                    return 'icon-control_remove';
                                                                }
                                                            },
                                                            getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                                                if (r.get('positive')) {
                                                                    return 'Addizionale positiva';
                                                                } else {
                                                                    return 'Addizionale negativa';
                                                                }
                                                            }
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    resizable: false,
                                                    dataIndex: 'name',
                                                    hideable: false,
                                                    text: 'Nome',
                                                    flex: 1
                                                },
                                                {
                                                    xtype: 'numbercolumn',
                                                    resizable: false,
                                                    align: 'right',
                                                    dataIndex: 'amount',
                                                    text: 'Valore',
                                                    editor: {
                                                        xtype: 'numberfield',
                                                        allowBlank: false,
                                                        allowOnlyWhitespace: false,
                                                        hideTrigger: true,
                                                        allowExponential: false,
                                                        minValue: 0
                                                    }
                                                },
                                                {
                                                    xtype: 'gridcolumn',
                                                    renderer: function (value, metaData, record, rowIndex, colIndex, store, view) {
                                                        if (value) {
                                                            return '%';
                                                        } else {
                                                            return '';
                                                        }
                                                    },
                                                    width: 20,
                                                    resizable: false,
                                                    align: 'center',
                                                    dataIndex: 'percentual',
                                                    hideable: false
                                                }
                                            ],
                                            listeners: {
                                                itemcontextmenu: {
                                                    fn: me.onCcpTypeEditLinkedAdditionalsGridItemContextMenu,
                                                    scope: me
                                                }
                                            },
                                            plugins: [
                                                Ext.create('Ext.grid.plugin.RowEditing', {
                                                    blocked: true,
                                                    listeners: {
                                                        beforeedit: {
                                                            fn: me.onRowEditingBeforeEdit,
                                                            scope: me
                                                        }
                                                    }
                                                })
                                            ],
                                            dockedItems: [
                                                {
                                                    xtype: 'toolbar',
                                                    permissible: true,
                                                    dock: 'top',
                                                    id: 'CcpTypeEditAdditionalsToolbar',
                                                    items: [
                                                        {
                                                            xtype: 'button',
                                                            handler: function (button, e) {
                                                                Ext.widget('CcpLinkedAdditionalsPickerWin').show();

                                                                Ext.getStore('CcpAdditionalsForm').load({
                                                                    params: {
                                                                        type: 'T'
                                                                    }
                                                                });
                                                            },
                                                            iconCls: 'icon-link',
                                                            text: 'Abbina addizionali'
                                                        },
                                                        {
                                                            xtype: 'tbspacer',
                                                            flex: 1
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function (button, e) {
                                                                Ext.widget('CcpAdditionalEditWin').createLink = 'T';
                                                                Ext.getCmp('CcpAdditionalEditWin').show();
                                                            },
                                                            id: 'CcpTypeEditNewAdditionalBtn',
                                                            iconCls: 'icon-add',
                                                            text: 'Crea nuova'
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        var data = Ext.getCmp('CcpTypeEditForm').getForm().getValues(),
                                            adds = Ext.getStore('CcpLinkedAdditionalsForm').getRange(),
                                            sTypes = Ext.getStore('CcpTypes'),
                                            sAdds = Ext.getStore('CcpAdditionals'),
                                            sCats = Ext.getStore('CcpCategories'),
                                            a = 'aggiunto',
                                            newType = data.id === "",
                                            lc = [];

                                        if (data.include_vat > 0 && !data.vat) {
                                            Ext.Msg.alert('ATTENZIONE', 'Iva non valorizzata');
                                            return;
                                        }

                                        data.linked_additionals = [];

                                        adds.forEach(function (a) {
                                            data.linked_additionals.push([a.get('additional_id'), a.get('amount')]);
                                        });

                                        data.incoming = data.incoming === 'on';
                                        data.mensa = data.mensa === 'on';

                                        // Saves the data
                                        if (!newType) {
                                            a = 'aggiornato';
                                            var r = Ext.getCmp('CcpTypesGrid').getSelectionModel().getSelection()[0];
                                            r.set('name', data.name);
                                            r.set('amount', data.amount);
                                            r.set('incoming', data.incoming);
                                            r.set('school_year', data.school_year);
                                            r.set('category_id', data.category_id);
                                            r.set('online_payment', data.online_payment);
                                            r.set('linked_additionals', data.linked_additionals);
                                            r.set('invoice_code', data.invoice_code);
                                            r.set('easy_code', data.easy_code);
                                            r.set('da_ratei', data.da_ratei);
                                            r.set('a_ratei', data.a_ratei);
                                            r.set('include_vat', data.include_vat);
                                            r.set('vat', data.vat);
                                            r.set('vat_code_id', data.vat_code_id);
                                            r.set('bollo', data.bollo);
                                            r.set('payment_mail', data.payment_mail);
                                            r.set('section', data.section);
                                            r.set('online_payment_status', data.online_payment_status);
                                            r.set('exclude_corrispettivi', data.exclude_corrispettivi);
                                            r.set('ccp_credits_type', data.ccp_credits_type);
                                            r.set('ccp_ae_category_id', data.ccp_ae_category_id);
                                            r.set('include_bollo', data.include_bollo);
                                            r.set('easy_code_contropartita', data.easy_code_contropartita);

                                        } else {
                                            sTypes.add(data);
                                        }

                                        // Syncs the record
                                        sTypes.sync({
                                            callback: function () {
                                                sTypes.load();
                                                sAdds.load();
                                                sCats.load();
                                            },
                                            success: function (form, action) {
                                                // Salvataggio type_steps
                                                var typeId = form.proxy.getReader().jsonData.results.id;
                                                var cts = Ext.getStore('CcpTypeSteps');
                                                Ext.each(cts.getRange(), function (v) {
                                                    v.set('ccp_type', typeId);
                                                });
                                                cts.sync();

                                                Ext.getCmp('CcpTypesGrid').getSelectionModel().deselectAll();
                                                Ext.getCmp('CcpTypeEditWin').close();
                                                Ext.Msg.alert('Successo', 'Tipo movimento ' + a);
                                            },
                                            failure: function (form, action) {
                                                Ext.Msg.alert('Attenzione', 'Tipo movimento NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    flex: 1,
                    id: 'CcpTypeEditAdditionalEditMn',
                    itemId: 'CcpTypeEditAdditionalEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function (item, e) {
                                var g = Ext.getCmp('CcpTypeEditLinkedAdditionalsGrid'),
                                    r = g.getSelectionModel().getSelection()[0],
                                    sAdds = Ext.getStore('CcpLinkedAdditionalsForm');

                                g.getPlugin().blocked = false;
                                g.getPlugin().startEdit(r);
                                g.getPlugin().blocked = true;
                            },
                            id: 'contextCcpTypeEditAdditionalEdit',
                            itemId: 'contextCcpTypeEditAdditionalEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica Importo'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function (item, e) {
                                var r = Ext.getCmp('CcpTypeEditLinkedAdditionalsGrid').getSelectionModel().getSelection()[0],
                                    sAdds = Ext.getStore('CcpLinkedAdditionalsForm');

                                sAdds.remove(r);
                            },
                            id: 'contextCcpTypeEditAdditionalDelete',
                            itemId: 'contextCcpTypeEditAdditionalDelete',
                            iconCls: 'icon-delete',
                            text: 'Rimuovi'
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onCcpTypeEditWinBoxReady,
                    scope: me
                },
                beforeclose: {
                    fn: me.onCcpTypeEditWinBeforeClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onRadiogroupChange: function (field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpAeCategoryCmb').setValue();
        Ext.getStore('CcpAeCategories').load({ params: { incoming: newValue.incoming === 'on' ? 1 : 0 } });

    },

    onCheckboxfieldChange: function (field, newValue, oldValue, eOpts) {
        if (newValue === true) {
            Ext.getCmp('CcpTypeIvaNum').enable();
            Ext.getCmp('CcpBolloExempionCnt').disable();
            Ext.getCmp('CcpNoBolloCmb').select();
            Ext.getCmp('CcpBolloCalcCheck').setValue(0);
        } else {
            Ext.getCmp('CcpBolloExempionCnt').enable();
            Ext.getCmp('CcpTypeIvaNum').disable();
            Ext.getCmp('CcpNoBolloCmb').select(Ext.getStore('CcpVatCodes').getRange()[0]);
            Ext.getCmp('CcpBolloCalcCheck').setValue(1);
        }
    },

    onCcpNoBolloCmbAfterRender: function (component, eOpts) {
        Ext.getCmp('CcpNoBolloCmb').select(Ext.getStore('CcpVatCodes').getRange()[0]);
    },

    onCcpTypePaymentMailChange: function (field, newValue, oldValue, eOpts) {
        var mail = Ext.getCmp('CcpTypePaymentMailTxt');
        if (newValue === true) mail.enable();
        else {
            mail.setValue();
            mail.disable();
        }
    },

    onCcpAeCategoryCmbAfterRender: function (component, eOpts) {
        Ext.getStore('CcpAeCategories').load({ params: { incoming: 1 } });

    },

    onRowEditingEdit: function (editor, context, eOpts) {
        var typeId = Ext.getCmp('CcpTypeEditForm').getValues().id;

        /*if(/[0-9]{2}\/[0-9]{2}\/[0-9]{4}/.test(context.newValues.expiration) === false) {
            Ext.Msg.alert('ERRORE', 'La data va scritta nel formato GG/MM/YYYY (Es. 22/05/2022)');
            return false;
        } else {*/
        if (typeId) Ext.getStore('CcpTypeSteps').sync();
        //}
        //
    },

    onCcpTypeEditLinkedAdditionalsGridItemContextMenu: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('CcpTypeEditAdditionalEditMn').showAt([newX, newY]);
    },

    onRowEditingBeforeEdit: function (editor, context, eOpts) {
        var pg = Ext.getCmp('CcpTypeEditLinkedAdditionalsGrid');

        return !pg.getPlugin().blocked;
    },

    onCcpTypeEditWinBoxReady: function (component, width, height, eOpts) {
        // Ext.getStore('CcpTypeSchoolYears').insert(0, {value: 'TUTTI'});
    },

    onCcpTypeEditWinBeforeClose: function (panel, eOpts) {
        // Ext.getStore('CcpTypeSchoolYears').removeAt(0);
    }

});