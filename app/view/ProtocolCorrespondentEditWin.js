/*
 * File: app/view/ProtocolCorrespondentEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolCorrespondentEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolCorrespondentEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Hidden',
        'Ext.form.field.ComboBox',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.FieldSet',
        'Ext.form.field.TextArea'
    ],

    height: 600,
    id: 'ProtocolCorrespondentEditWin',
    itemId: 'ProtocolCorrespondentEditWin',
    width: 700,
    resizable: false,
    title: 'Mittente / Destinatario',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'ProtocolCorrespondentEditForm',
                    itemId: 'ProtocolCorrespondentEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('ProtocolCorrespondents'),
                                            form = Ext.getCmp('ProtocolCorrespondentEditForm').getForm(),
                                            formValues = form.getFieldValues(),
                                            record = {
                                                title: formValues.title,
                                                legal_person: formValues.legal_person === 'on' ? true : false,
                                                fiscal_code: formValues.fiscal_code ? formValues.fiscal_code : null,
                                                address: formValues.address ? formValues.address : null,
                                                city_id: formValues.city_id ? formValues.city_id : null,
                                                zipcode: formValues.zipcode === '00000' ? '' : formValues.zipcode,
                                                phone: formValues.phone ? formValues.phone : null,
                                                fax: formValues.fax ? formValues.fax : null,
                                                mobile: formValues.mobile ? formValues.mobile : null,
                                                email: formValues.email ? formValues.email : null,
                                                web: formValues.web ? formValues.web : null,
                                                note: formValues.note ? formValues.note : null,
                                                correspondent_type: formValues.correspondent_type,
                                                correspondent_type_id: formValues.correspondent_type_id ? formValues.correspondent_type_id : null,
                                                force: 1
                                            },
                                            a = 'salvato';

                                        // Update or Creation
                                        if (formValues.id) {
                                            a = 'aggiornato';
                                            rec = store.getById(parseInt(formValues.id));
                                            if (record.correspondent_type === rec.get('correspondent_type') ) {
                                                if (record.correspondent_type_id === rec.get('correspondent_type_id') ||
                                                (record.correspondent_type_id !== rec.get('correspondent_type_id') &&
                                                record.correspondent_type_id === null)) {
                                                    delete record.correspondent_type;
                                                    delete record.correspondent_type_id;
                                                }
                                            }
                                            rec.set(record);
                                        } else {
                                            store.add(record);
                                        }

                                        store.sync({
                                            callback: function() {
                                                Ext.getCmp('ProtocolCorrespondentsGrid').getSelectionModel().deselectAll();
                                                store.loadPage(1);
                                            },
                                            success: function(form, action) {
                                                Ext.Msg.alert('Successo', 'Mittente / Destinatario ' + a);
                                                // From protocol
                                                if (Ext.getCmp('ProtocolCorrespondentEditWin').createFrom === 'P') {
                                                    var s = Ext.getStore('ProtocolLinkedCorrespondentsForm');
                                                    s.insert(s.count(), form.operations[0].records[0]);
                                                }
                                                Ext.getCmp('ProtocolCorrespondentEditWin').close();
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Mittente / Destinatario NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            fieldLabel: 'Label',
                            name: 'force',
                            value: 1
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ProtocolCorrespondentEditId',
                            itemId: 'ProtocolCorrespondentEditId',
                            name: 'id'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ProtocolCorrespondentEditCorrespondentTypeId',
                            itemId: 'ProtocolCorrespondentEditCorrespondentTypeId',
                            name: 'correspondent_type_id'
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'ProtocolCorrespondentEditCorrespondentType',
                                    itemId: 'ProtocolCorrespondentEditCorrespondentType',
                                    padding: '0 10 0 0',
                                    fieldLabel: 'Categoria',
                                    labelAlign: 'right',
                                    labelWidth: 70,
                                    name: 'correspondent_type',
                                    value: 'M',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    forceSelection: true,
                                    store: 'ProtocolCorrespondentsOrigins',
                                    valueField: 'id',
                                    listeners: {
                                        change: {
                                            fn: me.onProtocolCorrespondentEditCorrespondentTypeChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ProtocolCorrespondentEditOriginPickerWin').show();

                                        var origin = Ext.getCmp('ProtocolCorrespondentEditCorrespondentType').getValue(),
                                            store = Ext.getStore('ProtocolCorrespondentsOriginsForm');

                                        store.getProxy().setExtraParam("type", origin);
                                        store.load();
                                    },
                                    disabled: true,
                                    id: 'ProtocolCorrespondentEditCorrespondentLinkBtn',
                                    itemId: 'ProtocolCorrespondentEditCorrespondentLinkBtn',
                                    iconCls: 'icon-link',
                                    text: 'Abbina a record esistente'
                                }
                            ]
                        },
                        {
                            xtype: 'textfield',
                            id: 'ProtocolCorrespondentEditTitle',
                            itemId: 'ProtocolCorrespondentEditTitle',
                            padding: '5 0 0 0',
                            fieldLabel: 'Nominativo',
                            labelAlign: 'right',
                            labelWidth: 70,
                            name: 'title',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'radiogroup',
                            id: 'ProtocolCorrespondentEditLegalPerson',
                            itemId: 'ProtocolCorrespondentEditLegalPerson',
                            fieldLabel: 'Persona',
                            labelAlign: 'right',
                            labelWidth: 70,
                            layout: {
                                type: 'checkboxgroup',
                                autoFlex: false
                            },
                            items: [
                                {
                                    xtype: 'radiofield',
                                    name: 'legal_person',
                                    boxLabel: 'Fisica',
                                    checked: true,
                                    inputValue: 'off'
                                },
                                {
                                    xtype: 'radiofield',
                                    margin: '0 0 0 10',
                                    name: 'legal_person',
                                    boxLabel: 'Giuridica'
                                }
                            ]
                        },
                        {
                            xtype: 'textfield',
                            id: 'ProtocolCorrespondentEditFiscalCode',
                            itemId: 'ProtocolCorrespondentEditFiscalCode',
                            padding: '5 0 0 0',
                            fieldLabel: 'C.F. / P.IVA',
                            labelAlign: 'right',
                            labelWidth: 70,
                            name: 'fiscal_code'
                        },
                        {
                            xtype: 'container',
                            margin: '0 0 10 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    margin: '0 5 0 0',
                                    title: 'Recapito',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textareafield',
                                            flex: 1,
                                            id: 'ProtocolCorrespondentEditAddress6',
                                            itemId: 'ProtocolCorrespondentEditAddress6',
                                            fieldLabel: 'Indirizzo',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'address'
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'ProtocolCorrespondentEditCityId',
                                            itemId: 'ProtocolCorrespondentEditCityId',
                                            width: 205,
                                            fieldLabel: 'Città',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'city_id',
                                            matchFieldWidth: false,
                                            anyMatch: true,
                                            displayField: 'description',
                                            forceSelection: true,
                                            minChars: 2,
                                            queryMode: 'local',
                                            store: 'CoreCities',
                                            typeAhead: true,
                                            valueField: 'city_id',
                                            listeners: {
                                                focus: {
                                                    fn: me.onProtocolCorrespondentEditCityIdFocus,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolCorrespondentEditZipcode',
                                            itemId: 'ProtocolCorrespondentEditZipcode',
                                            fieldLabel: 'Cap',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'zipcode',
                                            maxLength: 5,
                                            minLength: 5,
                                            vtype: 'zipcode'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    margin: '0 0 0 5',
                                    title: 'Contatti',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolCorrespondentEditPhone',
                                            itemId: 'ProtocolCorrespondentEditPhone',
                                            fieldLabel: 'Telefono',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'phone'
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolCorrespondentEditFax',
                                            itemId: 'ProtocolCorrespondentEditFax',
                                            fieldLabel: 'Fax',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'fax'
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolCorrespondentEditMobile',
                                            itemId: 'ProtocolCorrespondentEditMobile',
                                            fieldLabel: 'Cellulare',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'mobile'
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolCorrespondentEditEmail',
                                            itemId: 'ProtocolCorrespondentEditEmail',
                                            fieldLabel: 'Email',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'email'
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'ProtocolCorrespondentEditWeb',
                                            itemId: 'ProtocolCorrespondentEditWeb',
                                            fieldLabel: 'Sito web',
                                            labelAlign: 'right',
                                            labelWidth: 60,
                                            name: 'web'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            id: 'ProtocolCorrespondentEditNote',
                            itemId: 'ProtocolCorrespondentEditNote',
                            fieldLabel: 'Note',
                            labelAlign: 'right',
                            labelWidth: 70,
                            name: 'note'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onProtocolCorrespondentEditCorrespondentTypeChange: function(field, newValue, oldValue, eOpts) {
        if (newValue !== 'M' && newValue !== 'T' && newValue !== 'S') {
            Ext.getCmp('ProtocolCorrespondentEditCorrespondentLinkBtn').setDisabled(false);
        } else {
            Ext.getCmp('ProtocolCorrespondentEditCorrespondentLinkBtn').setDisabled(true);
        }
        Ext.getCmp('ProtocolCorrespondentEditCorrespondentTypeId').setValue();
    },

    onProtocolCorrespondentEditCityIdFocus: function(component, e, eOpts) {
        Ext.getStore('CoreCities').clearFilter();
    }

});