/*
 * File: app/view/ReminderDetailWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ReminderDetailWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ReminderDetailWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.form.field.Text',
        'Ext.grid.column.Date',
        'Ext.grid.View',
        'Ext.grid.plugin.RowExpander',
        'Ext.XTemplate',
        'Ext.grid.column.Action'
    ],

    height: 597,
    id: 'ReminderDetailWin',
    width: 953,
    layout: 'fit',
    title: 'Dettaglio sollecito',
    maximizable: true,
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'CcpReminderDetailGrd',
                    title: '',
                    store: 'CcpReminderDetails',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'textfield',
                                    width: 200,
                                    fieldLabel: '',
                                    emptyText: 'Cerca ...',
                                    listeners: {
                                        change: {
                                            fn: me.onTextfieldChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            width: 250,
                            dataIndex: 'mail',
                            text: 'Mail'
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 150,
                            dataIndex: 'accountholder_text',
                            text: 'Intestatario'
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 150,
                            dataIndex: 'student_text',
                            text: 'Studente'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'truncate_message',
                            text: 'Messaggio',
                            flex: 1
                        },
                        {
                            xtype: 'datecolumn',
                            dataIndex: 'sent',
                            text: 'Inviato in data',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 35,
                            align: 'center',
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        Ext.Ajax.request({
                                            url: '/mc2-api/ccp/reminder/' + record.get('id'),
                                            method: 'DELETE',
                                            success: function() {
                                                Ext.getCmp('CcpReminderDetailGrd').getSelectionModel().deselectAll();
                                                //Ext.getStore('CcpReminderDetails').remove(record);
                                                //Ext.getStore('CcpReminders').load();



                                                var r = Ext.getCmp('CcpReminderGrd').getSelectionModel().getSelection()[0],
                                                    creation = Ext.Date.format(r.get('creation'), 'c');


                                                Ext.getStore('CcpReminderDetails').load({
                                                    params: {
                                                        creation: creation
                                                    }
                                                });

                                            }
                                        });
                                    },
                                    iconCls: 'icon-delete'
                                }
                            ]
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 70,
                            align: 'center',
                            text: 'Stampa',
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/print',
                                            params: {
                                                newSpool: 1,
                                                print: 'Reminder',
                                                namespace: 'CCP',
                                                type: 'PDF',
                                                mime: 'application/pdf',
                                                id: record.get('id')
                                            },
                                            success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                            }
                                        });
                                    },
                                    iconCls: 'icon-printer'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowExpander', {
                            pluginId: 'ReminderExpander',
                            rowBodyTpl: [
                                '{message}'
                            ]
                        })
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onReminderDetailWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onTextfieldChange: function(field, newValue, oldValue, eOpts) {
        var s = Ext.getStore('CcpReminderDetails'),
            name = newValue.toLowerCase();

        s.removeFilter();

        if(newValue) {
            s.filterBy(function(rec, id) {
                if(
                    rec.get('mail').toLowerCase().indexOf(name) > -1 ||
                    rec.get('student_text').toLowerCase().indexOf(name) > -1 ||
                    rec.get('accountholder_text').toLowerCase().indexOf(name) > -1
                  ) {
                    return true;
                }
                else {
                    return false;
                }
            });

        }

    },

    onReminderDetailWinClose: function(panel, eOpts) {
        Ext.getStore('CcpReminders').load();
    }

});