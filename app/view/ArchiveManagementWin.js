/*
 * File: app/view/ArchiveManagementWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveManagementWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveManagementWin',

    requires: [
        'Ext.form.FieldSet',
        'Ext.form.field.Number',
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Hidden',
        'Ext.form.field.TextArea'
    ],

    border: false,
    height: 466,
    id: 'ArchiveManagementWin',
    itemId: 'ArchiveManagementWin',
    width: 605,
    title: 'Account',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'fieldset',
                    margin: '10 10 10 10',
                    title: 'Pagine',
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'center'
                    },
                    items: [
                        {
                            xtype: 'numberfield',
                            id: 'ArchiveManagementPagesPurchased',
                            itemId: 'ArchiveManagementPagesPurchased',
                            fieldLabel: 'Acquistate',
                            labelAlign: 'right',
                            labelWidth: 80,
                            fieldStyle: 'text-align:right;',
                            readOnly: true
                        },
                        {
                            xtype: 'numberfield',
                            id: 'ArchiveManagementPagesUsed',
                            itemId: 'ArchiveManagementPagesUsed',
                            fieldLabel: 'Utilizzate',
                            labelAlign: 'right',
                            labelWidth: 80,
                            fieldStyle: 'text-align:right;',
                            readOnly: true
                        }
                    ]
                },
                {
                    xtype: 'form',
                    border: false,
                    id: 'Input',
                    itemId: 'Input',
                    bodyCls: 'bck-content',
                    bodyPadding: '20 20 5 20',
                    url: '/mc2-api/archive/account',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            fieldLabel: 'Servizio',
                            labelAlign: 'right',
                            labelWidth: 80,
                            name: 'message_type',
                            editable: false,
                            displayField: 'description',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'ArchiveServices',
                            valueField: 'name',
                            listeners: {
                                change: {
                                    fn: me.onComboboxChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'hiddenfield',
                            name: 'user_id',
                            value: 3
                        }
                    ]
                },
                {
                    xtype: 'panel',
                    flex: 1,
                    border: false,
                    bodyCls: 'bck-content',
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'center',
                        padding: '5 20 10 20'
                    },
                    items: [
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            id: 'Output',
                            itemId: 'Output',
                            readOnly: true,
                            grow: true
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onArchiveManagementWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {
        var f = Ext.getCmp('Input');

        f.getForm().submit({
            success: function(form, action) {
                Ext.getCmp('Output').setValue(JSON.stringify(action.result, null, 4));
            },
            failure: function(form, action) {
                Ext.Msg.alert("action.result.status", "action.result.comment");
            }
        });
    },

    onArchiveManagementWinBoxReady: function(component, width, height, eOpts) {
        var sp = Ext.getStore('CoreParameter');

        sp.load({
            callback : function(records, options, success) {
                    if (success) {
                        Ext.getCmp('ArchiveManagementPagesPurchased').setValue(parseInt(sp.findRecord('name', 'ARCHIVE_PAGES').get('value')));
                    }
            }
        });
    }

});