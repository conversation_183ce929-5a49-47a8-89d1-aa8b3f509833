/*
 * File: app/view/CcpInvoiceTransmissionWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpInvoiceTransmissionWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpInvoiceTransmissionWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.grid.View'
    ],

    height: 250,
    width: 400,
    layout: 'fit',
    title: 'Storico trasmissione',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    title: '',
                    store: 'CcpInvoiceTransmissions',
                    columns: [
                        {
                            xtype: 'datecolumn',
                            dataIndex: 'date',
                            text: 'Data',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            text: 'Descrizione',
                            flex: 1
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});