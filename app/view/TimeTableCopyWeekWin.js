/*
 * File: app/view/TimeTableCopyWeekWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TimeTableCopyWeekWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.TimeTableCopyWeekWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 525,
    id: 'TimeTableCopyWeekWin',
    itemId: 'TimeTableCopyWeekWin',
    minWidth: 200,
    width: 600,
    title: 'Copia settimana',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    layout: {
                        type: 'hbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            id: 'CopyWeekPnl',
                            itemId: 'CopyWeekPnl',
                            title: 'Settimane su cui effettuare la copia',
                            titleAlign: 'center',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            sortableColumns: false,
                            store: 'WeekBorders',
                            selModel: Ext.create('Ext.selection.CheckboxModel', {
                                enableKeyNav: false,
                                checkOnly: true,
                                listeners: {
                                    selectionchange: {
                                        fn: me.onCheckboxselectionmodelSelectionChange1,
                                        scope: me
                                    }
                                }
                            }),
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    hidden: true,
                                    dataIndex: 'month_year',
                                    text: 'Month'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        return Ext.Date.format(new Date(value),'d/m/Y');
                                    },
                                    align: 'center',
                                    dataIndex: 'monday',
                                    text: 'Inizio',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        return Ext.Date.format(new Date(value),'d/m/Y');
                                    },
                                    width: 214,
                                    align: 'center',
                                    dataIndex: 'sunday',
                                    text: 'Fine',
                                    flex: 1
                                }
                            ],
                            features: [
                                {
                                    ftype: 'grouping',
                                    enableGroupingMenu: false,
                                    groupHeaderTpl: [
                                        '{name}'
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            id: 'TimeTableCopyEmp',
                            itemId: 'TimeTableCopyEmp',
                            width: 150,
                            autoScroll: true,
                            title: 'Personale',
                            titleAlign: 'center',
                            emptyText: 'Nessun Personale',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            hideHeaders: true,
                            sortableColumns: false,
                            store: 'EmployeesTreeActive',
                            displayField: 'denomination',
                            useArrows: true,
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'denomination',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onTimeTableCopyEmpCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    padding: '5 0',
                    items: [
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: '0 5',
                            layout: {
                                type: 'vbox',
                                align: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    disabled: true,
                                    id: 'TimeTableCopyBtn',
                                    itemId: 'TimeTableCopyBtn',
                                    iconCls: 'icon-page_copy',
                                    text: 'Copia',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onTimeTableCompyWeekWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCheckboxselectionmodelSelectionChange1: function(model, selected, eOpts) {
        Ext.getCmp('TimeTableCopyWeekWin').enableCopy();

    },

    onTimeTableCopyEmpCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('TimeTableCopyWeekWin').enableCopy();
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('TimeTableCopyWeekWin').setLoading();

        var weeks = Ext.getCmp('CopyWeekPnl').getSelectionModel().getSelection(),
            employees = Ext.getCmp('TimeTableCopyEmp').getChecked(),
            ea = new Array(),
            wa = new Array();

        Ext.each(employees, function(a){
            if (a.data.leaf === true) {
                ea = ea.concat(a.raw.employee_id);
            }
        });
        var eaJSON = Ext.JSON.encode(ea);


        weeks.forEach(function(el){
            wa = wa.concat({
                sunday: el.get('sunday'),
                monday: el.get('monday')
            });
        });

        var record = Ext.getCmp('TimetableCalendarView').getSelectionModel().getSelection()[0];

        Ext.Ajax.request({
            url: '/mc2/applications/employees/timetables/calendar/copy_week.php',
            params: {
                employees: eaJSON,
                weeks: Ext.encode(wa),
                orig_e: record.get('employee_id'),
                orig_d: record.get('date')
            },
            timeout: 300,
            callback: function(){
                Ext.getCmp('TimeTableCopyWeekWin').close();
                Ext.getCmp('TimeTableTabPnl').loadData();
            }
        });
    },

    onTimeTableCompyWeekWinActivate: function(window, eOpts) {
        var t = Ext.getCmp('TimeTableCopyEmp');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });
    },

    enableCopy: function() {
        var weeks = Ext.getCmp('CopyWeekPnl').getSelectionModel().getSelection().length;
        var employees = Ext.getCmp('TimeTableCopyEmp').getChecked().length;

        if( weeks > 0 && employees > 0){
            Ext.getCmp('TimeTableCopyBtn').enable();
        } else {
            Ext.getCmp('TimeTableCopyBtn').disable();
        }
    }

});