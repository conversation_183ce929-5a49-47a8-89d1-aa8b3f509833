/*
 * File: app/view/AlboPublicationExtensionWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboPublicationExtensionWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboPublicationExtensionWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.Date',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 125,
    id: 'AlboPublicationExtensionWin',
    itemId: 'AlboPublicationExtensionWin',
    width: 250,
    title: '<PERSON>roga Pubblicazione',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'AlboPublicationExtensionForm',
                    itemId: 'AlboPublicationExtensionForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'AlboPublicationExtensionId',
                            itemId: 'AlboPublicationExtensionId',
                            name: 'id'
                        },
                        {
                            xtype: 'datefield',
                            endDateField: 'AlboPublicationExtensionExtendedExpirationDate',
                            disabled: true,
                            id: 'AlboPublicationExtensionExpirationDate',
                            itemId: 'AlboPublicationExtensionExpirationDate',
                            fieldLabel: 'Scadenza',
                            labelAlign: 'right',
                            name: 'expiration_date',
                            vtype: 'daterange',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'datefield',
                            startDateField: 'AlboPublicationExtensionExpirationDate',
                            id: 'AlboPublicationExtensionExtendedExpirationDate',
                            itemId: 'AlboPublicationExtensionExtendedExpirationDate',
                            fieldLabel: 'Proroga',
                            labelAlign: 'right',
                            name: 'extended_expiration_date',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            vtype: 'daterange',
                            editable: false,
                            format: 'd/m/Y',
                            startDay: 1,
                            submitFormat: 'c'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var data = Ext.getCmp('AlboPublicationExtensionForm').getForm().getValues(),
                                            store = Ext.getStore('AlboPublications'),
                                            record = {
                                                extension_date: new Date(),
                                                extended_expiration_date: data.extended_expiration_date
                                            };

                                        rec = store.getById(parseInt(data.id));
                                        rec.set(record);

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('AlboGrid').getSelectionModel().deselectAll();
                                                Ext.getCmp('AlboPublicationExtensionWin').close();
                                                Ext.Msg.alert('Successo', 'Pubblicazione prorogata');
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Pubblicazione NON prorogata');
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});