/*
 * File: app/view/ProtocolProtocolNewLinkedCorrespondentsPickerWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolProtocolNewLinkedCorrespondentsPickerWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolProtocolNewLinkedCorrespondentsPickerWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.selection.CheckboxModel',
        'Ext.form.field.Text',
        'Ext.toolbar.Paging'
    ],

    height: 400,
    id: 'ProtocolProtocolNewLinkedCorrespondentsPickerWin',
    itemId: 'ProtocolProtocolNewLinkedCorrespondentsPickerWin',
    width: 450,
    resizable: false,
    layout: 'fit',
    title: 'Abbina Mittenti / Destinatari',
    defaultFocus: 'ProtocolProtocolNewLinkedCorrespondentsPickerFilterName',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    border: false,
                    id: 'ProtocolProtocolNewLinkedCorrespondentsPickerGrid',
                    itemId: 'ProtocolProtocolNewLinkedCorrespondentsPickerGrid',
                    emptyText: 'Nessun Corrispondente trovato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ProtocolCorrespondentsForm',
                    viewConfig: {
                        listeners: {
                            groupclick: function (view, groupField, groupValue, eOpts) {
                                // Previene il toggle dei gruppi (rimangono sempre espansi)
                                return false;
                            }
                        }
                    },
                    cls: 'protocol-correspondents-grid',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'title',
                            hideable: false,
                            text: 'Denominazione',
                            flex: 1
                        }
                    ],
                    features: [
                        {
                            ftype: 'grouping',
                            enableGroupingMenu: false,
                            enableNoGroups: false,
                            groupHeaderTpl: [
                                '{name} ({rows.length})'
                            ]
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true,
                        showHeaderCheckbox: false,
                        listeners: {
                            select: {
                                fn: me.onCheckboxModelSelect,
                                scope: me
                            },
                            deselect: {
                                fn: me.onCheckboxModelDeselect,
                                scope: me
                            }
                        }
                    }),
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'ProtocolProtocolNewLinkedCorrespondentsPickerFilterName',
                                    itemId: 'ProtocolProtocolNewLinkedCorrespondentsPickerFilterName',
                                    checkChangeBuffer: 500,
                                    emptyText: 'Ricerca...',
                                    listeners: {
                                        change: {
                                            fn: me.onProtocolProtocolNewLinkedCorrespondentsPickerFilterNameChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'pagingtoolbar',
                            dock: 'bottom',
                            displayInfo: true,
                            store: 'ProtocolCorrespondentsForm'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onProtocolProtocolNewLinkedCorrespondentsPickerWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCheckboxModelSelect: function(rowmodel, record, index, eOpts) {
        Ext.getStore('ProtocolLinkedCorrespondentsForm').add(record);
    },

    onCheckboxModelDeselect: function(rowmodel, record, index, eOpts) {
        Ext.getStore('ProtocolLinkedCorrespondentsForm').remove(record);
    },

    onProtocolProtocolNewLinkedCorrespondentsPickerFilterNameChange: function(field, newValue, oldValue, eOpts) {
        var store = Ext.getStore('ProtocolCorrespondentsForm');

        store.clearFilter(true);

        if (newValue) {
            store.filter('title', newValue);
        } else {
            store.clearFilter();
        }
    },

    onProtocolProtocolNewLinkedCorrespondentsPickerWinShow: function(panel, eOpts) {
        Ext.getStore('ProtocolCorrespondentsForm').clearFilter();
    }

});