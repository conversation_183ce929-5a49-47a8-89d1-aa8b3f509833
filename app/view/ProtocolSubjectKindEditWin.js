/*
 * File: app/view/ProtocolSubjectKindEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolSubjectKindEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolSubjectKindEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Text',
        'Ext.form.field.Hidden'
    ],

    height: 120,
    id: 'ProtocolSubjectKindEditWin',
    itemId: 'ProtocolSubjectKindEditWin',
    width: 400,
    resizable: false,
    title: '<PERSON><PERSON><PERSON> di Oggetto',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'ProtocolSubjectKindEditForm',
                    itemId: 'ProtocolSendMethodEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'center'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('ProtocolSubjectKinds'),
                                            form = Ext.getCmp('ProtocolSubjectKindEditForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvato';

                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornato';
                                            record = store.getById(parseInt(values.id));
                                            record.set('title', values.title);
                                        } else {
                                            store.add({title: values.title});
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('ProtocolSubjectKindEditWin').close();
                                                Ext.Msg.alert('Successo', 'Tipo di Oggetto ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Tipo di Oggetto NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            name: 'title',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            name: 'id'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});