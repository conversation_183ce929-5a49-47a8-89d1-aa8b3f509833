/*
 * File: app/view/CcpStudentsMissing.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpStudentsMissing', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpStudentsMissing',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.button.Button'
    ],

    height: 141,
    id: 'CcpStudentsMissing',
    width: 292,
    title: 'Periodo di scadenza',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    id: 'CcpStudentsMissingFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'container',
                            layout: {
                                type: 'vbox',
                                align: 'stretch',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'datefield',
                                    id: 'CCCCC',
                                    fieldLabel: 'Da',
                                    labelAlign: 'right',
                                    labelWidth: 30,
                                    name: 'expiration_date_start',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d',
                                    listeners: {
                                        afterrender: {
                                            fn: me.onCCCCCAfterRender,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'datefield',
                                    fieldLabel: 'a',
                                    labelAlign: 'right',
                                    labelWidth: 30,
                                    name: 'expiration_date_end',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d',
                                    listeners: {
                                        afterrender: {
                                            fn: me.onDatefieldShow1,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: 5,
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var rec = {};

                                        rec.newSpool = 1;
                                        rec.print = 'StudentsList';
                                        rec.namespace = 'CCP';
                                        rec.type = 'PDF';
                                        rec.mime = 'application/pdf';
                                        rec.filter = Ext.encode(Ext.getCmp('CcpStudentsMissingFrm').getForm().getValues());

                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/print',
                                            params: rec,
                                            success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                                Ext.getCmp('CcpStudentsMissing').close();
                                            }
                                        });
                                    },
                                    text: 'Stampa'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onCCCCCAfterRender: function(component, eOpts) {
        var date = new Date(),
            year = date.getFullYear();

        if(date.getMonth()<9) year--;
        component.setValue(year + '-09-01');
    },

    onDatefieldShow1: function(component, eOpts) {
        var date = new Date(),
            year = date.getFullYear();

        if(date.getMonth()>8) year++;
        component.setValue(year + '-08-31');
    }

});