/*
 * File: app/view/CcpLinkedPaymentsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpLinkedPaymentsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpLinkedPaymentsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.grid.column.Number',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Separator'
    ],

    readOnly: false,
    linked: null,
    height: 234,
    id: 'CcpLinkedPaymentsWin',
    minHeight: 200,
    width: 820,
    resizable: false,
    title: 'Pagamenti',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'CcpLinkedPaymentsGrid',
                    emptyText: 'Nessun pagamento presente.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'CcpLinkedPayments',
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('receipt_id')) {
                                            return 'icon-page';
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('receipt_id')) {
                                            return 'Ricevuta n.' + r.get('receipt_number') + ' emessa il ' + Ext.Date.format(r.get('receipt_date'), 'd/m/Y H:i');
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'datecolumn',
                            align: 'center',
                            dataIndex: 'operation_date',
                            text: 'Data Operazione',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'datecolumn',
                            align: 'center',
                            dataIndex: 'accountable_date',
                            text: 'Data pagamento',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 229,
                            dataIndex: 'payment_method_text',
                            text: 'Metodo'
                        },
                        {
                            xtype: 'numbercolumn',
                            width: 110,
                            align: 'right',
                            dataIndex: 'amount',
                            text: 'Importo'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'payer_data',
                            text: 'Pagante/Ricevente',
                            flex: 1
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 40,
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        if (record.get('count_additionals') > 0) {
                                            var s = Ext.getStore('CcpLinkedAdditionals');
                                            s.removeAll();
                                            s.load({
                                                params: {
                                                    type: 'P',
                                                    item: record.get('id')
                                                },
                                                callback: function(records, operation, success) {
                                                    if (success) {
                                                        Ext.widget('CcpLinkedAdditionalsWin').show();
                                                        Ext.getCmp('CcpLinkedAdditionalsWin').setTitle('Addizionali abbinate al pagamento');
                                                    } else {
                                                        Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al pagamento fallito.');
                                                    }
                                                }
                                            });
                                        }
                                    },
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('count_additionals') > 0) {
                                            return 'icon-money_add';
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        var additionals = r.get('count_additionals'),
                                            a = 'e';

                                        if (additionals > 0) {
                                            if (additionals > 1) {
                                                a = 'i';
                                            }

                                            return 'Comprende ' + additionals + ' addizional' + a;
                                        }
                                    }
                                },
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        var msg = '<div style="display: table">' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>Conto Corrente:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + record.get('account_text') + '</div>' +
                                        '</div>' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>Bollettino:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + record.get('bollettino') + '</div>' +
                                        '</div>' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>Riferimento C/C:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + record.get('account_reference') + '</div>' +
                                        '</div>' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>Nominativo:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + '(' + record.get('payer_type') + ') ' + record.get('payer_name') + ' ' + record.get('payer_surname') + '</div>' +
                                        '</div>' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>Codice Fiscale:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + record.get('payer_fiscal_code') + '</div>' +
                                        '</div>' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>Indirizzo:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + record.get('payer_address') + '</div>' +
                                        '</div>' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>CAP:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + record.get('payer_zip_code') + '</div>' +
                                        '</div>' +
                                        '<div style="display: table-row">' +
                                        '<div style="display: table-cell"><b>Città:&nbsp;</b></div>' +
                                        '<div style="display: table-cell">' + record.get('payer_city') + ' (' + record.get('payer_province') + ')</div>' +
                                        '</div>' +
                                        '</div>';
                                        Ext.Msg.alert('Dettagli pagamento', msg);
                                    },
                                    iconCls: 'icon-information',
                                    tooltip: 'Dettagli'
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            permissible: true,
                            dock: 'top',
                            id: 'CcpLinkedPaymentsToolbar',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        /*var r = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection()[0],
                                        p = Ext.getStore('CcpPayers'),
                                        a = 'Pagamento';

                                        Ext.widget('CcpPaymentEditWin').show();
                                        Ext.getCmp('CcpStudentPaymentSave').enable();
                                        Ext.getCmp('CcpStudentPaymentMassive').hide();

                                        if (!r.get('incoming')) {
                                        a = 'Prelievo';
                                        }
                                        Ext.getCmp('CcpPaymentEditWin').setTitle(a + ' abbinato al Movimento ' + r.get('number'));


                                        Ext.getCmp('CcpPaymentMovementId').setValue(r.get('id'));
                                        Ext.getCmp('CcpPaymentOperationDate').setValue(new Date());
                                        Ext.getCmp('CcpPaymentAccountableDate').setValue(new Date());

                                        p.removeAll();
                                        if (r.get('subject_type') === 'O') {
                                        Ext.getCmp('CcpPaymentPayer').setDisabled(true);
                                        } else {
                                        Ext.getCmp('CcpPaymentPayer').setDisabled(false);
                                        p.load({
                                        params: {
                                        type: r.get('subject_type'),
                                        id: r.get('subject_id')
                                        }
                                        });
                                        }
                                        */
                                        if(Ext.getCmp('CcpMainPnl').getActiveTab().id == 'CcpMovementPnl') {
                                            Ext.getCmp('CcpMovementPnl').openPaymentWin();
                                        } else Ext.getCmp('CcpMovementPnl').openPaymentWin('movement_student');
                                    },
                                    id: 'CcpLinkedPaymentNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onCcpLinkedPaymentsGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    id: 'CcpLinkedPaymentsMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                /*var r = Ext.getCmp('CcpLinkedPaymentsGrid').getSelectionModel().getSelection()[0],
                                m = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection()[0],
                                s = Ext.getStore('CcpLinkedAdditionalsForm'),
                                p = Ext.getStore('CcpPayers'),
                                a = 'Pagamento';

                                s.removeAll();
                                s.load({
                                params: {
                                type: 'P',
                                item: r.get('id')
                                },
                                callback: function(records, operation, success) {
                                if (success) {
                                Ext.widget('CcpPaymentEditWin').show();

                                if (!r.get('incoming')) {
                                a = 'Prelievo';
                                }
                                Ext.getCmp('CcpPaymentEditWin').setTitle(a + ' abbinato al Movimento ' + r.get('movement_number'));

                                Ext.getCmp('CcpPaymentForm').getForm().loadRecord(r);

                                p.removeAll();
                                if (r.get('subject_type') === 'O') {
                                Ext.getCmp('CcpPaymentPayer').setDisabled(true);
                                } else {
                                Ext.getCmp('CcpPaymentPayer').setDisabled(false);
                                p.load({
                                params: {
                                type: r.get('subject_type'),
                                id: r.get('subject_id')
                                },
                                callback: function(records, operation, success) {
                                Ext.getCmp('CcpPaymentPayer').select(r.get('subject_type') + '_' + r.get('subject_id'));
                                }
                                });
                                }

                                Ext.getCmp('CcpPaymentAmount').setMaxValue(Math.round((m.get('remaining') + r.get('amount'))*100, 2)/100);
                                } else {
                                Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al pagamento fallito.');
                                }
                                }
                                });*/
                                Ext.getCmp('CcpMovementPnl').openPaymentWin('payment');
                            },
                            id: 'contextCcpLinkedPaymentEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('CcpLinkedPaymentsGrid').getSelectionModel().getSelection()[0],
                                    link = Ext.getCmp('CcpLinkedPaymentsWin').linked;

                                Ext.Msg.show({
                                    title: 'Attenzione',
                                    msg: 'Sei sicuro di voler eliminare questo Pagamento?<br />Sarà necessario emettere nuovamente l\'eventuale fattura associata, se legata ad altri pagamenti.',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('CcpLinkedPayments');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load({
                                                        params: {
                                                            linked: link,
                                                            item: link === 'M' ? record.get('movement_id') : record.get('receipt_id')
                                                        }
                                                    });
                                                    Ext.getStore('CcpMovements').load();
                                                    Ext.getStore('CcpPayments').load();
                                                    Ext.getStore('CcpReceipts').load();
                                                },
                                                success: function() {
                                                    Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();
                                                    Ext.Msg.alert('Successo', 'Pagamento eliminato');
                                                },
                                                failure: function(r) {
                                                    var res = r.proxy.getReader().jsonData,
                                                        msg = 'Pagamento NON eliminato';
                                                    if(res && res.message) {
                                                        msg = res.message;
                                                    }
                                                    Ext.Msg.alert('Attenzione', msg);
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextCcpLinkedPaymentDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        },
                        {
                            xtype: 'menuseparator'
                        },
                        {
                            xtype: 'menuitem',
                            id: 'contextCcpLinkedPaymentPrints',
                            iconCls: 'icon-printer',
                            text: 'Stampe singole',
                            menu: {
                                xtype: 'menu',
                                id: 'CcpLinkedPaymentEditPrintsMn',
                                items: [
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            var r = Ext.getCmp('CcpLinkedPaymentsGrid').getSelectionModel().getSelection()[0],
                                                id = r.get('id');

                                            if (r.get('receipt_id')) {
                                                Ext.Ajax.request({
                                                    url: '/mc2-api/core/print',
                                                    params: {
                                                        newSpool: 1,
                                                        print: r.get('receipt') === true ? mc2ui.app.settings.prints.receipt : 'Attestation',
                                                        namespace: 'CCP',
                                                        type: 'PDF',
                                                        mime: 'application/pdf',
                                                        receipt_id: r.get('receipt_id'),
                                                        number: r.get('receipt_number'),
                                                        date: r.get('receipt_date')
                                                    },
                                                    success: function(response, opts) {
                                                        var res = Ext.decode(response.responseText);
                                                        mc2ui.app.showNotifyPrint(res);
                                                    }
                                                });
                                            } else {
                                                Ext.widget('CcpReceiptEditWin').show();
                                                Ext.getCmp('CcpReceiptEditLinkedPayments').setValue(Ext.encode([id]));
                                            }
                                        },
                                        id: 'contextCcpLinkedPaymentReceipt',
                                        iconCls: 'icon-page',
                                        text: 'Ricevuta'
                                    },
                                    {
                                        xtype: 'menuitem',
                                        handler: function(item, e) {
                                            var r = Ext.getCmp('CcpLinkedPaymentsGrid').getSelectionModel().getSelection()[0];

                                            Ext.Ajax.request({
                                                url: '/mc2-api/core/print',
                                                params: {
                                                    newSpool: 1,
                                                    print: 'Certificate',
                                                    namespace: 'CCP',
                                                    type: 'PDF',
                                                    mime: 'application/pdf',
                                                    payment_id: r.get('id'),
                                                    operation_date: r.get('operation_date')
                                                },
                                                success: function(response, opts) {
                                                    var res = Ext.decode(response.responseText);
                                                    mc2ui.app.showNotifyPrint(res);
                                                }
                                            });
                                        },
                                        id: 'contextCcpLinkedPaymentCertificate',
                                        iconCls: 'icon-script',
                                        text: 'Attestato Contributo volontario'
                                    }
                                ]
                            }
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onCcpPaymentsWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onCcpLinkedPaymentsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        if (!Ext.getCmp('CcpLinkedPaymentsWin').readOnly) {
            var newX = e.xy[0],
                newY = e.xy[1];
            Ext.getCmp('CcpLinkedPaymentsMn').showAt([newX,newY]);

            if (record.get('receipt_id')) {
                Ext.getCmp('contextCcpLinkedPaymentEdit').setDisabled(true);
                //Ext.getCmp('contextCcpLinkedPaymentDelete').setDisabled(true);
            } else {
                Ext.getCmp('contextCcpLinkedPaymentEdit').setDisabled(false);
                //Ext.getCmp('contextCcpLinkedPaymentDelete').setDisabled(false);
            }
        }
    },

    onCcpPaymentsWinClose: function(panel, eOpts) {
        Ext.getStore('CcpMovements').load();
        Ext.getStore('CcpPayments').load();
        Ext.getStore('CcpReceipts').load();
    }

});