/*
 * File: app/view/EmployeeLastLockedMonthPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeLastLockedMonthPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeLastLockedMonthPrintWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 468,
    id: 'EmployeeLastLockedMonthPrintWin',
    itemId: 'EmployeeLastLockedMonthPrintWin',
    minHeight: 400,
    width: 354,
    title: 'Stampa ultima chiusura mese',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            listeners: {
                activate: {
                    fn: me.onEmployeeLastLockedMonthPrintWinActivate,
                    scope: me
                }
            },
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeLastLockedMonthPrintContainer',
                    itemId: 'EmployeeLastLockedMonthPrintContainer',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'toolbar',
                            padding: '5 0',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    disabled: true,
                                    id: 'EmployeeLastLockedMonthPrintBtnPrint',
                                    itemId: 'EmployeeLastLockedMonthPrintBtnPrint',
                                    iconCls: 'icon-printer',
                                    text: 'Stampa',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            border: false,
                            height: 250,
                            id: 'EmployeeLastLockedMonthPrintGrid',
                            itemId: 'EmployeeLastLockedMonthPrintGrid',
                            width: 400,
                            autoScroll: true,
                            title: 'Personale',
                            titleAlign: 'center',
                            emptyText: 'Nessun Personale',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            hideHeaders: true,
                            sortableColumns: false,
                            store: 'EmployeesTreeActive',
                            displayField: 'denomination',
                            useArrows: true,
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'denomination',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onEmployeeLastLockedMonthPrintGridCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onEmployeeLastLockedMonthPrintWinActivate: function(window, eOpts) {
        var t = Ext.getCmp('EmployeeLastLockedMonthPrintGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        Ext.getCmp('EmployeeLastLockedMonthPrintWin').enablePrint();
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('EmployeeLastLockedMonthPrintContainer').setLoading();

        // Take the merge id to print and put it in a JSON encoded array
        var sel = Ext.getCmp('EmployeeLastLockedMonthPrintGrid').getChecked(),
            mergeSelect = new Array();

        Ext.each(sel, function(a) {
            if (a.data.leaf === true) {
                mergeSelect = mergeSelect.concat(a.raw.employee_id);
            }
        });
        var mergeSelectJSON = Ext.JSON.encode(mergeSelect);

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 0,
                print: 'LastLockedMonth',
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFLastLockedMonth',
                mime: 'application/pdf',
                employees: mergeSelectJSON
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeeLastLockedMonthPrintContainer').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });
    },

    onEmployeeLastLockedMonthPrintGridCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeLastLockedMonthPrintWin').enablePrint();
    },

    enablePrint: function() {
        var employees = Ext.getCmp('EmployeeLastLockedMonthPrintGrid').getChecked();

        if (employees.length > 0) {
            Ext.getCmp('EmployeeLastLockedMonthPrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeLastLockedMonthPrintBtnPrint').setDisabled(true);
        }
    }

});