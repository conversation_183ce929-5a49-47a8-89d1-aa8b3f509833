/*
 * File: app/view/CcpPvrUploadWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpPvrUploadWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpPvrUploadWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.File',
        'Ext.button.Button',
        'Ext.form.FieldSet'
    ],

    height: 328,
    id: 'CcpPvrUploadWin',
    itemId: 'CcpPvrUploadWin',
    width: 465,
    layout: 'fit',
    title: 'Importazione crediti',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpUploadPvrFrm',
                    bodyPadding: 10,
                    title: '',
                    method: 'POST',
                    url: '/mc2-api/ccp/import_pvr',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'filefield',
                            id: 'CcpPvrUpload',
                            fieldLabel: '',
                            name: 'pvrUpload'
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('CcpUploadPvrFrm').submit({
                                            success: function(form, action) {
                                                //if (action.result.success === true) {
                                                Ext.each(action.result.data, function(val) {
                                                    lb = Ext.create('Ext.form.Label', {
                                                        text: val,
                                                        margin: '2 0'
                                                    });
                                                    Ext.getCmp('PvrImportErrors').add(lb);
                                                });
                                                //} else {
                                                Ext.each(action.result.errors, function(val) {
                                                    lb = Ext.create('Ext.form.Label', {
                                                        text: val,
                                                        margin: '2 0'
                                                    });
                                                    Ext.getCmp('PvrImportErrors').add(lb);
                                                });
                                                //}
                                            },
                                            failure: function(form, action) {

                                            }
                                        });





                                    },
                                    text: 'Importa'
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            id: 'PvrImportErrors',
                            itemId: 'PvrImportErrors',
                            margin: '5 0 ',
                            autoScroll: true,
                            title: 'Esito importazione',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});