/*
 * File: app/view/EmployeePresencesPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeePresencesPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeePresencesPrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.toolbar.Spacer',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel'
    ],

    height: 468,
    id: 'EmployeePresencesPrintWin',
    itemId: 'EmployeePresencesPrintWin',
    minHeight: 400,
    minWidth: 300,
    width: 600,
    title: 'Stampa riepilogo Mensile',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeePresencesPrintWinContainer',
                    itemId: 'EmployeePresencesPrintWinContainer',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            border: false,
                            id: 'EmployeePrintDataFrm',
                            itemId: 'EmployeePrintDataFrm',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-box-layout-ct'
                            ],
                            bodyPadding: 10,
                            header: false,
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: '5 0',
                                    layout: {
                                        type: 'hbox',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            disabled: true,
                                            id: 'EmployeePresencesPrintBtnPrint',
                                            itemId: 'EmployeePresencesPrintBtnPrint',
                                            iconCls: 'icon-printer',
                                            text: 'Stampa',
                                            listeners: {
                                                click: {
                                                    fn: me.onButtonClick,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'button',
                                            hidden: true,
                                            text: 'Stampa vecchia',
                                            listeners: {
                                                click: {
                                                    fn: me.onButtonClick1,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'PrintFilterMonth',
                                            itemId: 'PrintFilterMonth',
                                            padding: '0 5 0 0',
                                            width: 150,
                                            fieldLabel: 'Periodo:',
                                            labelWidth: 50,
                                            inputId: 'month',
                                            editable: false,
                                            queryMode: 'local',
                                            store: 'Months',
                                            valueField: 'number'
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'PrintFilterYear',
                                            itemId: 'PrintFilterYear',
                                            padding: '0 0 0 5',
                                            width: 100,
                                            inputId: 'year',
                                            editable: false,
                                            displayField: 'year',
                                            queryMode: 'local',
                                            store: 'Years',
                                            valueField: 'year'
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            id: 'EmployeeFormatPrintCmb',
                                            itemId: 'EmployeeFormatPrintCmb',
                                            fieldLabel: 'Formato',
                                            labelWidth: 50,
                                            inputId: 'print_format',
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'PrintFormats'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'treepanel',
                                    flex: 1,
                                    height: 250,
                                    id: 'EmployeePresencesPrintEmployeesGrid',
                                    itemId: 'EmployeePresencesPrintEmployeesGrid',
                                    width: 400,
                                    autoScroll: true,
                                    title: 'Personale',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun Personale',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    hideHeaders: true,
                                    sortableColumns: false,
                                    store: 'EmployeesTreeActive',
                                    displayField: 'denomination',
                                    useArrows: true,
                                    viewConfig: {

                                    },
                                    columns: [
                                        {
                                            xtype: 'treecolumn',
                                            resizable: false,
                                            dataIndex: 'denomination',
                                            text: '',
                                            flex: 1
                                        }
                                    ],
                                    listeners: {
                                        checkchange: {
                                            fn: me.onEmployeePresencesPrintEmployeesGridCheckChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'EmployeeAbsStackPrintMonthGrid',
                                    itemId: 'EmployeeAbsStackPrintMonthGrid',
                                    title: 'Monteore',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun monteore impostato',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    sortableColumns: false,
                                    store: 'AbsenceStacks',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'denomination',
                                            text: '',
                                            flex: 1
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                                        checkOnly: true,
                                        listeners: {
                                            selectionchange: {
                                                fn: me.onCheckboxModelSelectionChange,
                                                scope: me
                                            }
                                        }
                                    })
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeePresencesPrintWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        // Take period (month, year)
        var period = Ext.getCmp('EmployeePrintDataFrm').getForm().getValues();


        // take all people to print and put it in JSON encode array of employee_id
        var sel = Ext.getCmp('EmployeePresencesPrintEmployeesGrid').getChecked(),
            idSelect = new Array();
        Ext.each(sel, function(a){
            if (a.data.leaf === true) {
                idSelect = idSelect.concat(a.raw.employee_id);
            }
        });
        var idSelectJSON = Ext.JSON.encode(idSelect);

        // Take the hour stacks id to print and put it in a JSON encoded array
        if(mc2ui.app.settings.stacks_print_if_first_view === true){
            var sel = Ext.getStore('AbsenceStacks').getRange();
        } else {
            var sel = Ext.getCmp('EmployeeAbsStackPrintMonthGrid').getSelectionModel().getSelection();
        }

        var idStackSelect = new Array();
        Ext.each(sel, function(a){
            idStackSelect= idStackSelect.concat(parseInt(a.get('id')));
        });
        var idStackSelectJSON = Ext.JSON.encode(idStackSelect);


        Ext.getCmp('EmployeePresencesPrintWinContainer').setLoading();

        /*
        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 1,
                print: 'MonthExtraordinaries',
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFMonthExtraordinaries',
                mime: 'application/pdf',
                month: period.month,
                year: period.year,
                employees: idSelectJSON,
                stacks: idStackSelectJSON
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeePresencesPrintWinContainer').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });
        */

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 0,
                print: 'MonthExtraordinaries',
                print_format: Ext.getCmp('EmployeeFormatPrintCmb').getValue(),
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFMonthExtraordinaries',
                mime: 'application/pdf',
                month: period.month,
                year: period.year,
                employees: idSelectJSON,
                stacks: idStackSelectJSON
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeePresencesPrintWinContainer').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });

    },

    onButtonClick1: function(button, e, eOpts) {
        // Take period (month, year)
        var period = Ext.getCmp('EmployeePrintDataFrm').getForm().getValues();


        // take all people to print and put it in JSON encode array of employee_id
        var sel = Ext.getCmp('EmployeePresencesPrintEmployeesGrid').getChecked(),
            idSelect = new Array();
        Ext.each(sel, function(a){
            if (a.data.leaf === true) {
                idSelect = idSelect.concat(a.raw.employee_id);
            }
        });
        var idSelectJSON = Ext.JSON.encode(idSelect);

        // Take the hour stacks id to print and put it in a JSON encoded array
        if(mc2ui.app.settings.stacks_print_if_first_view === true){
            var sel = Ext.getStore('AbsenceStacks').getRange();
        } else {
            var sel = Ext.getCmp('EmployeeAbsStackPrintMonthGrid').getSelectionModel().getSelection();
        }

        var idStackSelect = new Array();
        Ext.each(sel, function(a){
            idStackSelect= idStackSelect.concat(parseInt(a.get('id')));
        });
        var idStackSelectJSON = Ext.JSON.encode(idStackSelect);


        Ext.getCmp('EmployeePresencesPrintWinContainer').setLoading();

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 0,
                print: 'MonthExtraordinaries',
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFMonthExtraordinaries',
                mime: 'application/pdf',
                month: period.month,
                year: period.year,
                employees: idSelectJSON,
                stacks: idStackSelectJSON
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeePresencesPrintWinContainer').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });

        /*
        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 1,
                print: 'MonthExtraordinaries',
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFMonthExtraordinaries',
                mime: 'application/pdf',
                month: period.month,
                year: period.year,
                employees: idSelectJSON,
                stacks: idStackSelectJSON
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeePresencesPrintWinContainer').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });*/
    },

    onEmployeePresencesPrintEmployeesGridCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeePresencesPrintWin').enablePrint();
    },

    onCheckboxModelSelectionChange: function(model, selected, eOpts) {
        Ext.getCmp('EmployeePresencesPrintWin').enablePrint();
    },

    onEmployeePresencesPrintWinActivate: function(window, eOpts) {
        var year = parseInt(Ext.Date.format(new Date(),'Y'));
        Ext.getCmp('PrintFilterYear').setValue(year);

        var month = parseInt(Ext.Date.format(new Date(),'m'));
        Ext.getCmp('PrintFilterMonth').setValue(month);

        Ext.getCmp('EmployeePresencesPrintWin').selectAllStacks();

        var t = Ext.getCmp('EmployeePresencesPrintEmployeesGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        Ext.getCmp('EmployeePresencesPrintWin').enablePrint();
        Ext.getCmp('PrintFilterYear').getStore().load();

        Ext.getCmp('EmployeeFormatPrintCmb').setValue('A4');
    },

    enablePrint: function() {
        var employees = Ext.getCmp('EmployeePresencesPrintEmployeesGrid').getChecked(),
            stacks = Ext.getCmp('EmployeeAbsStackPrintMonthGrid').getSelectionModel().getSelection();

        if (employees.length > 0 && stacks.length > 0) {
            Ext.getCmp('EmployeePresencesPrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('EmployeePresencesPrintBtnPrint').setDisabled(true);
        }
    },

    selectAllStacks: function() {
        var grid = Ext.getCmp('EmployeeAbsStackPrintMonthGrid').getSelectionModel();
        grid.selectAll();
    }

});