/*
 * File: app/view/ProtocolTypeEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolTypeEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolTypeEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Text',
        'Ext.form.Label',
        'Ext.form.field.Hidden'
    ],

    height: 120,
    id: 'ProtocolTypeEditWin',
    itemId: 'ProtocolTypeEditWin',
    width: 400,
    resizable: false,
    title: 'Voce <PERSON>',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'ProtocolTypeEditForm',
                    itemId: 'ProtocolTypeEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'hbox',
                        align: 'middle'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('ProtocolTypesTree'),
                                            formValues = Ext.getCmp('ProtocolTypeEditForm').getForm().getFieldValues(),
                                            record = {
                                                code: formValues.code,
                                                description: formValues.description,
                                                parent_type_id: formValues.parent_type_id
                                            },
                                            a = 'salvata';

                                        // Update, Creation (SubVoice) or Creation (Voice)
                                        if (formValues.id) {
                                            a = 'aggiornata';
                                            node = store.getNodeById(formValues.id);
                                            node.set(record);
                                        } else {
                                            if (formValues.parent_type_id) {
                                                node = store.getNodeById(formValues.parent_type_id);
                                            } else {
                                                node = store.getRootNode();
                                            }
                                            node.appendChild(record);
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('ProtocolTypeEditWin').close();
                                                Ext.Msg.alert('Successo', 'Voce Titolario ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Voce Titolario NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'textfield',
                            validator: function(value) {
                                if (this.forbiddenCodes.indexOf(value) < 0) {
                                    return true;
                                }
                                return 'Codice già utilizzato!';
                            },
                            forbiddenCodes: [
                                
                            ],
                            id: 'ProtocolTypeEditCode',
                            itemId: 'ProtocolTypeEditCode',
                            width: 50,
                            name: 'code',
                            fieldStyle: 'text-align:center;',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            emptyText: 'Codice'
                        },
                        {
                            xtype: 'label',
                            margin: '0 10 0 10',
                            text: '-'
                        },
                        {
                            xtype: 'textfield',
                            flex: 1,
                            id: 'ProtocolTypeEditDescription',
                            itemId: 'ProtocolTypeEditDescription',
                            name: 'description',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            emptyText: 'Denominazione'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ProtocolTypeEditId',
                            itemId: 'ProtocolTypeEditId',
                            fieldLabel: 'Label',
                            name: 'id'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ProtocolTypeEditParentId',
                            itemId: 'ProtocolTypeEditParentId',
                            fieldLabel: 'Label',
                            name: 'parent_type_id'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ProtocolTypeEditIdTree',
                            itemId: 'ProtocolTypeEditIdTree',
                            fieldLabel: 'Label',
                            name: 'id_tree'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});