/*
 * File: app/view/MyPanel3.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.MyPanel3', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.mypanel3',

    requires: [
        'Ext.form.Panel',
        'Ext.button.Button',
        'Ext.form.FieldSet',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Spacer',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.menu.Menu',
        'Ext.form.Label',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.grid.column.Date',
        'Ext.toolbar.Paging',
        'Ext.selection.CheckboxModel',
        'Ext.menu.Separator'
    ],

    border: false,
    id: 'CcpMovementPnl',
    itemId: 'CcpMovementPnl',
    layout: 'border',
    iconCls: 'icon-money_euro',
    title: 'Movimenti',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'panel',
                    region: 'west',
                    split: true,
                    id: 'CcpMLeftPnl',
                    width: 320,
                    autoScroll: true,
                    bodyCls: 'bck-content',
                    collapseDirection: 'left',
                    collapsible: false,
                    iconCls: 'icon-find',
                    title: 'Filtri',
                    titleCollapse: true,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            getFilter: function() {
                                var rec = Ext.getCmp('CcpMovementsFilterForm').getValues(),
                                    filter = {};

                                for (var key in rec) {
                                    if (rec[key] !== '') {
                                        filter[key] = rec[key];
                                    }
                                }

                                /*if (filter.class_id !== undefined) {
                                filter.class_id = Ext.encode(filter.class_id);
                                }*/

                                if (filter.school_year == 'all') {
                                    delete(filter.school_year);
                                }

                                if (filter.type_id == '0') {
                                    delete(filter.type_id);
                                }

                                if (filter.category_id == '0') {
                                    delete(filter.category_id);
                                }

                                delete(filter.subject);
                                if (rec.subject) {
                                    delete(filter.subject_data);

                                    var s = rec.subject.split('_');

                                    if (s[0] === 'S') {
                                        filter.subject_type = 'S';
                                        filter.subject_id = s[1];
                                        filter.subject_seat = s[2];
                                    } else if (s[0] === 'E') {
                                        filter.subject_type = 'E';
                                        filter.subject_id = s[1];
                                    }
                                } else if (rec.subject_data) {
                                    filter.subject_type = 'O';
                                }

                                return filter;
                            },
                            loadByFilter: function() {
                                if (mc2ui.app.ccpMovementsFilterEventTimeoutId > 0) {
                                    clearTimeout(mc2ui.app.ccpMovementsFilterEventTimeoutId);
                                }

                                mc2ui.app.ccpMovementsFilterEventTimeoutId = setTimeout(function() {
                                    Ext.getCmp('CcpTotalCredit').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalCreditPayed').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalCreditToPay').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalCreditExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalCreditNotExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalDebit').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalDebitPayed').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalDebitToPay').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalDebitExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalDebitNotExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotal').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalPayed').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalToPay').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpTotalNotExp').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpMovTotaImportTxt').setValue(Ext.util.Format.number(0, '0.000,00/i'));
                                    Ext.getCmp('CcpMovTotaDiscountTxt').setValue(Ext.util.Format.number(0, '0.000,00/i'));


                                    Ext.getCmp('CcpMovementsFilterForm').generateFilterString();


                                    Ext.getStore('CcpMovements').currentPage = 1;
                                    Ext.getStore('CcpMovements').load({
                                        callback: function(records, operation, success, a, b) {
                                            var res = Ext.decode(operation.response.responseText);

                                            Ext.getCmp('CcpTotalCredit').setValue(Ext.util.Format.number(res.total_credit, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalCreditPayed').setValue(Ext.util.Format.number(res.total_credit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalCreditToPay').setValue(Ext.util.Format.number(res.total_credit-res.total_credit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalCreditExp').setValue(Ext.util.Format.number(res.total_credit_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalCreditNotExp').setValue(Ext.util.Format.number(res.total_credit_not_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalDebit').setValue(Ext.util.Format.number(res.total_debit, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalDebitPayed').setValue(Ext.util.Format.number(res.total_debit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalDebitToPay').setValue(Ext.util.Format.number(res.total_debit-res.total_debit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalDebitExp').setValue(Ext.util.Format.number(res.total_debit_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalDebitNotExp').setValue(Ext.util.Format.number(res.total_debit_not_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpTotal').setValue(Ext.util.Format.number(res.total_credit - res.total_debit, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalPayed').setValue(Ext.util.Format.number(res.total_credit_payed - res.total_debit_payed, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalToPay').setValue(Ext.util.Format.number((res.total_credit - res.total_debit)-(res.total_credit_payed - res.total_debit_payed), '0.000,00/i'));
                                            Ext.getCmp('CcpTotalExp').setValue(Ext.util.Format.number(res.total_credit_exp - res.total_debit_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpTotalNotExp').setValue(Ext.util.Format.number(res.total_credit_not_exp - res.total_debit_not_exp, '0.000,00/i'));
                                            Ext.getCmp('CcpMovTotaImportTxt').setValue(Ext.util.Format.number(parseFloat(res.total_credit)+res.total_additionals*-1, '0.000,00/i'));
                                            Ext.getCmp('CcpMovTotaDiscountTxt').setValue(Ext.util.Format.number(res.total_additionals, '0.000,00/i'));

                                        }
                                    });
                                }, 500);


                            },
                            generateFilterString: function() {
                                var filterArr = [],
                                    filterStr = '',
                                    filter = Ext.getCmp('CcpMovementsFilterForm').getValues(),
                                    genericSearchVal = Ext.getCmp('CcpGenericSearchCmb').getRawValue();

                                filter.credit = filter.credit == '0' ? false : true;
                                filter.debit = filter.debit == '0' ? false : true;
                                filter.payed = filter.payed == '0' ? false : true;
                                filter.not_payed = filter.not_payed == '0' ? false : true;
                                filter.expired = filter.expired == '0' ? false : true;
                                filter.not_expired = filter.not_expired == '0' ? false : true;
                                filter.invoiced = filter.invoiced == '0' ? false : true;
                                filter.not_invoiced = filter.not_invoiced == '0' ? false : true;
                                filter.discounted = filter.discounted == '0' ? false : true;
                                filter.not_discounted = filter.not_discounted == '0' ? false : true;


                                if(filter.credit === true) {
                                    if(filter.debit === false) {
                                        filterArr.push('a credito');
                                    }
                                } else {
                                    if(filter.debit === true) {
                                        filterArr.push('a debito');
                                    }
                                }

                                if(filter.invoiced === true) {
                                    if(filter.not_invoiced === false) {
                                        filterArr.push('fatturati');
                                    }
                                } else {
                                    if(filter.not_invoiced === true) {
                                        filterArr.push('non fatturati');
                                    }
                                }

                                if(filter.payed === true) {
                                    if(filter.not_payed === false) {
                                        filterArr.push('pagati');
                                    }
                                } else {
                                    if(filter.not_payed === true) {
                                        filterArr.push('non pagati');
                                    }
                                }
                                if(filter.expired === true) {
                                    if(filter.not_expired === false) {
                                        filterArr.push('scaduti');
                                    }
                                } else {
                                    if(filter.not_expired === true) {
                                        filterArr.push('non scaduti');
                                    }
                                }

                                filterStr = filterArr.join(', ') + ' ';
                                if( filterArr.length === 0) filterStr = '';
                                filterArr = [];

                                if(filter.creation_date_start) {
                                    filter.creation_date_start  =Ext.Date.format(new Date(filter.creation_date_start), 'd/m/Y');
                                    filter.creation_date_end  =Ext.Date.format(new Date(filter.creation_date_end), 'd/m/Y');
                                    filterArr.push('con data creazione dal ' + filter.creation_date_start + ' al ' +  filter.creation_date_end);
                                }
                                if(filter.expiration_date_start) {
                                    filter.expiration_date_start  =Ext.Date.format(new Date(filter.expiration_date_start), 'd/m/Y');
                                    filter.expiration_date_end  =Ext.Date.format(new Date(filter.expiration_date_end), 'd/m/Y');
                                    filterArr.push('con scadenza dal ' +  filter.expiration_date_start + ' al ' +  filter.expiration_date_end);
                                }
                                if(filter.payment_date_start) {
                                    filter.payment_date_start  =Ext.Date.format(new Date(filter.payment_date_start), 'd/m/Y');
                                    filter.payment_date_end  =Ext.Date.format(new Date(filter.payment_date_end), 'd/m/Y');
                                    filterArr.push('con pagamenti dal ' +  filter.payment_date_start + ' al ' +  filter.payment_date_end);
                                }
                                if(filter.school_year) {
                                    if(filter.school_year != 'all') {
                                        filterArr.push("nell'anno scolastico " + filter.school_year);
                                    }
                                }
                                if(filter.type_id > 0) {
                                    filterArr.push("con tipo di movimento: " + genericSearchVal);
                                }
                                if(filter.category_id > 0) {
                                    filterArr.push("nella categoria: " + genericSearchVal);
                                }
                                filterStr = 'Elenco movimenti ' + filterStr + filterArr.join(', ');

                                Ext.getCmp('CcpFilterStringLbl').setText(filterStr);
                            },
                            border: false,
                            id: 'CcpMovementsFilterForm',
                            bodyCls: 'bck-content',
                            bodyPadding: 10,
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var form = Ext.getCmp('CcpMovementsFilterForm').getForm(),
                                            io = Ext.getCmp('CcpIncoming'),
                                            cs = Ext.getCmp('CcpCreationDateStart'),
                                            ce = Ext.getCmp('CcpCreationDateEnd'),
                                            sy = Ext.getCmp('CcpSchoolYear'),
                                            //ty = Ext.getCmp('CcpType'),
                                            ca = Ext.getCmp('CcpCategory');

                                        form.reset();
                                        io.select('A');
                                        cs.setValue('01/01/' + new Date().getFullYear());
                                        // ce.setValue('31/12/' + new Date().getFullYear());
                                        ce.setValue();
                                        sy.setValue('all');
                                        //ty.select();
                                        ca.select(0);

                                        if(mc2ui.app.settings.ccp_filter_movement_by_school_year === true) {
                                            cs.setValue();

                                            Ext.each(Ext.getStore('CcpSchoolYearsFilter').getRange(), function(v){
                                                if(mc2ui.app.settings.mcDb.schoolYear == v.get('id')) {
                                                    sy.select(v);
                                                    //return;
                                                }
                                            });
                                        }
                                    },
                                    margin: '0 0 10 0',
                                    iconCls: 'icon-arrow_undo',
                                    text: 'Reimposta'
                                },
                                {
                                    xtype: 'fieldset',
                                    padding: 5,
                                    title: '<b> Filtri per anno scolastico studente</b>',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            layout: {
                                                type: 'hbox',
                                                align: 'bottom',
                                                padding: '0 0 5 0'
                                            },
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    flex: 1,
                                                    id: 'CcpSchoolYear',
                                                    fieldLabel: '<span style="color:#15428b">Anno riferimento studente</span>',
                                                    labelAlign: 'top',
                                                    name: 'subject_school_year',
                                                    emptyText: 'Tutti',
                                                    validateBlank: true,
                                                    editable: false,
                                                    displayField: 'name',
                                                    forceSelection: true,
                                                    queryMode: 'local',
                                                    store: 'McDbs',
                                                    valueField: 'name',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onComboboxSelect11,
                                                            scope: me
                                                        },
                                                        afterrender: {
                                                            fn: me.onCcpSchoolYearAfterRender,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('CcpSchoolYear').setValue();
                                                        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

                                                    },
                                                    margin: '0 0 0 5',
                                                    iconCls: 'icon-cancel'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            margin: '0 0 5 0',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    disabled: true,
                                                    id: 'CcpMovementFilterAddressCnt',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'bottom'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            flex: 1,
                                                            id: 'CcpAddressSectionCmb',
                                                            fieldLabel: '<span style="color:#15428b">Indirizzo</span>',
                                                            labelAlign: 'top',
                                                            name: 'address_id[]',
                                                            emptyText: 'Tutti',
                                                            editable: false,
                                                            displayField: 'description',
                                                            forceSelection: true,
                                                            multiSelect: true,
                                                            queryMode: 'local',
                                                            store: 'Indirizzi',
                                                            valueField: 'id',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onCcpAddressSectionCmbSelect,
                                                                    scope: me
                                                                },
                                                                change: {
                                                                    fn: me.onCcpAddressSectionCmbChange,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                Ext.getCmp('CcpAddressSectionCmb').setValue();
                                                                Ext.getCmp('CcpClassSectionCmb').setValue();
                                                                Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

                                                            },
                                                            margin: '0 0 0 5',
                                                            iconCls: 'icon-cancel'
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'container',
                                                    flex: 1,
                                                    disabled: true,
                                                    id: 'CcpMovementFilterClassCnt',
                                                    margin: '5 0 0 0',
                                                    layout: {
                                                        type: 'hbox',
                                                        align: 'bottom'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'combobox',
                                                            flex: 1,
                                                            id: 'CcpClassSectionCmb',
                                                            itemId: 'CcpClassSectionCmb',
                                                            fieldLabel: '<span style="color:#15428b">Classe</span>',
                                                            labelAlign: 'top',
                                                            name: 'class_id[]',
                                                            emptyText: 'Tutte',
                                                            editable: false,
                                                            displayField: 'display',
                                                            forceSelection: true,
                                                            multiSelect: true,
                                                            queryMode: 'local',
                                                            store: 'Classi',
                                                            valueField: 'id',
                                                            listeners: {
                                                                select: {
                                                                    fn: me.onComboboxSelect5,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'button',
                                                            handler: function(button, e) {
                                                                Ext.getCmp('CcpClassSectionCmb').setValue();
                                                                Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

                                                            },
                                                            margin: '0 0 0 5',
                                                            iconCls: 'icon-cancel',
                                                            text: ''
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            disabled: true,
                                            id: 'CcpMovementParentIdCnt',
                                            layout: {
                                                type: 'hbox',
                                                align: 'bottom'
                                            },
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    flex: 1,
                                                    id: 'CcpMovementParentIdCmb',
                                                    itemId: 'CcpMovementParentIdCmb',
                                                    fieldLabel: '<span style="color:#15428b">Genitore</span>',
                                                    labelAlign: 'top',
                                                    name: 'parent_id',
                                                    emptyText: 'Tutti',
                                                    autoSelect: false,
                                                    forceSelection: true,
                                                    minChars: 2,
                                                    queryMode: 'local',
                                                    store: 'CcpParents',
                                                    typeAhead: true,
                                                    valueField: 'id_parente',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onComboboxSelect6,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('CcpMovementParentIdCmb').setValue();
                                                        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                    },
                                                    margin: '0 0 0 5',
                                                    iconCls: 'icon-cancel',
                                                    text: ''
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    padding: 5,
                                    title: '<b> Filtri per anno scolastico movimento</b>',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'bottom'
                                            },
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    flex: 1,
                                                    id: 'CcpMovementSchoolYearCmb',
                                                    itemId: 'CcpMovementSchoolYearCmb',
                                                    fieldLabel: '<span style="color:#15428b">Anno scolastico movimento</span>',
                                                    labelAlign: 'top',
                                                    name: 'movement_school_year',
                                                    emptyText: 'Tutti',
                                                    editable: false,
                                                    displayField: 'name',
                                                    forceSelection: true,
                                                    queryMode: 'local',
                                                    store: 'McDbs',
                                                    valueField: 'name',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onCcpMovementSchoolYearCmbSelect,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('CcpMovementSchoolYearCmb').setValue();
                                                        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                    },
                                                    margin: '0 0 0 5',
                                                    iconCls: 'icon-cancel',
                                                    text: ''
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            layout: {
                                                type: 'hbox',
                                                align: 'bottom'
                                            },
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    flex: 1,
                                                    id: 'CcpCategory',
                                                    fieldLabel: '<span style="color:#15428b">Categoria</span>',
                                                    labelAlign: 'top',
                                                    name: 'category_id',
                                                    emptyText: 'Tutte',
                                                    editable: false,
                                                    displayField: 'name',
                                                    forceSelection: true,
                                                    queryMode: 'local',
                                                    store: 'CcpCategoriesFilter',
                                                    valueField: 'id',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onCcpCategorySelect,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('CcpCategory').setValue();
                                                        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                    },
                                                    margin: '0 0 0 5',
                                                    iconCls: 'icon-cancel',
                                                    text: ''
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            layout: {
                                                type: 'hbox',
                                                align: 'bottom'
                                            },
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    tpl: '<ul class="x-list-plain">\n    <tpl for=".">\n        <li role="option" style="font-size:10x;line-height:17px" class="x-boundlist-item">{name} \n        <div style="color:#333;margin:-2px 0px 0px 0px;font-style:italic;font-size:10px">{school_year}</div></li>\n    </tpl>\n</ul>',
                                                    flex: 1,
                                                    id: 'CcpMovementTypeCmb',
                                                    fieldLabel: '<span style="color:#15428b">Tipo movimento</span>',
                                                    labelAlign: 'top',
                                                    name: 'type_ids[]',
                                                    emptyText: 'Tutti',
                                                    editable: false,
                                                    displayField: 'name_school_year',
                                                    forceSelection: true,
                                                    multiSelect: true,
                                                    queryMode: 'local',
                                                    store: 'CcpTypesFilter',
                                                    valueField: 'id',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onComboboxSelect,
                                                            scope: me
                                                        },
                                                        change: {
                                                            fn: me.onCcpMovementTypeCmbChange,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('CcpMovementTypeCmb').setValue();
                                                        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                    },
                                                    margin: '0 0 0 5',
                                                    iconCls: 'icon-cancel',
                                                    text: ''
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    padding: 5,
                                    title: 'Ricerca generica',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpSubjectType',
                                            itemId: 'CcpSubjectType',
                                            fieldLabel: 'Label',
                                            name: 'subject_type'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpSubjectId',
                                            itemId: 'CcpSubjectId',
                                            fieldLabel: 'Label',
                                            name: 'subject_id'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpSubjectSeat',
                                            itemId: 'CcpSubjectSeat',
                                            fieldLabel: 'Label',
                                            name: 'subject_seat'
                                        },
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            id: 'CcpGenericSearchCmb',
                                            itemId: 'CcpGenericSearchCmb',
                                            fieldLabel: '',
                                            name: 'query',
                                            emptyText: 'Cerca ...',
                                            hideTrigger: true,
                                            autoSelect: false,
                                            minChars: 2,
                                            selectOnTab: false,
                                            store: 'GenericSearches',
                                            valueField: 'id',
                                            listeners: {
                                                select: {
                                                    fn: me.onComboboxSelect4,
                                                    scope: me
                                                },
                                                blur: {
                                                    fn: me.onCcpGenericSearchCmbBlur,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpTypeId',
                                            fieldLabel: 'Label',
                                            name: 'type_id'
                                        },
                                        {
                                            xtype: 'tbspacer'
                                        },
                                        {
                                            xtype: 'button',
                                            iconCls: 'icon-magnifier',
                                            text: ''
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    padding: 5,
                                    title: 'Tipo addebito genitore',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            id: 'CcpMovementPaymentMethodCmb',
                                            itemId: 'CcpMovementPaymentMethodCmb',
                                            fieldLabel: '',
                                            name: 'payment_method',
                                            emptyText: 'Tutti',
                                            displayField: 'name',
                                            store: 'CcpPaymentMethodsFilter',
                                            valueField: 'id',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpMovementPaymentMethodCmbChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                Ext.getCmp('CcpMovementPaymentMethodCmb').setValue();
                                                Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                            },
                                            margin: '0 0 0 5',
                                            iconCls: 'icon-cancel',
                                            text: ''
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    padding: 5,
                                    title: 'Stato studente',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            id: 'CcpStudentStateCmb',
                                            fieldLabel: '',
                                            name: 'student_state',
                                            emptyText: 'Tutti',
                                            displayField: 'descrizione',
                                            store: 'StudentStates',
                                            valueField: 'id_stato_studente_personalizzato',
                                            listeners: {
                                                select: {
                                                    fn: me.onComboboxSelect8,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                Ext.getCmp('CcpStudentStateCmb').setValue();
                                                Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

                                            },
                                            margin: '0 0 0 5',
                                            iconCls: 'icon-cancel'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Direzione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'CcpIncoming',
                                            name: 'incoming',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            forceSelection: true,
                                            queryMode: 'local',
                                            store: 'CcpDirectionsFilter',
                                            valueField: 'id',
                                            listeners: {
                                                select: {
                                                    fn: me.onComboboxSelect3,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Data',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            fieldLabel: 'Tipo',
                                            labelAlign: 'right',
                                            labelWidth: 30,
                                            value: 'expiration',
                                            forceSelection: true,
                                            store: 'CcpDateTypeFilter',
                                            valueField: 'id',
                                            listeners: {
                                                select: {
                                                    fn: me.onComboboxSelect2,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'CcpCreationDateEnd',
                                            id: 'CcpCreationDateStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 30,
                                            name: 'expiration_date_start',
                                            vtype: 'daterange',
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'Y-m-d',
                                            listeners: {
                                                change: {
                                                    fn: me.onDatefieldChange,
                                                    scope: me
                                                },
                                                afterrender: {
                                                    fn: me.onCcpCreationDateStartAfterRender,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'CcpCreationDateStart',
                                            id: 'CcpCreationDateEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 30,
                                            name: 'expiration_date_end',
                                            vtype: 'daterange',
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'Y-m-d',
                                            listeners: {
                                                change: {
                                                    fn: me.onDatefieldChange1,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Numero transazione',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpNumberStart',
                                            fieldLabel: 'Dal',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'number_start',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange1,
                                                    delay: 500,
                                                    buffer: 500,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpNumberEnd',
                                            fieldLabel: 'al',
                                            labelAlign: 'right',
                                            labelWidth: 50,
                                            name: 'number_end',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange12,
                                                    delay: 500,
                                                    buffer: 500,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    title: 'Situazione crediti/debiti',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'CcpHasRemaining',
                                            name: 'credit',
                                            boxLabel: 'Crediti',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpHasRemainingChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'CcpHasAdditionals',
                                            name: 'debit',
                                            boxLabel: 'Debiti ',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpHasAdditionalsChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Situazione pagamenti',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'CcpHasPayments',
                                            name: 'payed',
                                            boxLabel: 'Pagati',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpHasPaymentsChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'CcpHasPayments3',
                                            name: 'not_payed',
                                            boxLabel: 'Non pagati',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpHasPaymentsChange3,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Situazione scadenze',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'CcpHasPayments1',
                                            name: 'expired',
                                            boxLabel: 'Scaduti',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpHasPaymentsChange2,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            id: 'CcpHasPayments2',
                                            name: 'not_expired',
                                            boxLabel: 'Non scaduti',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpHasPaymentsChange21,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Situazione fatture',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            fieldLabel: '',
                                            name: 'invoiced',
                                            boxLabel: 'Fatturati',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCheckboxfieldChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            fieldLabel: '',
                                            name: 'not_invoiced',
                                            boxLabel: 'Da fatturare',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCheckboxfieldChange1,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Situazione sconti',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            fieldLabel: '',
                                            name: 'discounted',
                                            boxLabel: 'Scontati',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCheckboxfieldChange2,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            flex: 1,
                                            fieldLabel: '',
                                            name: 'not_discounted',
                                            boxLabel: 'Non scontati',
                                            checked: true,
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCheckboxfieldChange3,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Situazione blocco movimenti',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'checkboxfield',
                                                    flex: 1,
                                                    width: 80,
                                                    fieldLabel: '',
                                                    name: 'locked',
                                                    boxLabel: 'Bloccati',
                                                    checked: true,
                                                    inputValue: '1',
                                                    uncheckedValue: '0',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCheckboxfieldChange4,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'checkboxfield',
                                                    flex: 1,
                                                    fieldLabel: '',
                                                    name: 'not_locked',
                                                    boxLabel: 'Non bloccati',
                                                    checked: true,
                                                    inputValue: '1',
                                                    uncheckedValue: '0',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCheckboxfieldChange5,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Situazione pubblicazione movimenti',
                                    items: [
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'checkboxfield',
                                                    flex: 1,
                                                    width: 80,
                                                    fieldLabel: '',
                                                    name: 'published',
                                                    boxLabel: 'Pubblicati',
                                                    checked: true,
                                                    inputValue: '1',
                                                    uncheckedValue: '0',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCheckboxfieldChange6,
                                                            scope: me
                                                        }
                                                    }
                                                },
                                                {
                                                    xtype: 'checkboxfield',
                                                    flex: 1,
                                                    fieldLabel: '',
                                                    name: 'not_published',
                                                    boxLabel: 'Non pubblicati',
                                                    checked: true,
                                                    inputValue: '1',
                                                    uncheckedValue: '0',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onCheckboxfieldChange7,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Uff. post. / Varie',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpMiscellaneous',
                                            name: 'miscellaneous',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange11,
                                                    delay: 500,
                                                    buffer: 500,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    hidden: true,
                                    title: 'Note',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpNote',
                                            name: 'note',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange111,
                                                    delay: 500,
                                                    buffer: 500,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var form = Ext.getCmp('CcpMovementsFilterForm').getForm(),
                                            io = Ext.getCmp('CcpIncoming'),
                                            cs = Ext.getCmp('CcpCreationDateStart'),
                                            ce = Ext.getCmp('CcpCreationDateEnd'),
                                            es = Ext.getCmp('CcpExpirationDateStart'),
                                            ee = Ext.getCmp('CcpExpirationDateEnd'),
                                            sy = Ext.getCmp('CcpSchoolYear'),
                                            sb = Ext.getCmp('CcpSubject'),
                                            st = Ext.getCmp('CcpMSubjectType'),
                                            ty = Ext.getCmp('CcpType'),
                                            ca = Ext.getCmp('CcpCategory');

                                        form.reset();
                                        io.select('A');
                                        cs.setValue('01/01/' + new Date().getFullYear());
                                        ce.setValue('31/12/' + new Date().getFullYear());
                                        es.setValue();
                                        ee.setValue();
                                        sy.select('all');
                                        sb.select();
                                        st.select('A');
                                        ty.select();
                                        ca.select(0);
                                    },
                                    hidden: true,
                                    margin: '0 0 10 0',
                                    iconCls: 'icon-magnifier',
                                    text: 'Cerca'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'panel',
                    permissible: true,
                    region: 'center',
                    id: 'CcpMovementsPnl',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        /*Ext.widget('CcpMovementEditWin').show();

                                        Ext.getCmp('CcpMovementCreationDate').setValue(new Date());*/

                                        mc2ui.view.CcpPnl.openEditMovementWin();
                                    },
                                    id: 'CcpMovementNewBtn',
                                    itemId: 'CcpMovementNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    text: 'Invia ai movimenti filtrati',
                                    menu: {
                                        xtype: 'menu',
                                        generateReminder: function(reminderType, html) {
                                            var rec = Ext.getCmp('CcpMovementsFilterForm').getForm().getValues(),
                                                sel = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection(),
                                                ids = [];
                                            rec.reminder_type = reminderType;


                                            Ext.each(sel, function(value){
                                                ids.push(value.get('id'));
                                            });
                                            rec.ids = Ext.encode(ids);

                                            if(html) rec.html = html;






                                            Ext.MessageBox.show({
                                                title:'Opzioni di invio',
                                                msg: 'Indicare a chi dovrà essere spedita la mail',
                                                buttonText: {yes: "Al SOLO parente pagante",no: "A tutti i parenti",cancel: "Annulla"},
                                                fn: function(btn){
                                                    if(btn==='no') {
                                                        rec.all_parents = 1;
                                                    }

                                                    if(btn!=='cancel') {
                                                        Ext.Msg.alert('INFO', 'La generazione è in corso e potrebbe richiedere qualche minuto in base al numero di solleciti da creare. <br />Verificate i dati nella sezione solleciti prima di confermare l\'invio');

                                                        Ext.Ajax.request({
                                                            url: '/mc2-api/ccp/reminder',
                                                            method: 'POST',
                                                            params: rec,
                                                            timeout: 60000,
                                                            success: function() {
                                                            },
                                                            failure: function(response, opts) {
                                                                Ext.Msg.alert('ERRORE', 'Nell\'ultima generazione solleciti sono insorti dei problemi per i quali la lista potrebbe non essere completa. Rigenerarla dopo aver verificato i dati');
                                                            }
                                                        });
                                                    }
                                                }
                                            });


                                        },
                                        id: 'CcpReminderMn',
                                        items: [
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpReminderMn').generateReminder('INFO');
                                                },
                                                text: 'Pro memoria'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.widget('CcpReminderCustomWin').show();
                                                    Ext.Ajax.request({
                                                        url: '/mc2-api/core/parameter',
                                                        method: 'GET',
                                                        params: {
                                                            name: 'REMINDER_MESSAGE_INFO_CUSTOMIZED'
                                                        },
                                                        success: function(res) {
                                                            var r = Ext.decode(res.responseText);
                                                            if(r.success===true) {
                                                                Ext.getCmp('CcpCustomReminderHtml').setValue(r.results[0].value);
                                                            }
                                                        }
                                                    });

                                                },
                                                text: 'Pro memoria personalizzato'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpReminderMn').generateReminder('WARNING');
                                                },
                                                text: 'Sollecito'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpReminderMn').generateReminder('WARNING_2');
                                                },
                                                text: 'Secondo sollecito'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpReminderMn').generateReminder('WARNING_3');
                                                },
                                                text: 'Terzo sollecito'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.widget('CcpReminderCustomWin').show();
                                                    Ext.getCmp('CcpReminderCustomWin').isWarning = true;
                                                    Ext.Ajax.request({
                                                        url: '/mc2-api/core/parameter',
                                                        method: 'GET',
                                                        params: {
                                                            name: 'REMINDER_MESSAGE_WARNING_CUSTOMIZED'
                                                        },
                                                        success: function(res) {
                                                            var r = Ext.decode(res.responseText);
                                                            if(r.success===true) {
                                                                Ext.getCmp('CcpCustomReminderHtml').setValue(r.results[0].value);
                                                            }
                                                        }
                                                    });

                                                },
                                                text: 'Sollecito personalizzato'
                                            }
                                        ]
                                    }
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ExportEasyWin').show();

                                    },
                                    text: 'Esportazione EASY'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ImportPaymentsEasyWin').show();

                                    },
                                    text: 'Importa pagamenti EASY'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('CcpPrintTypeWin').show();
                                        Ext.getCmp('CcpPrintTypeType').setValue('M');
                                    },
                                    iconCls: 'icon-printer',
                                    text: 'Stampa elenco'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {

                                        var rec = Ext.getCmp('CcpMovementsFilterForm').getForm().getValues(),
                                            gridName = 'CcpMovementsGrid',
                                            ids = [],
                                            sel = Ext.getCmp(gridName).getSelectionModel().getSelection();

                                        Ext.each(sel, function(value){
                                            ids.push(value.get('id'));
                                        });

                                        if(ids.length>0) rec.ids = Ext.encode(ids);


                                        rec.newSpool = 1;
                                        rec.namespace = 'CCP';
                                        rec.type = 'PDF';
                                        rec.mime = 'application/pdf';
                                        rec.print = mc2ui.app.settings.prints.declaration_movement;

                                        if (rec.subject_type === 'O') {
                                            rec.subject_data = rec.query;
                                        }

                                        rec.sort = [];
                                        Ext.each(Ext.getCmp(gridName).getStore().getSorters(), function(sorter){
                                            rec.sort = rec.sort.concat({
                                                "property": sorter.property,
                                                "direction": sorter.direction
                                            });
                                        });
                                        rec.sort = Ext.encode(rec.sort);


                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/print',
                                            params: rec,
                                            success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                            }
                                        });
                                    },
                                    text: 'Stampa dichiarazione'
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'label',
                                    id: 'CcpFilterStringLbl',
                                    itemId: 'CcpFilterStringLbl',
                                    padding: 5,
                                    style: ' font: italic bold 12px Arial',
                                    text: 'Filtri'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    text: 'Azioni movimenti selezionati',
                                    menu: {
                                        xtype: 'menu',
                                        lockUnlockMovements: function(lock) {
                                            var rec = Ext.getCmp('CcpMovementsFilterForm').getForm().getValues(),
                                                sel = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection(),
                                                ids = [];


                                            Ext.each(sel, function(value){
                                                ids.push(value.get('id'));
                                            });

                                            if(ids.length>0) rec.ids = Ext.encode(ids);
                                            rec.action_locked=lock;

                                            Ext.getCmp('CcpMovementsGrid').setLoading(true);
                                            Ext.Ajax.request({
                                                url: '/mc2-api/ccp/lock-unlock-movement',
                                                method: 'POST',
                                                params: rec,
                                                success: function() {
                                                    Ext.getCmp('CcpMovementsGrid').getStore().load();
                                                    Ext.getCmp('CcpMovementsGrid').setLoading(false);
                                                },
                                                failure: function(response, opts) {
                                                    Ext.Msg.alert('ERRORE', 'Errore durante l\'operazione');
                                                    Ext.getCmp('CcpMovementsGrid').setLoading(false);
                                                }
                                            });
                                        },
                                        publishUnpublishMovements: function(publish) {
                                            var rec = Ext.getCmp('CcpMovementsFilterForm').getForm().getValues(),
                                                sel = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection(),
                                                ids = [];


                                            Ext.each(sel, function(value){
                                                ids.push(value.get('id'));
                                            });

                                            if(ids.length>0) rec.ids = Ext.encode(ids);
                                            rec.action_publish=publish;

                                            Ext.getCmp('CcpMovementsGrid').setLoading(true);
                                            Ext.Ajax.request({
                                                url: '/mc2-api/ccp/publish-unpublish-movement',
                                                method: 'POST',
                                                params: rec,
                                                success: function() {
                                                    Ext.getCmp('CcpMovementsGrid').getStore().load();
                                                    Ext.getCmp('CcpMovementsGrid').setLoading(false);
                                                },
                                                failure: function(response, opts) {
                                                    Ext.Msg.alert('ERRORE', 'Errore durante l\'operazione');
                                                    Ext.getCmp('CcpMovementsGrid').setLoading(false);
                                                }
                                            });
                                        },
                                        id: 'CcpLockUnlockMn',
                                        width: 150,
                                        items: [
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpLockUnlockMn').lockUnlockMovements(true);
                                                },
                                                iconCls: 'icon-lock',
                                                text: 'Blocca'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpLockUnlockMn').lockUnlockMovements(false);
                                                },
                                                iconCls: 'icon-lock_open',
                                                text: 'Sblocca'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpLockUnlockMn').publishUnpublishMovements(1);
                                                },
                                                iconCls: 'icon-world',
                                                text: 'Pubblica'
                                            },
                                            {
                                                xtype: 'menuitem',
                                                handler: function(item, e) {
                                                    Ext.getCmp('CcpLockUnlockMn').publishUnpublishMovements(0);
                                                },
                                                iconCls: 'icon-world_delete',
                                                text: 'Rimuovi pubblicazione'
                                            }
                                        ]
                                    }
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('CcpExcelMovementExportWin').show();

                                    },
                                    iconCls: 'icon-page_excel',
                                    text: ''
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            setColors: function(field, value) {

                                if (parseInt(value) > 0) {
                                    //Ext.get(field.getInputId()).addCls('positive');
                                    field.setFieldStyle('color: green');
                                } else if (parseInt(value) < 0) {
                                    //Ext.get(field.getInputId()).addCls('negative');
                                    field.setFieldStyle('color: red');
                                } else {
                                    field.setFieldStyle('color: #000');
                                }
                            },
                            dock: 'bottom',
                            id: 'CcpTotaleToolBar',
                            itemId: 'CcpTotaleToolBar',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    width: 300,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpMovTotaImportTxt',
                                            width: 200,
                                            fieldLabel: 'Totale importi',
                                            labelAlign: 'right',
                                            labelWidth: 110
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpMovTotaDiscountTxt',
                                            width: 200,
                                            fieldLabel: 'Totale sconti applicati',
                                            labelAlign: 'right',
                                            labelWidth: 110
                                        }
                                    ]
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'container',
                                    width: 180,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            flex: 1,
                                            html: '<b>Totali</b>',
                                            style: 'text-align:right'
                                        },
                                        {
                                            xtype: 'textfield',
                                            flex: 1,
                                            id: 'CcpTotalCredit',
                                            itemId: 'CcpTotalCredit',
                                            fieldLabel: 'Crediti',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 60,
                                            name: 'total_credit',
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpMovementsSumChange4,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalDebit',
                                            itemId: 'CcpTotalDebit',
                                            fieldLabel: 'Debiti',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 60,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpPaymentsSumChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotal',
                                            itemId: 'CcpTotal',
                                            fieldLabel: 'Totali',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 60,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpRemainingsSumChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    width: 100,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            flex: 1,
                                            html: '<b>Pagati</b>',
                                            style: 'text-align:right'
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalCreditPayed',
                                            itemId: 'CcpTotalCreditPayed',
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpMovementsSumChange31,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalDebitPayed',
                                            itemId: 'CcpTotalDebitPayed',
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpPaymentsSumChange3,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalPayed',
                                            itemId: 'CcpTotalPayed',
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpRemainingsSumChange3,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    width: 100,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            html: '<b>Da pagare</b>',
                                            style: 'text-align:right',
                                            text: ''
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalCreditToPay',
                                            fieldLabel: '',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpTotalCreditToPayChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalDebitToPay',
                                            fieldLabel: '',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpTotalDebitToPayChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalToPay',
                                            fieldLabel: '',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpTotalToPayChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    width: 100,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            flex: 1,
                                            html: '<b>Scaduti</b>',
                                            style: 'text-align:right',
                                            text: ''
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalCreditExp',
                                            itemId: 'CcpTotalCreditExp',
                                            width: 280,
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 180,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpMovementsSumChange21,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalDebitExp',
                                            itemId: 'CcpTotalDebitExp',
                                            width: 280,
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 180,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpPaymentsSumChange2,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalExp',
                                            itemId: 'CcpTotalExp',
                                            width: 280,
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 180,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpRemainingsSumChange2,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    width: 100,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            flex: 1,
                                            html: '<b>Non scaduti</b>',
                                            style: 'text-align:right',
                                            text: ''
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalCreditNotExp',
                                            itemId: 'CcpTotalCreditNotExp',
                                            width: 280,
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 180,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpMovementsSumChange11,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalDebitNotExp',
                                            itemId: 'CcpTotalDebitNotExp',
                                            width: 280,
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 180,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpPaymentsSumChange1,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'textfield',
                                            id: 'CcpTotalNotExp',
                                            itemId: 'CcpTotalNotExp',
                                            width: 280,
                                            fieldLabel: '',
                                            labelAlign: 'right',
                                            labelStyle: 'font-weight: bold',
                                            labelWidth: 180,
                                            fieldStyle: 'text-align:right;',
                                            readOnly: true,
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpRemainingsSumChange1,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            border: false,
                            id: 'CcpMovementsGrid',
                            header: false,
                            title: 'Movimenti',
                            emptyText: 'Nessun movimento',
                            enableColumnMove: false,
                            store: 'CcpMovements',
                            viewConfig: {
                                listeners: {
                                    itemdblclick: {
                                        fn: me.onViewItemDblClick,
                                        scope: me
                                    }
                                }
                            },
                            columns: [
                                {
                                    xtype: 'actioncolumn',
                                    width: 100,
                                    resizable: false,
                                    items: [
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('remaining') > 0) {
                                                    return 'icon-coins_delete';
                                                }
                                            },
                                            getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                var remaining = r.get('remaining'),
                                                    i = '',
                                                    q = 'pagare: ';


                                                if (remaining > 0) {
                                                    if (!r.get('incoming')) {
                                                        i = 'Non eseguito';
                                                        q = 'prelevare: ';
                                                    }
                                                    return i + '<br />Da ' + q + Ext.util.Format.number(remaining, '0,000.00');
                                                }
                                            }
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (!r.get('incoming')) {
                                                    return 'icon-basket_remove';
                                                }
                                            },
                                            getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                if (!r.get('incoming')) {
                                                    return 'In uscita';
                                                }
                                            },
                                            altText: 'Note'
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('school_year') !== r.get('subject_school_year')) {
                                                    return 'icon-error';
                                                }
                                            },
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                if (record.get('school_year') !== record.get('subject_school_year')) {
                                                    console.log(record.get('school_year'), record.get('subject_school_year'));

                                                    var msg = "L'anno del tipo movimento è diverso dall'anno di riferimento dello studente.<br />";
                                                    msg += "Questo può portare a comportamenti non desiderati poichè tutti i dati relativamente allo studente verranno presi dall'anno di riferimento "+record.get('subject_school_year')+",";
                                                    msg += "mentre il tipo di movimento è stato legato all'anno "+ record.get('school_year');

                                                    Ext.Msg.alert('ATTENZIONE', msg);
                                                }


                                            },
                                            altText: 'Note'
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('locked')===true) return 'icon-lock';
                                                else return '';
                                            }
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('date_published')) return 'icon-world';
                                                else return '';
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        return value < 1 ? '-' : value;
                                    },
                                    hidden: true,
                                    width: 70,
                                    resizable: false,
                                    sortable: true,
                                    align: 'right',
                                    dataIndex: 'number',
                                    text: 'Numero'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        if(value<0) return '-';
                                        return value;
                                    },
                                    width: 65,
                                    align: 'right',
                                    dataIndex: 'invoice_number',
                                    text: 'Fattura'
                                },
                                {
                                    xtype: 'datecolumn',
                                    width: 80,
                                    resizable: false,
                                    sortable: true,
                                    align: 'center',
                                    dataIndex: 'creation_date',
                                    groupable: true,
                                    text: 'Data',
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'datecolumn',
                                    width: 80,
                                    resizable: false,
                                    sortable: true,
                                    align: 'center',
                                    dataIndex: 'expiration_date',
                                    groupable: true,
                                    text: 'Scadenza',
                                    format: 'd/m/Y'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        switch (record.get('subject_type')) {
                                            case 'S':
                                            value = '(S) ' + value;
                                            if(record.get('subject_class')){
                                                value += ' - ' + record.get('subject_class');
                                            }
                                            if(record.get('subject_school_address_code')){
                                                value += ' (' + record.get('subject_school_address_code') + ')';
                                            }
                                            break;
                                            case 'E':
                                            value = '(P) ' + value;
                                            break;
                                            case 'O':
                                            value = '(A) ' + value;
                                            break;
                                            default:
                                            value = '-';
                                            break;
                                        }

                                        return value;
                                    },
                                    width: 200,
                                    sortable: true,
                                    dataIndex: 'subject_data',
                                    groupable: true,
                                    text: 'Debitore/Creditore'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        if (record.get('description') && record.get('description') !== value) value+=' - '+record.get('description');

                                        return value;
                                    },
                                    sortable: true,
                                    dataIndex: 'type_text',
                                    groupable: true,
                                    text: 'Tipo movimento',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    hidden: true,
                                    width: 100,
                                    dataIndex: 'category_text',
                                    text: 'Categoria'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        return Ext.util.Format.number(value, '0,000.00');

                                    },
                                    width: 70,
                                    align: 'right',
                                    dataIndex: 'amount',
                                    text: 'Importo'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        var color;
                                        if(record.get('incoming') === true) {
                                            if(record.get('linked_additionals').length > 0) {
                                                if(value<=0) color = 'green';
                                                else color = 'red';

                                                value = ' <font style="color:'+color+'">' + Ext.util.Format.number(value, '0,000.00') + '</font>';
                                                return value;

                                            }

                                        }
                                        return '';
                                    },
                                    width: 70,
                                    align: 'right',
                                    dataIndex: 'total_additionals',
                                    text: 'Sconto'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        if(record.get('incoming') === true) {
                                            return Ext.util.Format.number(value, '0,000.00');
                                        }
                                        return '';
                                    },
                                    width: 70,
                                    sortable: true,
                                    align: 'right',
                                    dataIndex: 'total',
                                    text: 'Credito'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        if(record.get('incoming') === false) {
                                            return Ext.util.Format.number(value, '0,000.00');
                                        }
                                        return '';
                                    },
                                    width: 70,
                                    sortable: true,
                                    align: 'right',
                                    dataIndex: 'total',
                                    text: 'Debito'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        return Ext.util.Format.number(value, '0,000.00');
                                    },
                                    width: 70,
                                    align: 'right',
                                    dataIndex: 'total_payments',
                                    text: 'Pagato'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                        var value = Ext.util.Format.number(value, '0,000.00');
                                        if (record.get('expired')) {
                                            return '<font style="color:red">'+value+'</font>';
                                        } else {
                                            return value;
                                        }
                                    },
                                    width: 70,
                                    align: 'right',
                                    dataIndex: 'remaining',
                                    text: 'Da pagare'
                                },
                                {
                                    xtype: 'gridcolumn',
                                    text: 'Ultimo pagamento',
                                    columns: [
                                        {
                                            xtype: 'datecolumn',
                                            align: 'center',
                                            dataIndex: 'last_payment',
                                            text: 'Data',
                                            format: 'd/m/Y'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            width: 150,
                                            dataIndex: 'last_payment_method_text',
                                            text: 'Metodo'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 90,
                                    resizable: false,
                                    items: [
                                        {
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                var s = Ext.getStore('CcpLinkedPayments');

                                                Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();
                                                Ext.getCmp('CcpMovementsGrid').getSelectionModel().select(record);

                                                if (record.get('count_payments') > 0) {
                                                    s.removeAll();
                                                    s.load({
                                                        params: {
                                                            linked: 'M',
                                                            item: record.get('id')
                                                        },
                                                        callback: function(records, operation, success) {
                                                            var i = 'agamenti';
                                                            if (!record.get('incoming')) {
                                                                i = 'relievi';
                                                            }

                                                            if (success) {
                                                                Ext.widget('CcpLinkedPaymentsWin').show();
                                                                Ext.getCmp('CcpLinkedPaymentsWin').linked = 'M';
                                                                Ext.getCmp('CcpLinkedPaymentsWin').readOnly = false;
                                                                if (record.get('number')) {
                                                                    Ext.getCmp('CcpLinkedPaymentsWin').setTitle('P' + i + ' abbinati al Movimento ' + record.get('number'));
                                                                } else {
                                                                    Ext.getCmp('CcpLinkedPaymentsWin').setTitle('P' + i + ' abbinati al Movimento');
                                                                }
                                                            } else {
                                                                Ext.Msg.alert('Attenzione', 'Caricamento p' + i + ' abbinati al movimento fallito.');
                                                            }
                                                        }
                                                    });
                                                }
                                            },
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('count_payments') > 0) {
                                                    return 'icon-creditcards';
                                                }
                                            },
                                            getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                var payments = r.get('count_payments'),
                                                    i = ' pagament',
                                                    a = 'i';

                                                if (payments > 0) {
                                                    if (!r.get('incoming')) {
                                                        i = ' preliev';
                                                    }

                                                    if (payments === 1) {
                                                        a = 'o';
                                                    }

                                                    return 'Contiene ' + payments + i + a;
                                                }
                                            }
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                if (r.get('count_additionals') > 0) {
                                                    return 'icon-money_add';
                                                }
                                            },
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();
                                                Ext.getCmp('CcpMovementsGrid').getSelectionModel().select(record);
                                                if (record.get('count_additionals') > 0) {
                                                    var s = Ext.getStore('CcpLinkedAdditionals');
                                                    s.removeAll();
                                                    s.load({
                                                        params: {
                                                            type: 'M',
                                                            item: record.get('id')
                                                        },
                                                        callback: function(records, operation, success) {
                                                            if (success) {
                                                                Ext.widget('CcpLinkedAdditionalsWin').show();
                                                                Ext.getCmp('CcpLinkedAdditionalsWin').setTitle('Addizionali abbinate al Movimento ' + record.get('number'));
                                                            } else {
                                                                Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al movimento fallito.');
                                                            }
                                                        }
                                                    });
                                                }
                                            },
                                            getTip: function(v, meta, r, rowIndex, colIndex, store) {
                                                var additionals = r.get('count_additionals'),
                                                    a = 'e';

                                                if (additionals > 0) {
                                                    if (additionals > 1) {
                                                        a = 'i';
                                                    }

                                                    return 'Comprende ' + additionals + ' addizional' + a;
                                                }
                                            },
                                            altText: 'Note'
                                        },
                                        {
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                var msg = '<div style="display: table">' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Anno Scolastico:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + record.get('school_year') + '</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Importo senza addizionali:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + Ext.util.Format.number(record.get('amount'), '0,000.00') + '&nbsp;&euro;</div>' +
                                                '</div>' +
                                                '<div style="display: table-row">' +
                                                '<div style="display: table-cell"><b>Note:&nbsp;</b></div>' +
                                                '<div style="display: table-cell">' + record.get('note') + '</div>' +
                                                '</div>' +
                                                '</div>';
                                                Ext.Msg.alert('Dettagli movimento di ' + record.get('subject_data'), msg);
                                            },
                                            iconCls: 'icon-information',
                                            tooltip: 'Dettagli'
                                        },
                                        {
                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                return r.get('easy_select') ? 'icon-flag_checked':'';
                                            },
                                            tooltip: 'Marcato per invio ad EASY'
                                        }
                                    ]
                                }
                            ],
                            listeners: {
                                itemcontextmenu: {
                                    fn: me.onCcpMovementsGridItemContextMenu,
                                    scope: me
                                }
                            },
                            dockedItems: [
                                {
                                    xtype: 'pagingtoolbar',
                                    dock: 'bottom',
                                    displayInfo: true,
                                    displayMsg: 'Movimenti {0} - {1} di {2}',
                                    emptyMsg: 'Nessun movimento',
                                    store: 'CcpMovements'
                                }
                            ],
                            selModel: Ext.create('Ext.selection.CheckboxModel', {
                                checkOnly: true
                            })
                        },
                        {
                            xtype: 'menu',
                            permissible: true,
                            id: 'CcpMovementEditMn',
                            itemId: 'CcpMovementEditMn',
                            items: [
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {

                                        var r = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection()[0];
                                        mc2ui.view.CcpPnl.openEditMovementWin(r.get('id'));
                                    },
                                    id: 'contextCcpMovementEdit',
                                    iconCls: 'icon-pencil',
                                    text: 'Modifica'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var r = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection()[0];

                                        Ext.widget('CcpMovementCopyWin').show();
                                        Ext.getCmp('CcpMovementCopyWin').movement = r.getData();

                                        if (r.get('number')) {
                                            Ext.getCmp('CcpMovementCopyWin').setTitle('Copia Movimento ' + r.get('number'));
                                        }
                                    },
                                    hidden: true,
                                    id: 'contextCcpMovementCopy',
                                    iconCls: 'icon-page_copy',
                                    text: 'Copia'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        var record = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection()[0];

                                        Ext.Msg.show({
                                            title: 'Eliminazione Movimento numero ' + record.get('number'),
                                            msg: 'Sei sicuro di voler eliminare questo Movimento?<br />Verranno eliminati anche i pagamenti associati e sarà necessario emettere nuovamente le eventuali fatture che contenevano tali pagamenti se queste erano legate ad ulteriori altri pagamenti.',
                                            buttons: Ext.Msg.YESNO,
                                            fn: function(r){
                                                if (r == 'yes') {
                                                    sMovs = Ext.getStore('CcpMovements');
                                                    sTypes = Ext.getStore('CcpTypes');
                                                    sAdds = Ext.getStore('CcpAdditionals');
                                                    sCats = Ext.getStore('CcpCategories');
                                                    sMovs.remove(record);
                                                    sMovs.sync({
                                                        callback: function () {
                                                            //Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                            sMovs.load();
                                                            sTypes.load();
                                                            sAdds.load();
                                                            sCats.load();
                                                        },
                                                        success: function() {
                                                            Ext.Msg.alert('Successo', 'Movimento eliminato');
                                                            Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                                                        },
                                                        failure: function(res, r) {
                                                            console.log(r, res);
                                                            Ext.Msg.alert('Attenzione', 'Impossibile cancellare movimento. Verificare che il movimento non sia bloccato, legato ad una fattura/distinta' );
                                                        }
                                                    });


                                                }
                                            }
                                        });


                                    },
                                    id: 'contextCcpMovementDelete',
                                    iconCls: 'icon-cancel',
                                    text: 'Elimina'
                                },
                                {
                                    xtype: 'menuseparator'
                                },
                                {
                                    xtype: 'menuitem',
                                    handler: function(item, e) {
                                        Ext.getCmp('CcpMovementPnl').openPaymentWin();
                                    },
                                    id: 'contextCcpMovementPay',
                                    iconCls: 'icon-creditcards',
                                    text: 'Aggiungi un pagamento'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onComboboxSelect11: function(field, newValue, oldValue, eOpts) {
        var subject_school_year = Ext.getCmp('CcpSchoolYear').getValue();


        Ext.getCmp('CcpClassSectionCmb').setValue();
        Ext.getCmp('CcpAddressSectionCmb').setValue();

        Ext.getStore('Classi').load({params: {subject_school_year: subject_school_year}});
        Ext.getStore('Indirizzi').load({params: {subject_school_year: subject_school_year}});
        if(Ext.getCmp('CcpSchoolYear').getValue() === null) {
            Ext.getCmp('CcpMovementFilterClassCnt').disable();
            Ext.getCmp('CcpMovementFilterAddressCnt').disable();
            Ext.getCmp('CcpMovementParentIdCnt').disable();

        } else {
            Ext.getCmp('CcpMovementFilterClassCnt').enable();
            Ext.getCmp('CcpMovementFilterAddressCnt').enable();
            Ext.getCmp('CcpMovementParentIdCnt').enable();
        }

        Ext.getCmp('CcpMovementParentIdCmb').setValue();
        Ext.getStore('CcpParents').load({
            params: {subject_school_year: subject_school_year},
        });

        // Ext.getCmp('CcpMovementsFilterForm').loadByFilter();



    },

    onCcpSchoolYearAfterRender: function(component, eOpts) {
        //if(mc2ui.app.settings.ccp_filter_movement_by_school_year === true) {
        Ext.each(Ext.getStore('McDbs').getRange(), function(v){
            if(v.get('current') === true) {
                Ext.getCmp('CcpSchoolYear').select(v);
            }
        });
        //}
    },

    onCcpAddressSectionCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('CcpClassSectionCmb').setValue();

        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

        Ext.getStore('Classi').clearFilter();
        Ext.getStore('Classi').filter(function(v){
            var ret = false;
            Ext.each(records, function(record) {
                if (v.get('school_address_code') == record.get('code')) ret= true;
            });
            return ret;

        });

    },

    onCcpAddressSectionCmbChange: function(field, newValue, oldValue, eOpts) {
        if(!newValue) Ext.getStore('Classi').clearFilter();

    },

    onComboboxSelect5: function(combo, records, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onComboboxSelect6: function(combo, records, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpMovementSchoolYearCmbSelect: function(combo, records, eOpts) {
        var movement_school_year = Ext.getCmp('CcpMovementSchoolYearCmb').getValue(),
            oldTypeIds = Ext.getCmp('CcpMovementTypeCmb').getValue(),
            newIds;

        Ext.getCmp('CcpCategory').setValue(0);
        var oldCategoryId = Ext.getCmp('CcpCategory').getValue();
        Ext.getStore('CcpCategoriesFilter').load({
            params: {movement_school_year: movement_school_year},
            callback: function(e,r) {
                /*if(!Ext.getStore('CcpCategoriesFilter').getById(oldCategoryId)) {
                            Ext.getCmp('CcpCategory').setValue(0);
                        } else {
                            Ext.getCmp('CcpCategory').setValue(oldCategoryId);
                        }*/
            }
        });


        Ext.getCmp('CcpMovementTypeCmb').setValue();
        Ext.getStore('CcpTypesFilter').load({
            params: {
                school_year: movement_school_year,
                movement_school_year: movement_school_year
            },
            callback: function(e,r) {
                /*Ext.each(oldTypeIds, function(v){
                    if(Ext.getStore('CcpTypesFilter').getById(v)) {
                        newIds.push(v);
                    }
                });

                if(newIds.length === 0) {
                    Ext.getCmp('CcpMovementTypeCmb').setValue(0);
                } else {
                    Ext.getCmp('CcpMovementTypeCmb').setValue(newIds);
                }*/
            }

        });



        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();


    },

    onCcpCategorySelect: function(combo, records, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onComboboxSelect: function(combo, records, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpMovementTypeCmbChange: function(field, newValue, oldValue, eOpts) {

        if(newValue.length === 0) Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onComboboxSelect4: function(combo, records, eOpts) {
        var r = records[0];


        Ext.getCmp('CcpSubjectType').setValue();
        Ext.getCmp('CcpSubjectId').setValue();
        Ext.getCmp('CcpSubjectSeat').setValue();
        Ext.getCmp('CcpTypeId').setValue();

        Ext.getCmp('CcpSubjectType').setValue(r.get('type'));
        if(r.get('type') == 'S'){
            Ext.getCmp('CcpSubjectId').setValue(r.get('id'));
            Ext.getCmp('CcpSubjectSeat').setValue(r.get('seat_id'));
        } else if(r.get('type') == 'E'){
            Ext.getCmp('CcpSubjectId').setValue(r.get('id'));
        }
        if(r.get('type') == 'T') Ext.getCmp('CcpTypeId').setValue(r.get('id'));


        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

    },

    onCcpGenericSearchCmbBlur: function(component, e, eOpts) {
        // Non c'è stata selezione
        if(!parseInt(Ext.getCmp('CcpGenericSearchCmb').getValue())) {
            Ext.getCmp('CcpSubjectId').setValue();
            Ext.getCmp('CcpSubjectSeat').setValue();
            Ext.getCmp('CcpSubjectType').setValue();
            Ext.getCmp('CcpTypeId').setValue();

            if(Ext.getCmp('CcpGenericSearchCmb').getValue())  Ext.getCmp('CcpSubjectType').setValue('O');
            Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
        }

    },

    onCcpMovementPaymentMethodCmbChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onComboboxSelect8: function(combo, records, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

    },

    onComboboxSelect3: function(combo, records, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onComboboxSelect2: function(combo, records, eOpts) {
        if(records[0].get('id') === 'creation') {
            Ext.getCmp('CcpCreationDateStart').name = 'creation_date_start';
            Ext.getCmp('CcpCreationDateEnd').name = 'creation_date_end';
        } else if(records[0].get('id') === 'payment') {
            Ext.getCmp('CcpCreationDateStart').name = 'payment_date_start';
            Ext.getCmp('CcpCreationDateEnd').name = 'payment_date_end';
        }else {
            Ext.getCmp('CcpCreationDateStart').name = 'expiration_date_start';
            Ext.getCmp('CcpCreationDateEnd').name = 'expiration_date_end';
        }

        Ext.getCmp('CcpMovementsFilterForm').loadByFilter(false);
    },

    onDatefieldChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter(false);
    },

    onCcpCreationDateStartAfterRender: function(component, eOpts) {
        if(mc2ui.app.settings.ccp_filter_movement_by_school_year === true) {
            component.setValue();
        }
    },

    onDatefieldChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter(false);
    },

    onTextfieldChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onTextfieldChange12: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpHasRemainingChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpHasAdditionalsChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpHasPaymentsChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpHasPaymentsChange3: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpHasPaymentsChange2: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCcpHasPaymentsChange21: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange2: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange3: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange4: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange5: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange6: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onCheckboxfieldChange7: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onTextfieldChange11: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onTextfieldChange111: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
    },

    onViewItemDblClick: function(dataview, record, item, index, e, eOpts) {
        Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();
        Ext.getCmp('CcpMovementsGrid').getSelectionModel().select(index, true);

        Ext.getCmp('contextCcpMovementEdit').handler();
    },

    onCcpMovementsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0],
            newY = e.xy[1];
        Ext.getCmp('CcpMovementEditMn').showAt([newX,newY]);

        Ext.getCmp('CcpMovementsGrid').getSelectionModel().select(index, true);



        if (record.get('locked')) {
            //Ext.getCmp('contextCcpMovementEdit').setDisabled(true);
        } else {
            Ext.getCmp('contextCcpMovementEdit').setDisabled(false);
        }

        if( Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection().length>1) {
            Ext.getCmp('contextCcpMovementEdit').setDisabled(true);
            Ext.getCmp('contextCcpMovementDelete').setDisabled(true);
        } else{
            Ext.getCmp('contextCcpMovementDelete').setDisabled(false);
            Ext.getCmp('contextCcpMovementEdit').setDisabled(false);
        }

        if (record.get('subject_type') !== 'S') {
            Ext.getCmp('contextCcpMovementCopy').setDisabled(true);
        } else {
            Ext.getCmp('contextCcpMovementCopy').setDisabled(false);
        }

        if (record.get('incoming')) {
            Ext.getCmp('contextCcpMovementPay').setText('Aggiungi un pagamento');
        } else {
            Ext.getCmp('contextCcpMovementPay').setText('Effettua un prelievo');
        }
    },

    onCcpMovementsSumChange4: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpPaymentsSumChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpRemainingsSumChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpMovementsSumChange31: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpPaymentsSumChange3: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpRemainingsSumChange3: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpTotalCreditToPayChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpTotalDebitToPayChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpTotalToPayChange: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);
    },

    onCcpMovementsSumChange21: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpPaymentsSumChange2: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpRemainingsSumChange2: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpMovementsSumChange11: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpPaymentsSumChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    onCcpRemainingsSumChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpTotaleToolBar').setColors(field, newValue);

    },

    addMovementToInvoice: function(movement, invoice) {
        Ext.Ajax.request({
            url: '/mc2-api/ccp/invoice/add_movements',
            method: 'POST',
            params: Ext.encode({
                "invoice_id": parseInt(invoice),
                "movements": movement,
                "multiple_expiration_dates": 1
            }),
            success:function(res){
                var r = Ext.decode(res.responseText);
                if(r.success === true) {
                    var studentIdMc = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0].get('db_id');
                    Ext.getCmp('CcpStudentsGrd').filterByStudent(studentIdMc);
                } else {
                    Ext.Msg.alert('ERRORE', r.message);
                }
            }
        });

    },

    openPaymentWin: function(from) {
        from = !from ? 'movement' : from;

        if (from=='payment'){
            var r = Ext.getCmp('CcpLinkedPaymentsGrid').getSelectionModel().getSelection()[0],
                m = Ext.getCmp('CcpMovementsGrid').getSelectionModel().getSelection()[0],
                s = Ext.getStore('CcpLinkedAdditionalsForm'),
                p = Ext.getStore('CcpPayers'),
                a = 'Pagamento';

            s.removeAll();
            s.load({
                params: {
                    type: 'P',
                    item: r.get('id')
                },
                callback: function(records, operation, success) {
                    if (success) {
                        Ext.widget('CcpPaymentEditWin').show();
                        if(r.get('ccp_credit')>0) {
                            Ext.getCmp('CcpPaymentPaymentMethodId').hide();
                            Ext.getCmp('CcpPayByCreditTxt').show();
                        }
                        Ext.getCmp('CcpStudentPaymentMassive').hide();

                        if (!r.get('incoming')) {
                            a = 'Prelievo';

                            if(r.get('covered_movement_id')) {
                                Ext.getCmp('CppCoverMovementId').show();
                                Ext.getCmp('CppCoverMovementId').disable();
                                Ext.getStore('CcpMovementsCover').load({
                                    params: {
                                        subject_type: 'S',
                                        subject_id: r.get('subject_id'),
                                        credit: 1,
                                        debit: 0,
                                        payed:1,
                                        not_payed: 1
                                    }
                                });
                            }

                        }


                        Ext.getCmp('CcpPaymentEditWin').setTitle(a + ' abbinato al Movimento ' + r.get('movement_number'));

                        Ext.getCmp('CcpPaymentForm').getForm().loadRecord(r);

                        p.removeAll();
                        if (r.get('subject_type') === 'O') {
                            Ext.getCmp('CcpPaymentPayer').setDisabled(true);
                        } else {
                            Ext.getCmp('CcpPaymentPayer').setDisabled(false);
                            p.load({
                                params: {
                                    type: r.get('subject_type'),
                                    id: r.get('subject_id'),
                                    subject_school_year: m.get('subject_school_year'),
                                    all_as_payer:true
                                },
                                callback: function(records, operation, success) {

                                    Ext.each(p.getRange(), function(v) {

                                        if(v.get('db_id')== r.get('payer_id')) {
                                            Ext.getCmp('CcpPaymentPayer').setValue(v);
                                            Ext.getCmp('CcpPaymentForm').getForm().loadRecord(r);
                                        }
                                    });


                                    //Ext.getCmp('CcpPaymentPayer').select(r.get('subject_type') + '_' + r.get('subject_id'));
                                }
                            });
                        }

                        //Ext.getCmp('CcpPaymentAmount').setMaxValue(Math.round((m.get('remaining') + r.get('amount'))*100, 2)/100);
                    } else {
                        Ext.Msg.alert('Attenzione', 'Caricamento addizionali abbinate al pagamento fallito.');
                    }
                }
            });
        } else {
            var gridId = from == 'movement_student' ? 'CcpMovementsStudentGrid' : 'CcpMovementsGrid',
                r = Ext.getCmp(gridId).getSelectionModel().getSelection()[0],
                p = Ext.getStore('CcpPayers'),
                pd = Ext.getStore('CcpPaymentDestinations'),
                a = 'Pagamento',
                amount,
                creditRemain,
                multipleMovements = [];



            /*pd.filterBy(function(rec, id) {
                if (rec.get('type') == 'P')
                    return true;
                else
                    return false;
            });*/
            pd_ccp = pd.getRange()[0];
            pd.clearFilter();

            pd_ccp= pd_ccp ? pd_ccp: pd.getRange()[0];

            Ext.widget('CcpPaymentEditWin').show();
            if(r.get('ccp_credit')>0) {
                Ext.getCmp('CcpPaymentPaymentMethodId').hide();
                Ext.getCmp('CcpPayByCreditTxt').show();
            }
            Ext.getCmp('CcpPaymentPaymentDestinationId').setValue(pd_ccp.get('id'));

            if(from == 'movement_student' && r.get('incoming') === false) {
                Ext.getCmp('CcpPaymentPaymentMethodId').setValue(20);
                Ext.getCmp('CppCoverMovementId').show();

                var params = {
                    subject_type: 'S',
                    subject_id: r.get('subject_id'),
                    credit: 1,
                    debit: 0,
                    payed:0,
                    not_payed: 1
                };

                if(mc2ui.app.settings.invoiceEnabled) {
                    params.invoiced = 1;
                    params.not_invoiced = 0;
                }
                Ext.getStore('CcpMovementsCover').load({
                    params: params
                });
            }

            setTimeout(function() {
                // amount = r.get('remaining');
                amount = 0;
                Ext.each(Ext.getCmp(gridId).getSelectionModel().getSelection(), function(mov){
                    amount += mov.get('remaining');
                });


                creditRemain = Ext.getCmp('CcpPaymentCreditRemain').getValue();
                if (from == 'movement_student') {
                    if (creditRemain < amount && Ext.getCmp('CcpPaymentCreditId').getValue()) amount = creditRemain;
                }
                Ext.getCmp('CcpPaymentAmount').setValue(amount);
                //Ext.getCmp('CcpPaymentAmount').setMaxValue(Math.round(r.get('remaining')*100, 2)/100);
            }, 500);

            if (!r.get('incoming')) {
                a = 'Prelievo';
            }

            if (r.get('number')) {
                Ext.getCmp('CcpPaymentEditWin').setTitle(a + ' abbinato al Movimento ' + r.get('number'));
            } else {
                Ext.getCmp('CcpPaymentEditWin').setTitle(a + ' abbinato al Movimento');
            }

            Ext.getCmp('CcpPaymentMovementId').setValue(r.get('id'));



            if(Ext.getCmp(gridId).getSelectionModel().getSelection().length > 1) {
                Ext.each(Ext.getCmp(gridId).getSelectionModel().getSelection(), function(v) {
                    multipleMovements.push(v.get('id'));
                });
                Ext.getCmp('CcpPaymentMovementId').setValue(multipleMovements.join('_'));
            } else {
                Ext.getCmp('CcpPaymentGroupPaymentCh').hide();
                Ext.getCmp('CcpPaymentDefPayerCh').hide();
            }

            Ext.getCmp('CcpPaymentOperationDate').setValue(new Date());
            Ext.getCmp('CcpPaymentAccountableDate').setValue(new Date());

            p.removeAll();
            if (r.get('subject_type') === 'O') {
                Ext.getCmp('CcpPaymentPayer').setDisabled(true);
                Ext.getCmp('CcpPaymentPayerType').select('O');
                Ext.getCmp('CcpPaymentPayerName').setValue(r.get('subject_data'));
            } else {
                var ids = [];
                Ext.each(Ext.getCmp(gridId).getSelectionModel().getSelection(), function(mov){
                    if(!Ext.Array.contains(ids, mov.get('subject_id'))) ids.push(mov.get('subject_id'));
                });

                Ext.getCmp('CcpPaymentPayer').setDisabled(false);
                p.load({
                    params: {
                        type: r.get('subject_type'),
                        id: Ext.encode(ids),
                        subject_school_year: r.get('subject_school_year'),
                        all_as_payer:true
                    },
                    callback: function(){
                        if(ids.length===1) {
                            Ext.each(p.getRange(), function(v) {
                                if(v.get('pagante')=== "t") Ext.getCmp('CcpPaymentPayer').setValue(v);
                            });
                        }
                    }
                });
            }

            if(multipleMovements.length > 1){
                Ext.getCmp('CcpStudentPaymentSave').hide();
                Ext.getCmp('CcpStudentPaymentMassive').show();
            } else {
                Ext.getCmp('CcpStudentPaymentSave').show();
                Ext.getCmp('CcpStudentPaymentMassive').hide();
            }


        }

    }

});