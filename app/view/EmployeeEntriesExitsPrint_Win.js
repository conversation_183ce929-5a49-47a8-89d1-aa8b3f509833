/*
 * File: app/view/EmployeeEntriesExitsPrint_Win.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeEntriesExitsPrint_Win', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeEntriesExitsPrint_Win',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 468,
    id: 'EmployeeEntriesExitsPrint_Win',
    itemId: 'EmployeeEntriesExitsPrint_Win',
    minHeight: 400,
    width: 354,
    title: 'Stampa Entrate / Uscite',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeEntriesExitsPrint_Container',
                    itemId: 'EmployeeEntriesExitsPrint_Container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            border: false,
                            id: 'EmployeeEntriesExitsPrint_Form',
                            itemId: 'EmployeeEntriesExitsPrint_Form',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-box-layout-ct'
                            ],
                            bodyPadding: 10,
                            header: false,
                            title: 'My Form',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'datefield',
                                    endDateField: 'EmployeeEntriesExitsPrint_FilterEnd',
                                    id: 'EmployeeEntriesExitsPrint_FilterStart',
                                    itemId: 'EmployeeEntriesExitsPrint_FilterStart',
                                    width: 200,
                                    fieldLabel: 'Dal',
                                    labelAlign: 'right',
                                    inputId: 'start',
                                    allowBlank: false,
                                    vtype: 'daterange',
                                    editable: false,
                                    altFormats: 'm/d/Y',
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'd-m-Y'
                                },
                                {
                                    xtype: 'datefield',
                                    startDateField: 'EmployeeEntriesExitsPrint_FilterStart',
                                    id: 'EmployeeEntriesExitsPrint_FilterEnd',
                                    itemId: 'EmployeeEntriesExitsPrint_FilterEnd',
                                    width: 200,
                                    fieldLabel: 'al',
                                    labelAlign: 'right',
                                    inputId: 'end',
                                    allowBlank: false,
                                    vtype: 'daterange',
                                    editable: false,
                                    altFormats: 'm/d/Y',
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'd-m-Y'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    id: 'EmployeeEntriesExitsPrint_ShowNotes',
                                    itemId: 'EmployeeEntriesExitsPrint_ShowNotes',
                                    width: 200,
                                    name: 'notes',
                                    boxLabel: 'Stampare le note',
                                    uncheckedValue: 'off'
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: '5 0',
                                    layout: {
                                        type: 'hbox',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            disabled: true,
                                            id: 'EmployeeEntriesExitsPrintBtnPrint',
                                            itemId: 'EmployeeEntriesExitsPrintBtnPrint',
                                            iconCls: 'icon-printer',
                                            text: 'Stampa',
                                            listeners: {
                                                click: {
                                                    fn: me.onButtonClick,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            border: false,
                            height: 250,
                            id: 'EmployeeEntriesExitsPrintGrid',
                            itemId: 'EmployeeEntriesExitsPrintGrid',
                            width: 400,
                            autoScroll: true,
                            title: 'Personale',
                            titleAlign: 'center',
                            emptyText: 'Nessun Personale',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            hideHeaders: true,
                            sortableColumns: false,
                            store: 'EmployeesTreeActive',
                            displayField: 'denomination',
                            useArrows: true,
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'denomination',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onEmployeeEntriesExitsPrintGridCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeeEntriesExitsPrint_WinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        var pnl = Ext.getCmp('EmployeeEntriesExitsPrint_Form'),
            form = pnl.getForm();

        if (form.isValid()) {
            Ext.getCmp('EmployeeEntriesExitsPrint_Container').setLoading();

            // Take period (start, end)
            var values = form.getValues();

            // Take the merge id to print and put it in a JSON encoded array
            var sel = Ext.getCmp('EmployeeEntriesExitsPrintGrid').getChecked(),
                mergeSelect = new Array();

            Ext.each(sel, function(a) {
                if (a.data.leaf === true) {
                    mergeSelect = mergeSelect.concat(a.raw.employee_id);
                }
            });
            var mergeSelectJSON = Ext.JSON.encode(mergeSelect);

            Ext.Ajax.request({
                url: '/mc2-api/core/print',
                params:{
                    newSpool: 0,
                    print: 'EntriesExits',
                    namespace: 'Personnel',
                    type: 'PDF',
                    printClass: 'PrintPDFEntriesExits',
                    mime: 'application/pdf',
                    start: values.start,
                    end: values.end,
                    notes: values.notes,
                    employees: mergeSelectJSON
                },
                success: function(response, opts) {
                    Ext.getCmp('EmployeeEntriesExitsPrint_Container').setLoading(false);
                    var res = Ext.decode(response.responseText);
                    mc2ui.app.showNotifyPrint(res);
                }
            });
        }
    },

    onEmployeeEntriesExitsPrintGridCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeEntriesExitsPrint_Win').enablePrint();
    },

    onEmployeeEntriesExitsPrint_WinActivate: function(window, eOpts) {
        var date = Ext.Date.format(new Date(),'d/m/Y');
        Ext.getCmp('EmployeeEntriesExitsPrint_FilterStart').setValue(date);

        var t = Ext.getCmp('EmployeeEntriesExitsPrintGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        Ext.getCmp('EmployeeEntriesExitsPrint_Win').enablePrint();
    },

    enablePrint: function() {
        var employees = Ext.getCmp('EmployeeEntriesExitsPrintGrid').getChecked();

        if (employees.length > 0) {
            Ext.getCmp('EmployeeEntriesExitsPrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeEntriesExitsPrintBtnPrint').setDisabled(true);
        }
    }

});