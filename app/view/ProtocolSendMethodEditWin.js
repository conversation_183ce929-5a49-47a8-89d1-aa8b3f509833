/*
 * File: app/view/ProtocolSendMethodEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolSendMethodEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolSendMethodEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Text',
        'Ext.form.field.Hidden'
    ],

    height: 120,
    id: 'ProtocolSendMethodEditWin',
    itemId: 'ProtocolSendMethodEditWin',
    width: 400,
    resizable: false,
    title: 'Mezzo di Invio',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'ProtocolSendMethodEditForm',
                    itemId: 'ProtocolSendMethodEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'center'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('ProtocolSendMethods'),
                                            form = Ext.getCmp('ProtocolSendMethodEditForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvato';

                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornato';
                                            record = store.getById(parseInt(values.id));
                                            record.set('title', values.title);
                                        } else {
                                            store.add({title: values.title});
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('ProtocolSendMethodEditWin').close();
                                                Ext.Msg.alert('Successo', 'Mezzo di Invio ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Mezzo di Invio NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            name: 'title',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            name: 'id'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});