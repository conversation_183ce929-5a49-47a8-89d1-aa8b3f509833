/*
 * File: app/view/CcpByCCPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpByCCPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpByCCPrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Date',
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel'
    ],

    height: 347,
    id: 'CcpByCCPrintWin',
    itemId: 'CcpByCCPrintWin',
    width: 448,
    resizable: false,
    title: 'Stampa registro Conto corrente',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch',
        pack: 'center'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'CcpByCCPrintForm',
                    itemId: 'CcpByCCPrintForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    layout: {
                        type: 'vbox',
                        align: 'center',
                        pack: 'center'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var dates = Ext.getCmp('CcpByCCPrintForm').getForm().getValues(),
                                            ccs = Ext.getCmp('CcpByCCPrintGrid').getSelectionModel().getSelection(),
                                            rec = {},
                                            list = [];

                                        Ext.each(ccs, function(cc){
                                            list.push(parseInt(cc.get('id')));
                                        });

                                        rec.newSpool = 0;
                                        rec.print = 'ByCC';
                                        rec.namespace = 'CCP';
                                        rec.type = 'PDF';
                                        rec.printClass = 'Ccp\\PrintPDFByCC';
                                        rec.mime = 'application/pdf';
                                        rec.data_estratto_conto_start = dates.start;
                                        rec.data_estratto_conto_end = dates.end;
                                        rec.ccs = Ext.JSON.encode(list);

                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/print',
                                            params: rec,
                                            success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                            }
                                        });
                                    },
                                    disabled: true,
                                    id: 'CcpByCCPrintBtnPrint',
                                    itemId: 'CcpByCCPrintBtnPrint',
                                    iconCls: 'icon-printer',
                                    text: 'Stampa'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'datefield',
                            endDateField: 'CcpByCCPrintDateEnd',
                            id: 'CcpByCCPrintDateStart',
                            itemId: 'CcpByCCPrintDateStart',
                            width: 200,
                            fieldLabel: 'Dal',
                            name: 'start',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            vtype: 'daterange',
                            editable: false,
                            format: 'd/m/Y',
                            startDay: 1,
                            submitFormat: 'c'
                        },
                        {
                            xtype: 'datefield',
                            startDateField: 'CcpByCCPrintDateStart',
                            id: 'CcpByCCPrintDateEnd',
                            itemId: 'CcpByCCPrintDateEnd',
                            width: 200,
                            fieldLabel: 'Al',
                            name: 'end',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            vtype: 'daterange',
                            editable: false,
                            format: 'd/m/Y',
                            startDay: 1,
                            submitFormat: 'c'
                        }
                    ]
                },
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'CcpByCCPrintGrid',
                    itemId: 'CcpByCCPrintGrid',
                    title: 'Conti Corrente',
                    titleAlign: 'center',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'CoreBankAccounts',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'denomination',
                            hideable: false,
                            text: 'Denominazione',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 150,
                            resizable: false,
                            dataIndex: 'iban',
                            hideable: false,
                            text: 'Coordinate'
                        }
                    ],
                    listeners: {
                        selectionchange: {
                            fn: me.onCcpByCCPrintGridSelectionChange,
                            scope: me
                        }
                    },
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true,
                        showHeaderCheckbox: true
                    })
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpByCCPrintGridSelectionChange: function(model, selected, eOpts) {
        Ext.getCmp('CcpByCCPrintWin').enablePrint();
    },

    enablePrint: function() {
        var ccs = Ext.getCmp('CcpByCCPrintGrid').getSelectionModel().getSelection();

        if (ccs.length > 0) {
            Ext.getCmp('CcpByCCPrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('CcpByCCPrintBtnPrint').setDisabled(true);
        }
    }

});