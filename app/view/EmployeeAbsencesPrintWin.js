/*
 * File: app/view/EmployeeAbsencesPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeAbsencesPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeAbsencesPrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.selection.CheckboxModel'
    ],

    height: 468,
    id: 'EmployeeAbsencesPrintWin',
    itemId: 'EmployeeAbsencesPrintWin',
    minHeight: 400,
    minWidth: 300,
    width: 600,
    title: 'Stampa riepilogo Assenze',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeAbsencesPrintWinContainer',
                    itemId: 'EmployeeAbsencesPrintWinContainer',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            border: false,
                            id: 'EmployeeAbsencesPrintDataFrm',
                            itemId: 'EmployeeAbsencesPrintDataFrm',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-box-layout-ct'
                            ],
                            bodyPadding: 10,
                            header: false,
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'datefield',
                                    endDateField: 'EmployeeAbsencesPrintFilterEnd',
                                    id: 'EmployeeAbsencesPrintFilterStart',
                                    itemId: 'EmployeeAbsencesPrintFilterStart',
                                    width: 200,
                                    fieldLabel: 'Dal',
                                    labelAlign: 'right',
                                    inputId: 'abs_start',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    vtype: 'daterange',
                                    editable: false,
                                    altFormats: 'm/d/Y',
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'd-m-Y'
                                },
                                {
                                    xtype: 'datefield',
                                    startDateField: 'EmployeeAbsencesPrintFilterStart',
                                    id: 'EmployeeAbsencesPrintFilterEnd',
                                    itemId: 'EmployeeAbsencesPrintFilterEnd',
                                    width: 200,
                                    fieldLabel: 'al',
                                    labelAlign: 'right',
                                    inputId: 'abs_end',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    vtype: 'daterange',
                                    editable: false,
                                    altFormats: 'm/d/Y',
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'd-m-Y'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    id: 'EmployeeAbsencesPrintFilterIgnoreEmpty',
                                    itemId: 'EmployeeAbsencesPrintFilterIgnoreEmpty',
                                    width: 200,
                                    inputId: 'ignore_empty',
                                    boxLabel: 'Ignora personale senza assenze',
                                    uncheckedValue: 'off'
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: '5 0',
                                    layout: {
                                        type: 'hbox',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            disabled: true,
                                            id: 'EmployeeAbsencesPrintBtnPrint',
                                            itemId: 'EmployeeAbsencesPrintBtnPrint',
                                            iconCls: 'icon-printer',
                                            text: 'Stampa',
                                            listeners: {
                                                click: {
                                                    fn: me.onButtonClick,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'treepanel',
                                    flex: 1,
                                    height: 250,
                                    id: 'EmployeeAbsencesPrintEmployeesGrid',
                                    itemId: 'EmployeeAbsencesPrintEmployeesGrid',
                                    width: 400,
                                    autoScroll: true,
                                    title: 'Personale',
                                    titleAlign: 'center',
                                    emptyText: 'Nessun Personale',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    hideHeaders: true,
                                    sortableColumns: false,
                                    store: 'EmployeesTreeActive',
                                    displayField: 'denomination',
                                    useArrows: true,
                                    viewConfig: {

                                    },
                                    columns: [
                                        {
                                            xtype: 'treecolumn',
                                            resizable: false,
                                            dataIndex: 'denomination',
                                            text: '',
                                            flex: 1
                                        }
                                    ],
                                    listeners: {
                                        checkchange: {
                                            fn: me.onEmployeeAbsencesPrintEmployeesGridCheckChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'EmployeeAbsencesPrintGrid',
                                    itemId: 'EmployeeAbsencesPrintGrid',
                                    title: 'Tipi assenza',
                                    hideHeaders: true,
                                    store: 'AbsenceKindGrouped',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'description_code',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'stack_text',
                                            groupable: true,
                                            flex: 1
                                        }
                                    ],
                                    features: [
                                        {
                                            ftype: 'grouping',
                                            groupByText: 'stack_text',
                                            groupHeaderTpl: [
                                                '{name}'
                                            ]
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                                        listeners: {
                                            selectionchange: {
                                                fn: me.onCheckboxModelSelectionChange,
                                                scope: me
                                            }
                                        }
                                    }),
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('EmployeeAbsencesPrintGrid').getSelectionModel().selectAll();
                                                    },
                                                    text: 'Seleziona tutti'
                                                },
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        Ext.getCmp('EmployeeAbsencesPrintGrid').getSelectionModel().deselectAll();
                                                    },
                                                    text: 'Deseleziona tutti'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeeAbsencesPrintWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        var pnl = Ext.getCmp('EmployeeAbsencesPrintDataFrm'),
            form = pnl.getForm();

        if (form.isValid()) {
            Ext.getCmp('EmployeeAbsencesPrintWinContainer').setLoading();

            // Take period (start, end)
            var values = form.getValues();

            // Take all people to print and put it in JSON encode array of employee_id
            var sel = Ext.getCmp('EmployeeAbsencesPrintEmployeesGrid').getChecked(),
                employeesSelect = new Array();

            Ext.each(sel, function(a) {
                if (a.data.leaf === true) {
                    employeesSelect = employeesSelect.concat(a.raw.employee_id);
                }
            });
            var employeesSelectJSON = Ext.JSON.encode(employeesSelect);

            // Take the merge id to print and put it in a JSON encoded array
            var sel = Ext.getCmp('EmployeeAbsencesPrintGrid').getSelectionModel().getSelection(),
                mergeSelect = new Array();

            Ext.each(sel, function(a) {
                mergeSelect = mergeSelect.concat(a.raw.code);
            });
            var mergeSelectJSON = Ext.JSON.encode(mergeSelect);

            Ext.Ajax.request({
                url: '/mc2-api/core/print',
                params:{
                    newSpool: 0,
                    print: 'Absences',
                    namespace: 'Personnel',
                    type: 'PDF',
                    printClass: 'PrintPDFAbsences',
                    mime: 'application/pdf',
                    start: values.abs_start,
                    end: values.abs_end,
                    ignore: values.ignore_empty,
                    employees: employeesSelectJSON,
                    kinds: mergeSelectJSON
                },
                success: function(response, opts) {
                    Ext.getCmp('EmployeeAbsencesPrintWinContainer').setLoading(false);
                    var res = Ext.decode(response.responseText);
                    mc2ui.app.showNotifyPrint(res);
                }
            });
        }
    },

    onEmployeeAbsencesPrintEmployeesGridCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeAbsencesPrintWin').enablePrint();
    },

    onCheckboxModelSelectionChange: function(model, selected, eOpts) {
        Ext.getCmp('EmployeeAbsencesPrintWin').enablePrint();
    },

    onEmployeeAbsencesPrintWinActivate: function(window, eOpts) {
        var date = Ext.Date.format(new Date(),'d/m/Y');
        Ext.getCmp('EmployeeAbsencesPrintFilterStart').setValue(date);

        /*
        var t = Ext.getCmp('EmployeeAbsencesPrintMergeGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        var t = Ext.getCmp('EmployeeAbsencesPrintEmployeesGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });
        */

        Ext.getCmp('EmployeeAbsencesPrintWin').enablePrint();
        Ext.getStore('AbsenceKindGrouped').load({
            params: {
                linked: true
            }
        });
    },

    enablePrint: function() {
        var kinds = Ext.getCmp('EmployeeAbsencesPrintGrid').getSelectionModel().getSelection(),
            employees = Ext.getCmp('EmployeeAbsencesPrintEmployeesGrid').getChecked();

        if (kinds.length > 0 && employees.length > 0) {
            Ext.getCmp('EmployeeAbsencesPrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeAbsencesPrintBtnPrint').setDisabled(true);
        }
    }

});