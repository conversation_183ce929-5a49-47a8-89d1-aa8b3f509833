/*
 * File: app/view/EmployeeProjectsHourTypeEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeProjectsHourTypeEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeProjectsHourTypeEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Hidden',
        'Ext.form.field.Number',
        'Ext.form.FieldSet',
        'Ext.form.Label',
        'Ext.form.field.TextArea'
    ],

    id: 'EmployeeProjectsHourTypeEditWin',
    width: 450,
    resizable: false,
    title: 'Tipo di Ora',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'EmployeeProjectsHourTypeEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var data = Ext.getCmp('EmployeeProjectsHourTypeEditForm').getForm().getValues(),
                                            sHourTypes = Ext.getStore('PersonnelHourTypes'),
                                            grid = Ext.getCmp('EmployeeProjectsManagementHourTypesGrid'),
                                            a = 'aggiunto',
                                            newHourType = data.id === "";

                                        // Saves the data
                                        if (!newHourType) {
                                            a = 'aggiornato';
                                            var r = grid.getSelectionModel().getSelection()[0];
                                            r.set('name', data.name);
                                            r.set('description', data.description);
                                            r.set('price', data.price);
                                            r.set('inpdap_perc', data.inpdap_perc);
                                            r.set('inps_perc', data.inps_perc);
                                            r.set('irap_perc', data.irap_perc);
                                        } else {
                                            sHourTypes.add(data);
                                        }

                                        // Syncs the record
                                        sHourTypes.sync({
                                            callback: function() {
                                                sHourTypes.load();
                                            },
                                            success: function(form, action) {
                                                grid.getSelectionModel().deselectAll();
                                                Ext.getCmp('EmployeeProjectsHourTypeEditWin').close();
                                                Ext.Msg.alert('Successo', 'Tipo di Ora ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Tipo di Ora NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'hiddenfield',
                            fieldLabel: 'Label',
                            name: 'id'
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Denominazione',
                            labelAlign: 'right',
                            name: 'name',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    layout: {
                                        type: 'hbox',
                                        align: 'middle',
                                        pack: 'end'
                                    },
                                    items: [
                                        {
                                            xtype: 'numberfield',
                                            width: '',
                                            fieldLabel: 'Retribuzione',
                                            labelAlign: 'right',
                                            name: 'price',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            hideTrigger: true,
                                            allowExponential: false,
                                            minValue: 0,
                                            step: 0.1
                                        }
                                    ]
                                },
                                {
                                    xtype: 'fieldset',
                                    flex: 1,
                                    title: 'Percentuali',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'middle',
                                                pack: 'end',
                                                padding: '5 0 5 0'
                                            },
                                            items: [
                                                {
                                                    xtype: 'numberfield',
                                                    width: 120,
                                                    fieldLabel: 'INPDAP',
                                                    labelAlign: 'right',
                                                    labelWidth: 60,
                                                    name: 'inpdap_perc',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    hideTrigger: true,
                                                    allowExponential: false,
                                                    maxValue: 100,
                                                    minValue: 0,
                                                    step: 0.1
                                                },
                                                {
                                                    xtype: 'label',
                                                    padding: '0 0 0 5',
                                                    text: '%'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'middle',
                                                pack: 'end',
                                                padding: '5 0 5 0'
                                            },
                                            items: [
                                                {
                                                    xtype: 'numberfield',
                                                    width: 120,
                                                    fieldLabel: 'INPS',
                                                    labelAlign: 'right',
                                                    labelWidth: 60,
                                                    name: 'inps_perc',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    hideTrigger: true,
                                                    allowExponential: false,
                                                    maxValue: 100,
                                                    minValue: 0,
                                                    step: 0.1
                                                },
                                                {
                                                    xtype: 'label',
                                                    padding: '0 0 0 5',
                                                    text: '%'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'middle',
                                                pack: 'end',
                                                padding: '5 0 5 0'
                                            },
                                            items: [
                                                {
                                                    xtype: 'numberfield',
                                                    width: 120,
                                                    fieldLabel: 'IRAP',
                                                    labelAlign: 'right',
                                                    labelWidth: 60,
                                                    name: 'irap_perc',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    hideTrigger: true,
                                                    allowExponential: false,
                                                    maxValue: 100,
                                                    minValue: 0,
                                                    step: 0.1
                                                },
                                                {
                                                    xtype: 'label',
                                                    padding: '0 0 0 5',
                                                    text: '%'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'textareafield',
                            fieldLabel: 'Descrizione',
                            labelAlign: 'right',
                            name: 'description'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});