/*
 * File: app/view/ProtocolTypesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolTypesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolTypesWin',

    requires: [
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.plugin.TreeViewDragDrop',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Separator'
    ],

    height: 600,
    id: 'ProtocolTypesWin',
    itemId: 'ProtocolTypesWin',
    width: 600,
    title: 'Titolario',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'treepanel',
                    permissible: true,
                    flex: 1,
                    border: false,
                    id: 'ProtocolTypesTreeGrid',
                    itemId: 'ProtocolTypesTreeGrid',
                    header: false,
                    emptyText: 'Nessuna voce di titolario caricata',
                    hideHeaders: true,
                    store: 'ProtocolTypesTree',
                    displayField: 'denomination',
                    rootVisible: false,
                    useArrows: true,
                    viewConfig: {
                        plugins: [
                            Ext.create('Ext.tree.plugin.TreeViewDragDrop', {
                                appendOnly: true,
                                displayField: 'full_denomination',
                                dragText: '{0} Voci selezionate'
                            })
                        ],
                        listeners: {
                            beforedrop: {
                                fn: me.onViewBeforeDrop,
                                scope: me
                            },
                            drop: {
                                fn: me.onViewDrop,
                                scope: me
                            }
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ProtocolTypeEditWin').show();

                                        var forbiddenCodes = [],
                                            codes = Ext.getStore('ProtocolTypes').getRange();

                                        codes.forEach(function(c) {
                                            if (!c.get('parent_type_id')) {
                                                forbiddenCodes.push(c.get('code'));
                                            }
                                        });

                                        Ext.getCmp('ProtocolTypeEditCode').forbiddenCodes = forbiddenCodes;
                                    },
                                    id: 'ProtocolTypeNewBtn',
                                    itemId: 'ProtocolTypeNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuova voce'
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onProtocolTypesTreeItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'ProtocolTypeEditMn',
                    itemId: 'ProtocolTypeEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                Ext.widget('ProtocolTypeEditWin').show();

                                var record = Ext.getCmp('ProtocolTypesTreeGrid').getSelectionModel().getSelection()[0],
                                    form = Ext.getCmp('ProtocolTypeEditForm').getForm();

                                form.loadRecord(record);
                            },
                            id: 'contextProtocolTypeEdit',
                            itemId: 'contextProtocolTypeEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('ProtocolTypesTreeGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('denomination'),
                                    msg: 'Sei sicuro di voler eliminare questa Voce e tutte le relative Sottovoci?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('ProtocolTypesTree');
                                            record.remove();
                                            store.sync({
                                                callback: function() {
                                                    store.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Voce Titolario eliminata');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Voce Titolario NON eliminata');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextProtocolTypeDelete',
                            itemId: 'contextProtocolTypeDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        },
                        {
                            xtype: 'menuseparator'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                Ext.widget('ProtocolTypeEditWin').show();

                                var record = Ext.getCmp('ProtocolTypesTreeGrid').getSelectionModel().getSelection()[0],
                                    parent = Ext.getCmp('ProtocolTypeEditParentId'),
                                    forbiddenCodes = [],
                                    codes = Ext.getStore('ProtocolTypes').getRange();

                                parent.setValue(record.get('id'));

                                codes.forEach(function(c) {
                                    if (c.get('parent_type_id') == record.get('id')) {
                                        forbiddenCodes.push(c.get('code'));
                                    }
                                });

                                Ext.getCmp('ProtocolTypeEditCode').forbiddenCodes = forbiddenCodes;
                            },
                            id: 'contextProtocolTypeAdd',
                            itemId: 'contextProtocolTypeAdd',
                            iconCls: 'icon-add',
                            text: 'Aggiungi Sottovoce'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onViewBeforeDrop: function(node, data, overModel, dropPosition, dropHandlers, eOpts) {
        var store = Ext.getStore('ProtocolTypesTree');

        // Defer the handling
        dropHandlers.wait = true;
        Ext.MessageBox.confirm('Attenzione', 'Spostare la Voce ' + data.records[0].get('full_denomination') + ' in ' + overModel.get('full_denomination') + '?', function(btn){
            if (btn === 'yes') {
                dropHandlers.processDrop();
            } else {
                dropHandlers.cancelDrop();
            }
        });
    },

    onViewDrop: function(node, data, overModel, dropPosition, eOpts) {
        var store = Ext.getStore('ProtocolTypesTree'),
            a = 'aggiornato';

        nodeDropped = store.getNodeById(data.records[0].get('id'));
        nodeDropped.set({parent_type_id: overModel.get('id')});

        store.sync({
            callback: function() {
                store.load();
            },
            success: function(form, action) {
                Ext.Msg.alert('Successo', 'Voce Titolario aggiornata');
            },
            failure: function(form, action) {
                Ext.Msg.alert('Attenzione', 'Voce Titolario NON aggiornata');
            }
        });
    },

    onProtocolTypesTreeItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];

        if (record.get('locked')) {
            Ext.getCmp('contextProtocolTypeDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextProtocolTypeDelete').setDisabled(false);
        }

        if (record.get('last_level')) {
            Ext.getCmp('contextProtocolTypeAdd').setDisabled(true);
        } else {
            Ext.getCmp('contextProtocolTypeAdd').setDisabled(false);
        }

        Ext.getCmp('ProtocolTypeEditMn').showAt([newX,newY]);
    }

});