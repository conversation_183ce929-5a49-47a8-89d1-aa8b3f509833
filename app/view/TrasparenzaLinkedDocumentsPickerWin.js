/*
 * File: app/view/TrasparenzaLinkedDocumentsPickerWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TrasparenzaLinkedDocumentsPickerWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.TrasparenzaLinkedDocumentsPickerWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.form.field.Text',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.toolbar.Paging'
    ],

    height: 400,
    id: 'TrasparenzaLinkedDocumentsPickerWin',
    itemId: 'TrasparenzaLinkedDocumentsPickerWin',
    width: 400,
    resizable: false,
    title: 'Allega documenti',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'TrasparenzaLinkedDocumentsPickerGrid',
                    itemId: 'TrasparenzaLinkedDocumentsPickerGrid',
                    emptyText: 'Nessun documento da selezionare.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'TrasparenzaDocumentsForm',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'TrasparenzaLinkedDocumentsPickerFilterName',
                                    itemId: 'TrasparenzaLinkedDocumentsPickerFilterName',
                                    checkChangeBuffer: 500,
                                    emptyText: 'Ricerca...',
                                    listeners: {
                                        change: {
                                            fn: me.onTrasparenzaLinkedDocumentsPickerFilterNameChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'pagingtoolbar',
                            dock: 'bottom',
                            displayInfo: true,
                            store: 'TrasparenzaDocumentsForm'
                        }
                    ],
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'filename',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true,
                        showHeaderCheckbox: false,
                        listeners: {
                            select: {
                                fn: me.onCheckboxModelSelect,
                                scope: me
                            },
                            deselect: {
                                fn: me.onCheckboxModelDeselect,
                                scope: me
                            }
                        }
                    }),
                    features: [
                        {
                            ftype: 'grouping',
                            enableGroupingMenu: false,
                            enableNoGroups: false,
                            groupHeaderTpl: [
                                '{name} ({rows.length})'
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onTrasparenzaLinkedDocumentsPickerWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onTrasparenzaLinkedDocumentsPickerFilterNameChange: function(field, newValue, oldValue, eOpts) {
        var store = Ext.getStore('TrasparenzaDocumentsForm');

        store.clearFilter(true);

        if (newValue) {
            store.filter('filename', newValue);
        } else {
            store.clearFilter();
        }
    },

    onCheckboxModelSelect: function(rowmodel, record, index, eOpts) {
        Ext.getStore('TrasparenzaLinkedDocumentsForm').add(record);
    },

    onCheckboxModelDeselect: function(rowmodel, record, index, eOpts) {
        Ext.getStore('TrasparenzaLinkedDocumentsForm').remove(record);
    },

    onTrasparenzaLinkedDocumentsPickerWinClose: function(panel, eOpts) {
        Ext.getStore('TrasparenzaDocumentsForm').clearFilter();
    }

});