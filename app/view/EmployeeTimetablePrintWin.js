/*
 * File: app/view/EmployeeTimetablePrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.EmployeeTimetablePrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.EmployeeTimetablePrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.form.Label',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 468,
    id: 'EmployeeTimetablePrintWin',
    itemId: 'EmployeeTimetablePrintWin',
    minHeight: 400,
    width: 354,
    title: 'Stampa Orario lavorativo',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    flex: 1,
                    id: 'EmployeeTimetablePrint_Container',
                    itemId: 'EmployeeTimetablePrintGrid_Container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            border: false,
                            id: 'EmployeeTimetablePrint_Form',
                            itemId: 'EmployeeTimetablePrint_Form',
                            bodyCls: [
                                'bck-content',
                                'x-panel-body-default',
                                'x-box-layout-ct'
                            ],
                            bodyPadding: 10,
                            header: false,
                            title: 'My Form',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    padding: '5 0',
                                    layout: {
                                        type: 'hbox',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'button',
                                            disabled: true,
                                            id: 'EmployeeTimetablePrintBtnPrint',
                                            itemId: 'EmployeeTimetablePrintBtnPrint',
                                            iconCls: 'icon-printer',
                                            text: 'Stampa',
                                            listeners: {
                                                click: {
                                                    fn: me.onButtonClick,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ],
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'hbox',
                                        align: 'middle'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'EmployeeTimetablePrint_FilterYear',
                                            itemId: 'EmployeeTimetablePrint_FilterYear',
                                            width: 160,
                                            fieldLabel: 'Anno Scolastico',
                                            labelAlign: 'right',
                                            inputId: 'year',
                                            editable: false,
                                            displayField: 'year',
                                            queryMode: 'local',
                                            store: 'Years',
                                            valueField: 'year',
                                            listeners: {
                                                change: {
                                                    fn: me.onEmployeeTimetablePrint_FilterYearChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'label',
                                            flex: 1,
                                            html: '&nbsp;&nbsp;/&nbsp;&nbsp;----',
                                            id: 'EmployeeTimetablePrint_FilterYearLabel',
                                            itemId: 'EmployeeTimetablePrint_FilterYearLabel'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            border: false,
                            height: 250,
                            id: 'EmployeeTimetablePrintGrid',
                            itemId: 'EmployeeTimetablePrintGrid',
                            width: 400,
                            autoScroll: true,
                            title: 'Personale',
                            titleAlign: 'center',
                            emptyText: 'Nessun Personale',
                            enableColumnHide: false,
                            enableColumnMove: false,
                            enableColumnResize: false,
                            hideHeaders: true,
                            sortableColumns: false,
                            store: 'EmployeesTreeActive',
                            displayField: 'denomination',
                            useArrows: true,
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    resizable: false,
                                    dataIndex: 'denomination',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onEmployeeTimetablePrintGridCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                activate: {
                    fn: me.onEmployeeTimetablePrintWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('EmployeeTimetablePrint_Container').setLoading();

        // Take period (start, end)
        var period = Ext.getCmp('EmployeeTimetablePrint_Form').getForm().getValues();

        // Take the merge id to print and put it in a JSON encoded array
        var sel = Ext.getCmp('EmployeeTimetablePrintGrid').getChecked(),
            mergeSelect = new Array();

        Ext.each(sel, function(a) {
            if (a.data.leaf === true) {
                mergeSelect = mergeSelect.concat(a.raw.employee_id);
            }
        });
        var mergeSelectJSON = Ext.JSON.encode(mergeSelect);

        Ext.Ajax.request({
            url: '/mc2-api/core/print',
            params:{
                newSpool: 0,
                print: 'TimeTables',
                namespace: 'Personnel',
                type: 'PDF',
                printClass: 'PrintPDFTimeTables',
                mime: 'application/pdf',
                year: period.year,
                employees: mergeSelectJSON
            },
            success: function(response, opts) {
                Ext.getCmp('EmployeeTimetablePrint_Container').setLoading(false);
                var res = Ext.decode(response.responseText);
                mc2ui.app.showNotifyPrint(res);
            }
        });
    },

    onEmployeeTimetablePrint_FilterYearChange: function(field, newValue, oldValue, eOpts) {
        var label = Ext.getCmp('EmployeeTimetablePrint_FilterYearLabel'),
            year = Ext.getCmp('EmployeeTimetablePrint_FilterYear').getValue();


        label.setText("&nbsp;&nbsp;/&nbsp;&nbsp;" + (String)(year + 1), false);
    },

    onEmployeeTimetablePrintGridCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('EmployeeTimetablePrintWin').enablePrint();
    },

    onEmployeeTimetablePrintWinActivate: function(window, eOpts) {
        var date = parseInt(Ext.Date.format(new Date(),'Y'));
        Ext.getCmp('EmployeeTimetablePrint_FilterYear').setValue(date);

        var t = Ext.getCmp('EmployeeTimetablePrintGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        Ext.getCmp('EmployeeTimetablePrintWin').enablePrint();
    },

    enablePrint: function() {
        var employees = Ext.getCmp('EmployeeTimetablePrintGrid').getChecked();

        if (employees.length > 0) {
            Ext.getCmp('EmployeeTimetablePrintBtnPrint').setDisabled(false);
        } else {
            Ext.getCmp('EmployeeTimetablePrintBtnPrint').setDisabled(true);
        }
    }

});