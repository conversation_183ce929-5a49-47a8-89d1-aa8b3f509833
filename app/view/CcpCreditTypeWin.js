/*
 * File: app/view/CcpCreditTypeWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpCreditTypeWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpCreditTypeWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.form.field.Checkbox',
        'Ext.grid.column.Action',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 360,
    width: 896,
    layout: 'fit',
    title: 'Categorie crediti',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'CcpCreditsTypeGrd',
                    title: '',
                    store: 'CreditsType',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                name: 'description'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 150,
                            dataIndex: 'piano_conti',
                            text: 'Piano dei conti',
                            editor: {
                                xtype: 'textfield',
                                name: 'piano_conti'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 150,
                            dataIndex: 'causale_contabile',
                            text: 'Causale contabile',
                            editor: {
                                xtype: 'textfield',
                                name: 'causale_contabile'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 150,
                            dataIndex: 'causale_contabile_uscite',
                            text: 'Causale contabile uscite',
                            editor: {
                                xtype: 'textfield',
                                name: 'causale_contabile_uscite'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'ccp_payment_method_text',
                            text: 'Metodo pagamento',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if(value===true) return 'SI';
                                else return 'NO';

                            },
                            width: 126,
                            align: 'center',
                            dataIndex: 'show_on_site',
                            text: 'Mostra estratto conto',
                            editor: {
                                xtype: 'checkboxfield'
                            }
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 50,
                            items: [
                                {
                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                        Ext.getStore('CreditsType').remove(record);
                                        Ext.getStore('CreditsType').sync({
                                            callback: function(r,rr) {
                                                var res = r.proxy.reader.jsonData;
                                                if(res.success=== false) {
                                                    Ext.getStore('CreditsType').load();
                                                    Ext.Msg.alert('ATTENZIONE', res.message);
                                                }
                                            }
                                        });

                                    },
                                    iconCls: 'icon-delete'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                },
                                canceledit: {
                                    fn: me.onRowEditingCanceledit,
                                    scope: me
                                }
                            }
                        })
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.getStore('CreditsType').add({});
                                Ext.getCmp('CcpCreditsTypeGrd').editingPlugin.startEdit(Ext.getStore('CreditsType').getRange().length-1);

                            },
                            iconCls: 'icon-add',
                            text: 'Nuova'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                },
                close: {
                    fn: me.onWindowClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        Ext.getStore('CreditsType').sync();

    },

    onRowEditingCanceledit: function(editor, context, eOpts) {
        if(!context.record.get('id')) {
            Ext.getStore('CreditsType').load();
        }
    },

    onWindowShow: function(component, eOpts) {
        Ext.getStore('CreditsType').load();
    },

    onWindowClose: function(panel, eOpts) {
        Ext.getStore('CreditsType').load();
    }

});