/*
 * File: app/view/CcpCategoriesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpCategoriesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpCategoriesWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 300,
    id: 'CcpCategoriesWin',
    itemId: 'CcpCategoriesWin',
    width: 400,
    resizable: false,
    title: 'Categorie di movimento',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    permissible: true,
                    flex: 1,
                    dock: 'top',
                    id: 'CcpCategoriesToolbar',
                    itemId: 'CcpCategoriesToolbar',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpCategoryEditWin').show();
                            },
                            id: 'CcpCategoryNewBtn',
                            itemId: 'CcpCategoryNewBtn',
                            iconCls: 'icon-add',
                            text: 'Nuova'
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'CcpCategoriesGrid',
                    itemId: 'CcpCategoriesGrid',
                    emptyText: 'Nessuna Categoria presente.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'CcpCategories',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                name: 'name'
                            }
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onCcpCategoriesGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'CcpCategoryEditMn',
                    itemId: 'CcpCategoryEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var r = Ext.getCmp('CcpCategoriesGrid').getSelectionModel().getSelection()[0];
                                Ext.widget('CcpCategoryEditWin').show();
                                Ext.getCmp('CcpCategoryEditForm').getForm().loadRecord(r);
                            },
                            id: 'contextCcpCategoryEdit',
                            itemId: 'contextCcpCategoryEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('CcpCategoriesGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('name'),
                                    msg: 'Sei sicuro di voler eliminare questa Categoria?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function(r){
                                        if (r == 'yes') {
                                            store = Ext.getStore('CcpCategories');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load();
                                                },
                                                success: function() {
                                                    Ext.Msg.alert('Successo', 'Categoria eliminata');
                                                },
                                                failure: function() {
                                                    Ext.Msg.alert('Attenzione', 'Categoria NON eliminata');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextCcpCategoryDelete',
                            itemId: 'contextCcpCategoryDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpCategoriesGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('CcpCategoryEditMn').showAt([newX,newY]);

        if (record.get('locked')) {
            Ext.getCmp('contextCcpCategoryEdit').setDisabled(true);
            Ext.getCmp('contextCcpCategoryDelete').setDisabled(true);
        } else {
            Ext.getCmp('contextCcpCategoryEdit').setDisabled(false);
            Ext.getCmp('contextCcpCategoryDelete').setDisabled(false);
        }
    }

});