/*
 * File: app/view/CcpAdditionalTemplateWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpAdditionalTemplateWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpAdditionalTemplateWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.form.field.Number',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    height: 460,
    id: 'CcpAdditionalTemplateWin',
    width: 655,
    title: 'Tipo di movimento predefiniti per ll\'addizionale ',

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'CcpAdditionalTemplateTypeGrd',
                    title: '',
                    store: 'CcpTypes',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            width: 100,
                            align: 'center',
                            dataIndex: 'school_year',
                            text: 'Anno'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'Tipo',
                            flex: 1
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                    })
                },
                {
                    xtype: 'numberfield',
                    id: 'CcpAdditionalTemplateAmounNm',
                    margin: 5,
                    width: 300,
                    fieldLabel: 'Ammontare sconto predefinito',
                    labelAlign: 'right',
                    labelWidth: 200,
                    name: 'amount',
                    hideTrigger: true
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'bottom',
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var additional = Ext.getCmp('CcpAdditionalsGrid').getSelectionModel().getSelection()[0],
                                    additional_id = additional.get('id'),
                                    type_id=[];

                                Ext.each(Ext.getCmp('CcpAdditionalTemplateTypeGrd').getSelectionModel().getSelection(), function(val){
                                    type_id.push(val.get('id'));
                                });

                                Ext.Ajax.request({
                                    url: '/mc2-api/ccp/additional_templates',
                                    method: 'POST',
                                    jsonData: {
                                        type_id: type_id,
                                        additional_id: additional_id
                                    },
                                    success: function() {
                                        Ext.getCmp('CcpAdditionalTemplateWin').close();
                                    }
                                });


                            },
                            iconCls: 'icon-disk',
                            text: 'Salva template'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onWindowShow: function(component, eOpts) {
        var sel = Ext.getCmp('CcpAdditionalTemplateTypeGrd').getSelectionModel(),
            additional_id = Ext.getCmp('CcpAdditionalsGrid').getSelectionModel().getSelection()[0].get('id'),
            ts = Ext.getStore('CcpTypes');

        sel.deselectAll();
        Ext.getStore('CcpTypes').load({

            callback: function() {
                Ext.Ajax.request({
                    url: '/mc2-api/ccp/additional_templates/' + additional_id,
                    method:'GET',
                    success: function(res) {
                        var r = Ext.decode(res.responseText);
                        if(r.success === true) {
                            Ext.each(r.results.movement_types, function(type_r){
                                sel.select(ts.getById(type_r.id).index, true);
                            });
                        }
                    }

                });
            }
        });

    }

});