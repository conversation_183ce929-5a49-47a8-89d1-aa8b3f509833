/*
 * File: app/view/ExportEasyWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ExportEasyWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ExportEasyWin',

    requires: [
        'Ext.form.Panel',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Checkbox',
        'Ext.form.FieldSet',
        'Ext.toolbar.Spacer'
    ],

    inStudent: false,
    id: 'ExportEasyWin',
    width: 400,
    layout: 'fit',
    title: 'Parametri dell\'esportazione',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    sendExport: function(api, force) {

                        var rec = Ext.getCmp('ExportEasyFrm').getForm().getValues(),
                            filter={},
                            ids = [],
                            gridId = Ext.getCmp('ExportEasyWin').inStudent === true ? 'CcpMovementsStudentGrid' : 'CcpMovementsGrid',
                            url=mc2ui.app.settings.easy_mc2_api;//'/mc2-api/ccp/export_easy_movimenti';


                        Ext.getCmp('ExportEasyWin').setLoading();
                        Ext.each(Ext.getCmp(gridId).getSelectionModel().getSelection(), function(v, i){
                            ids.push(v.get('id'));
                        });

                        if (ids.length > 0) {
                            filter.ids = Ext.encode(ids);
                        } else {
                            filter = Ext.getCmp('CcpMovementsFilterForm').getForm().getValues();
                        }

                        if(rec.tipo_addebito) {
                            filter.tipo_addebito = rec.tipo_addebito;
                        }

                        filter.multiple_expiration_dates = rec.multiple_expiration_dates;

                        filter.force = force === true;

                        if (filter.subject_type === 'O') filter.subject_data = filter.query;

                        if(api===true) {
                            filter.api = true;
                        } else {
                            if(mc2ui.app.settings.easy_mc2_api!=='/mc2-api/ccp/export_easy_movimenti') {
                                url += '?' + Ext.urlEncode(filter);
                                window.open(url,'_blank');
                                Ext.getCmp('ExportEasyWin').close();
                                return;
                            }
                        }



                        Ext.Ajax.request({
                            method:'GET',
                            url: url ,
                            params: filter,
                            timeout:90000,
                            success: function(r) {
                                var res = Ext.decode(r.responseText);
                                if(res.success === false) {
                                    Ext.Msg.alert('ATTENZIONE', res.message);
                                    Ext.getCmp('ExportEasyWin').setLoading(false);
                                } else {
                                    Ext.getCmp('ExportEasyWin').setLoading(false);
                                    if( force !== true && (res.easy_warning || res.easy_error) ) {
                                        Ext.Msg.confirm('ATTENZIONE', 'Ci sono dei dati da ricontrollare prima dell\'invio. Verificarli con questo <a target=”_blank” href="'+res.easy_path+'">file</a>. <br> Confermi l\'invio ugualmente?',
                                        function(ar){
                                            if(ar=='yes') {
                                                Ext.getCmp('ExportEasyFrm').sendExport(api, true);
                                            }
                                        }

                                        );
                                    } else {
                                        if(api===true) {
                                            Ext.getCmp('ExportEasyWin').close();
                                            Ext.getCmp('CcpMovementsFilterForm').loadByFilter();

                                        } else {
                                            window.open(res.easy_path,'_blank');
                                            Ext.getCmp('ExportEasyWin').close();

                                        }


                                    }
                                }

                            }
                        });


                    },
                    id: 'ExportEasyFrm',
                    itemId: 'ExportEasyFrm',
                    bodyPadding: 10,
                    title: '',
                    items: [
                        {
                            xtype: 'container',
                            margin: '5 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    flex: 1,
                                    id: 'ExportEasyDefaultBnt',
                                    itemId: 'ExportEasyDefaultBnt',
                                    enableToggle: true,
                                    pressed: true,
                                    text: 'Predefinito per il parente',
                                    listeners: {
                                        toggle: {
                                            fn: me.onButtonToggle,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'button',
                                    flex: 1,
                                    id: 'ExportEasyCustomBtn',
                                    itemId: 'ExportEasyCustomBtn',
                                    enableToggle: true,
                                    text: 'Personalizza',
                                    listeners: {
                                        toggle: {
                                            fn: me.onButtonToggle1,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            disabled: true,
                            id: 'ExportEasyCmb',
                            itemId: 'ExportEasyCmb',
                            fieldLabel: '',
                            name: 'tipo_addebito',
                            displayField: 'name',
                            queryMode: 'local',
                            store: 'CcpPaymentMethods',
                            valueField: 'easy_code'
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    fieldLabel: '',
                                    name: 'multiple_expiration_dates',
                                    boxLabel: 'Forza fattura singola',
                                    uncheckedValue: 'off'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            padding: 5,
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('ExportEasyFrm').sendExport();
                                    },
                                    flex: 1,
                                    text: 'Esporta csv'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('ExportEasyFrm').sendExport(true);
                                    },
                                    flex: 1,
                                    text: 'Marca per il prossimo invio'
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            id: 'EasyMovImportErrors',
                            autoScroll: true,
                            title: 'Report',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                },
                {
                    xtype: 'form',
                    hidden: true,
                    id: 'ExportEasyPaymentFrm',
                    bodyPadding: 10,
                    items: [
                        {
                            xtype: 'fieldset',
                            id: 'EasyPayImportErrors',
                            autoScroll: true,
                            title: 'Report',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var filter={},
                                            ids = [],
                                            gridId = 'CcpPaymentsGrid',
                                            url='/mc2-api/ccp/export_easy_pagamenti';


                                        Ext.each(Ext.getCmp(gridId).getSelectionModel().getSelection(), function(v, i){
                                            ids.push(v.get('id'));
                                        });

                                        if (ids.length > 0) {
                                            filter.ids = Ext.encode(ids);
                                        } else {
                                            filter = Ext.getCmp('CcpPaymentsFilterForm').getFilter();
                                        }


                                        url += '?' + Ext.urlEncode(filter);
                                        window.open(url,'_blank');
                                    },
                                    text: 'Esporta'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onExportEasyWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonToggle: function(button, pressed, eOpts) {
        if (pressed === true) {
            Ext.getCmp('ExportEasyCmb').disable();
            Ext.getCmp('ExportEasyCmb').setValue();
            Ext.getCmp('ExportEasyCustomBtn').toggle(false);
        }
    },

    onButtonToggle1: function(button, pressed, eOpts) {
        if (pressed === true) {
            Ext.getCmp('ExportEasyCmb').enable(true);
            Ext.getCmp('ExportEasyDefaultBnt').toggle(false);
        }
    },

    onExportEasyWinShow: function(component, eOpts) {
        Ext.getCmp('EasyMovImportErrors').removeAll();
        Ext.getCmp('EasyMovImportErrors').setLoading(true);
        Ext.Ajax.request({
            url: '/mc2-api/core/easy-reports/ricavi',
            success: function(res) {
                Ext.getCmp('EasyMovImportErrors').setLoading(false);
                var r = Ext.decode(res.responseText);
                if(r.success === true) {
                    if(r.results.length>0) {
                        lb = Ext.create('Ext.form.Label', {
                            html: r.results[0].csv.date_time + ': <a href="/mc2-api/core/print/0?path='+r.results[0].csv.path+'">Scarica report</a>',
                            margin: '2 0'
                        });
                        Ext.getCmp('EasyMovImportErrors').add(lb);
                    }
                }
            }
        });

        Ext.getCmp('EasyPayImportErrors').removeAll();
        Ext.getCmp('EasyPayImportErrors').setLoading(true);
        Ext.Ajax.request({
            url: '/mc2-api/core/easy-reports/incassi',
            success: function(res) {
                Ext.getCmp('EasyPayImportErrors').setLoading(false);
                var r = Ext.decode(res.responseText);
                if(r.success === true) {
                    if(r.results.length>0) {
                        lb = Ext.create('Ext.form.Label', {
                            html: r.results[0].csv.date_time + ': <a href="/mc2-api/core/print/0?path='+r.results[0].csv.path+'">Scarica report</a>',
                            margin: '2 0'
                        });
                        Ext.getCmp('EasyPayImportErrors').add(lb);
                    }
                }
            }
        });

    }

});