/*
 * File: app/view/ArchiveSignRemoteWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveSignRemoteWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveSignRemoteWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.Label',
        'Ext.form.field.ComboBox',
        'Ext.button.Button'
    ],

    id: 'ArchiveSignRemoteWin',
    itemId: 'ArchiveSignRemoteWin',
    width: 400,
    layout: 'fit',
    title: 'Firma remota',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'ArchiveSignRemoteFormatFrm',
                    itemId: 'ArchiveSignRemoteFormatFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/archive/sign/otp',
                    items: [
                        {
                            xtype: 'label',
                            hidden: true,
                            html: '<font style="color: #FF8000">Per questo flusso è possibile utilizzare solo la firma di tipo CADES poichè sono presenti anche file che non sono in formato pdf. <br> Questo significa che i file verranno convertiti in p7m.</font>',
                            id: 'ArchiveSignRemoteLabel',
                            itemId: 'ArchiveSignRemoteLabel'
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            margin: '10 0',
                            fieldLabel: 'Tipo di firma',
                            name: 'format',
                            store: 'ArchiveSignTypes',
                            valueField: 'id'
                        },
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            hidden: true,
                            id: 'ArchiveMassiveSignAliasTxt',
                            itemId: 'ArchiveMassiveSignAliasTxt',
                            fieldLabel: 'Alias',
                            name: 'alias'
                        },
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            hidden: true,
                            id: 'ArchiveMassiveSignPinTxt',
                            itemId: 'ArchiveMassiveSignPinTxt',
                            fieldLabel: 'Pin',
                            name: 'pin',
                            inputType: 'password'
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var win = Ext.getCmp('ArchiveSignRemoteWin'),
                                            format = Ext.getCmp('ArchiveSignRemoteFormatFrm').getValues(),
                                            massive = win.massive,
                                            ids = [],
                                            formValues;

                                        if(!massive || typeof massive === 'undefined') {	// Remote Sign

                                            if (mc2ui.app.settings.signType == 'REMOTE') {
                                                Ext.getCmp('ArchiveSignRemoteFormatFrm').submit({
                                                    success: function(form, res) {
                                                        Ext.widget('ArchiveSignRemoteOtpWin').show();
                                                        Ext.getCmp('ArchiveSignRemoteOtpWin').record = Ext.getCmp('ArchiveSignRemoteWin').record;
                                                        Ext.getCmp('ArchiveSignRemoteWin').close();
                                                        Ext.getCmp('ArchiveSignRemoteOtpFrm').getForm().setValues(format);
                                                    },
                                                    failure: function(form, res) {
                                                        if (res.result.status === 307) {
                                                            Ext.widget('ArchiveSignRemoteLogin').show();
                                                            Ext.getCmp('ArchiveSignRemoteLogin').record = Ext.getCmp('ArchiveSignRemoteWin').record;
                                                            Ext.getCmp('ArchiveSignRemoteWin').close();
                                                            Ext.getCmp('ArchiveSignRemoteLoginFrm').getForm().setValues(format);
                                                        } else {
                                                            Ext.Msg.alert('ATTENZIONE','Errore durante l\'invio dei dati. Si prega di rieffettuare la procedura. Se l\'errore dovesse persistere contattare l\'assistenza');
                                                        }
                                                    }
                                                });
                                            } else { // MASSIVA DA EX INTERFACCIA REMOTA
                                                var formValues = Ext.getCmp('ArchiveSignRemoteFormatFrm').getValues();
                                                Ext.Ajax.request({
                                                    method: 'POST',
                                                    url: '/mc2-api/archive/sign/sign/',
                                                    params: {
                                                        folders: Ext.encode([Ext.getCmp('ArchiveSignRemoteWin').record.get('id')]),
                                                        format: formValues.format,
                                                        alias: formValues.alias,
                                                        pin: formValues.pin,
                                                        massive: false
                                                    },
                                                    success: function(response, opts) {
                                                        var res = Ext.decode(response.responseText);
                                                        if (res.status === 200){
                                                            Ext.getCmp('ArchiveSignRemoteWin').close();
                                                            Ext.Msg.alert('FIRMA', 'Documenti firmati correttamente');
                                                            Ext.getStore('ArchiveDocumentsArchived').load();
                                                            Ext.getStore('ArchiveDocumentsUser').load();
                                                            Ext.getStore('ArchiveDocumentsOffice').load();
                                                        } else if (res.status === 307) {
                                                            Ext.widget('ArchiveSignRemoteLogin').show();
                                                            Ext.getCmp('ArchiveSignRemoteLogin').record = Ext.getCmp('ArchiveSignRemoteWin').record;
                                                            Ext.getCmp('ArchiveSignRemoteWin').close();
                                                            Ext.getCmp('ArchiveSignRemoteLoginFrm').getForm().setValues(format);
                                                        } else {
                                                            Ext.Msg.alert('ATTENZIONE','Errore durante l\'invio dei dati. Si prega di rieffettuare la procedura. Se l\'errore dovesse persistere contattare l\'assistenza');
                                                            Ext.getCmp('ArchiveSignRemoteOtpWin').close();
                                                        }

                                                    },
                                                    failure: function(form, res) {

                                                    }
                                                });
                                            }

                                        } else {	// Massive Sign

                                            formValues = Ext.getCmp('ArchiveSignRemoteFormatFrm').getValues();

                                            Ext.each(win.records, function(v) {
                                                ids.push(v.get('id'));
                                            });

                                            Ext.Ajax.request({
                                                method: 'POST',
                                                url: '/mc2-api/archive/sign/sign/',
                                                params: {
                                                    folders: Ext.encode(ids),
                                                    format: formValues.format,
                                                    alias: formValues.alias,
                                                    pin: formValues.pin,
                                                    massive: true
                                                },
                                                success: function(){
                                                    win.close();
                                                    Ext.Msg.alert('SUCCESSO', 'L\'attività di firma massiva è cominciata. Verificare dal dettaglio fascicolo il progresso dei documenti tramite il tasto aggiorna');
                                                }
                                            });
                                        }
                                    },
                                    text: 'Procedi'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});