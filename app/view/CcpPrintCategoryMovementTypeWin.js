/*
 * File: app/view/CcpPrintCategoryMovementTypeWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpPrintCategoryMovementTypeWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpPrintCategoryMovementTypeWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 500,
    id: 'CcpPrintCategoryMovementTypeWin',
    width: 800,
    layout: 'fit',
    title: 'Selezione tipi movimento',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'CcpCategoryPrintTypeSelectedGrd',
                    title: '',
                    store: 'CcpPrintCategoryMovementTypes',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'Name',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            align: 'center',
                            dataIndex: 'school_year',
                            text: 'Anno scolastico'
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value === true) {
                                    return 'ENTRATA';
                                } else {
                                    return 'USCITA';
                                }
                            },
                            align: 'center',
                            dataIndex: 'incoming',
                            text: 'Tipologia'
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        allowDeselect: true,
                        ignoreRightMouseSelection: true,
                        checkOnly: true
                    }),
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var selected = Ext.getCmp('CcpCategoryPrintTypeSelectedGrd').getSelectionModel().getSelection(),
                                            ids = [],
                                            params = {
                                                ccp_print_category_id: Ext.getCmp('CcpPrintCategoryMovementTypeWin').categoryId
                                            };

                                        Ext.each(selected, function(val){
                                            ids.push(val.get('id'));
                                        });
                                        params.ccp_type_ids = ids;

                                        Ext.getCmp('CcpPrintCategoryMovementTypeWin').setLoading(true);
                                        Ext.Ajax.request({
                                            url: '/mc2-api/ccp/print-category-movement-type',
                                            method: 'POST',
                                            jsonData: params,
                                            success: function(res) {
                                                var r = Ext.decode(res.responseText);
                                                if(r.success===true) {
                                                    Ext.getCmp('CcpPrintCategoryMovementTypeWin').close();
                                                } else {
                                                    Ext.Msg.alert('ERRORE', r.message);
                                                }
                                            },
                                            failure: function() {
                                                Ext.Msg.alert('ERRORE', 'Errore durante il salvataggio dei dati');
                                            },
                                            callback: function() {
                                                Ext.getCmp('CcpPrintCategoryMovementTypeWin').setLoading(false);
                                            }

                                        });
                                    },
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});