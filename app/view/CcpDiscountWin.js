/*
 * File: app/view/CcpDiscountWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpDiscountWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpDiscountWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.field.ComboBox',
        'Ext.form.Label',
        'Ext.grid.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.form.field.Number',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    id: 'CcpDiscountWin',
    width: 1000,
    layout: 'fit',
    title: 'Assegna un modello di sconto ad uno o più parenti/studenti',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpDiscountWizardFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'radiogroup',
                            hidden: true,
                            id: 'CcpDiscountS0',
                            width: 400,
                            fieldLabel: 'Scegli la modalità di sconto da assegnare',
                            labelAlign: 'right',
                            labelWidth: 200,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'radiofield',
                                    flex: 1,
                                    name: 'discount_type',
                                    boxLabel: 'Percentuale',
                                    inputValue: 'P'
                                },
                                {
                                    xtype: 'radiofield',
                                    hidden: true,
                                    width: 100,
                                    name: 'discount_type',
                                    boxLabel: 'Assoluto',
                                    inputValue: 'A'
                                }
                            ],
                            listeners: {
                                change: {
                                    fn: me.onCcpDiscountS0Change,
                                    scope: me
                                },
                                afterrender: {
                                    fn: me.onCcpDiscountS0AfterRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            hidden: true,
                            id: 'CcpDiscountS1',
                            fieldLabel: 'Scegli sconto da assegnare',
                            labelAlign: 'right',
                            labelWidth: 200,
                            name: 'additional_id',
                            allowBlank: false,
                            editable: false,
                            displayField: 'name_full',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'CcpAdditionals',
                            valueField: 'id',
                            listeners: {
                                change: {
                                    fn: me.onCcpDiscountS1Change,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            flex: 1,
                            hidden: true,
                            id: 'CcpDiscountS1_A',
                            fieldLabel: 'Scegliere su che credito assegnare lo sconto',
                            labelAlign: 'right',
                            labelWidth: 200,
                            name: 'credit_type_id',
                            displayField: 'description',
                            queryMode: 'local',
                            store: 'CreditsType',
                            valueField: 'id',
                            listeners: {
                                change: {
                                    fn: me.onCcpDiscountS1_AChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'radiogroup',
                            hidden: true,
                            id: 'CcpDiscountS2',
                            width: 400,
                            fieldLabel: 'Scegli a chi assegnare lo sconto',
                            labelAlign: 'right',
                            labelWidth: 200,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'radiofield',
                                    flex: 1,
                                    name: 'subject_type',
                                    boxLabel: 'Parenti',
                                    inputValue: 'P'
                                },
                                {
                                    xtype: 'radiofield',
                                    width: 100,
                                    name: 'subject_type',
                                    boxLabel: 'Studenti',
                                    inputValue: 'S'
                                }
                            ],
                            listeners: {
                                change: {
                                    fn: me.onCcpDiscountS2Change,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'container',
                            hidden: true,
                            id: 'CcpDiscountS3_P',
                            width: 200,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    width: 200,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            padding: 3,
                                            style: 'text-align: right',
                                            text: 'Scegli uno o più parenti a cui assegnare lo sconto:'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    nextHideShow: function() {
                                        if(Ext.getCmp('CcpDiscountParentGrd').getSelectionModel().getSelection().length > 0) {
                                            Ext.getCmp('CcpDiscountS4').show();
                                            Ext.getCmp('CcpDiscountS5').show();
                                            Ext.getCmp('CcpDiscountS6').show();
                                        } else {
                                            Ext.getCmp('CcpDiscountS4').hide();
                                            Ext.getCmp('CcpDiscountS5').hide();
                                            Ext.getCmp('CcpDiscountS6').hide();
                                        }
                                        if(Ext.getCmp('CcpDiscountWizardFrm').getValues().discount_type === 'A')  Ext.getCmp('CcpDiscountS5').hide();
                                    },
                                    flex: 1,
                                    height: 200,
                                    id: 'CcpDiscountParentGrd',
                                    margin: '0 0 0 5',
                                    title: '',
                                    store: 'CcpParents',
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    flex: 1,
                                                    emptyText: 'Cerca ...',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onTextfieldChange,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ],
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                html = record.get('cognome') + ' ' + record.get('nome');
                                                html += '<br /> <font style="font-style:italic;font-size:9px;color:#333"> ' + record.get('parentele') + '</font>';

                                                return html;
                                            },
                                            dataIndex: 'text',
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 40,
                                            align: 'center',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                                        if(Ext.getStore('CcpReportStudentsParents').getById(record.get('id_parente')) === null) {
                                                            Ext.getStore('CcpReportStudentsParents').add({
                                                                id: record.get('id_parente'),
                                                                name: record.get('text')
                                                            });
                                                            Ext.getCmp('CcpDiscountStudentGrd').nextHideShow();
                                                        }
                                                    },
                                                    iconCls: 'icon-arrow_right'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'CcpDiscountParentSelGrd',
                                    margin: '0 0 0 5',
                                    title: 'Selezionati',
                                    store: 'CcpReportStudentsParents',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'name',
                                            text: 'String',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 40,
                                            align: 'center',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.getStore('CcpReportStudentsParents').remove(record);
                                                        Ext.getCmp('CcpDiscountStudentGrd').nextHideShow();
                                                    },
                                                    iconCls: 'icon-cancel'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            hidden: true,
                            id: 'CcpDiscountS3_S',
                            margin: '5 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    width: 200,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            padding: 3,
                                            style: 'text-align: right',
                                            text: 'Scegli uno o più studenti a cui assegnare lo sconto:'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    nextHideShow: function() {
                                        if(Ext.getStore('CcpReportStudentsParents').getRange().length > 0) {
                                            Ext.getCmp('CcpDiscountS4').show();
                                            Ext.getCmp('CcpDiscountS5').show();
                                            Ext.getCmp('CcpDiscountS6').show();
                                        } else {
                                            Ext.getCmp('CcpDiscountS4').hide();
                                            Ext.getCmp('CcpDiscountS5').hide();
                                            Ext.getCmp('CcpDiscountS6').hide();

                                        }
                                        if(Ext.getCmp('CcpDiscountWizardFrm').getValues().discount_type === 'A')  Ext.getCmp('CcpDiscountS5').hide();
                                    },
                                    filterStudents: function() {
                                        var store = Ext.getStore('CcpStudentsDiscount'),
                                            newValue=Ext.getCmp('CcpFilterDiscountStudentTxt').getValue(),
                                            genia=Ext.getCmp('CcpDiscountStudentsGeniaCmb').getValue();

                                        store.clearFilter();

                                        var ids=[];
                                        Ext.getStore('ListaFratelli').clearFilter();
                                        Ext.getStore('ListaFratelli').filter('numero_ordine', genia);
                                        Ext.each(Ext.getStore('ListaFratelli').getRange(), function(v){
                                            ids.push(v.get('id_studente'));
                                        });

                                        store.filterBy(function(rec, id) {
                                            var ret=true;

                                            if(newValue) {
                                                if(rec.get('display').toLowerCase().indexOf(newValue.toLowerCase()) > -1 ||
                                                rec.get('genitori').toLowerCase().indexOf(newValue.toLowerCase()) > -1) {
                                                    ret=true;
                                                } else ret=false;

                                            }

                                            if(genia!== 'all') {

                                                ret = ret===true && Ext.Array.contains(ids, rec.get('db_id'));
                                            }
                                            return ret;
                                        });

                                    },
                                    flex: 1,
                                    height: 300,
                                    id: 'CcpDiscountStudentGrd',
                                    title: '',
                                    store: 'CcpStudentsDiscount',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                html = record.get('surname') + ' ' + record.get('name') + ' ('+record.get('class')+record.get('section')+' - '+record.get('school_address_code')+')';
                                                html += '<br /> <font style="font-style:italic;font-size:9px;color:#333"> ' + record.get('genitori') + '</font>';

                                                return html;
                                            },
                                            dataIndex: 'display',
                                            text: 'Nome',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 40,
                                            align: 'center',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {

                                                        if(Ext.getStore('CcpReportStudentsParents').getById(record.get('db_id')) === null) {
                                                            Ext.getStore('CcpReportStudentsParents').add({
                                                                id: record.get('db_id'),
                                                                name: record.get('display')
                                                            });
                                                            Ext.getCmp('CcpDiscountStudentGrd').nextHideShow();
                                                        }
                                                    },
                                                    iconCls: 'icon-arrow_right'
                                                }
                                            ]
                                        }
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    flex: 1,
                                                    id: 'CcpDiscountStudentsGeniaCmb',
                                                    fieldLabel: 'Fratelli',
                                                    value: 'all',
                                                    editable: false,
                                                    displayField: 'numero_ordine',
                                                    forceSelection: true,
                                                    store: [
                                                        [
                                                            'all',
                                                            'Tutti'
                                                        ],
                                                        [
                                                            'primogeniti',
                                                            'Primogeniti'
                                                        ],
                                                        [
                                                            'secondogeniti',
                                                            'Secondogeniti'
                                                        ],
                                                        [
                                                            'terzogeniti',
                                                            'Terzogeniti'
                                                        ],
                                                        [
                                                            'quartogeniti',
                                                            'Quartogeniti'
                                                        ],
                                                        [
                                                            'quintogeniti',
                                                            'Quintogeniti'
                                                        ],
                                                        
                                                    ],
                                                    valueField: 'numero_ordine',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onComboboxSelect,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'textfield',
                                                    flex: 1,
                                                    id: 'CcpFilterDiscountStudentTxt',
                                                    fieldLabel: '',
                                                    emptyText: 'Cerca ...',
                                                    listeners: {
                                                        change: {
                                                            fn: me.onTextfieldChange1,
                                                            delay: 500,
                                                            buffer: 500,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'treepanel',
                                    nextHideShow: function() {
                                        if(Ext.getCmp('CcpDiscountWizardTree').getChecked().length > 0) {
                                            Ext.getCmp('CcpDiscountS4').show();
                                            Ext.getCmp('CcpDiscountS5').show();
                                            Ext.getCmp('CcpDiscountS6').show();

                                        } else {
                                            Ext.getCmp('CcpDiscountS4').hide();
                                            Ext.getCmp('CcpDiscountS5').hide();
                                            Ext.getCmp('CcpDiscountS6').hide();
                                        }

                                        if(Ext.getCmp('CcpDiscountWizardFrm').getValues().discount_type === 'A')  Ext.getCmp('CcpDiscountS5').hide();
                                    },
                                    flex: 1,
                                    height: 200,
                                    hidden: true,
                                    id: 'CcpDiscountWizardTree',
                                    margin: '0 0 0 5',
                                    title: '',
                                    titleAlign: 'center',
                                    emptyText: 'Nessuno studente trovato',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    enableColumnResize: false,
                                    hideHeaders: true,
                                    store: 'CcpStudentsTree',
                                    useArrows: true,
                                    viewConfig: {

                                    },
                                    columns: [
                                        {
                                            xtype: 'treecolumn',
                                            dataIndex: 'text',
                                            text: 'Nominativo',
                                            flex: 1
                                        }
                                    ],
                                    listeners: {
                                        checkchange: {
                                            fn: me.onTreepanelCheckChange,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'CcpDiscountStudentSelGrd',
                                    margin: '0 0 0 5',
                                    maxHeight: 300,
                                    autoScroll: true,
                                    title: 'Selezionati',
                                    store: 'CcpReportStudentsParents',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'name',
                                            text: 'String',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 40,
                                            align: 'center',
                                            items: [
                                                {
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.getStore('CcpReportStudentsParents').remove(record);
                                                        Ext.getCmp('CcpDiscountStudentGrd').nextHideShow();
                                                    },
                                                    iconCls: 'icon-cancel'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'numberfield',
                            hidden: true,
                            id: 'CcpDiscountS4',
                            margin: '5 0',
                            fieldLabel: 'Ammontare dello sconto',
                            labelAlign: 'right',
                            labelWidth: 200,
                            name: 'amount',
                            hideTrigger: true
                        },
                        {
                            xtype: 'container',
                            hidden: true,
                            id: 'CcpDiscountS5',
                            width: 200,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    width: 200,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'label',
                                            padding: 3,
                                            style: 'text-align: right',
                                            text: 'Verifica per quali tipi di movimento si applica lo sconto scelto'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    height: 200,
                                    id: 'CcpDiscountAdditionalTemplateTypeGrd',
                                    margin: '0 0 0 5',
                                    title: '',
                                    store: 'CcpTypes',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            width: 100,
                                            align: 'center',
                                            dataIndex: 'school_year',
                                            text: 'Number'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'name',
                                            text: 'String',
                                            flex: 1
                                        }
                                    ],
                                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                                    })
                                }
                            ]
                        },
                        {
                            xtype: 'numberfield',
                            flex: 1,
                            hidden: true,
                            id: 'CcpDiscountS6',
                            margin: '5 0',
                            fieldLabel: 'Ordinamento',
                            labelAlign: 'right',
                            labelWidth: 200,
                            name: 'discount_order',
                            emptyText: 'maggiore di 0 ...',
                            hideTrigger: true
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpDiscountWinShow,
                    scope: me
                },
                close: {
                    fn: me.onCcpDiscountWinClose,
                    scope: me
                }
            },
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'bottom',
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var data = Ext.getCmp('CcpDiscountWizardFrm').getValues(),
                                    url;

                                data.subject_ids = [];
                                data.movement_type_ids = [];

                                var subjects;
                                /*if(data.subject_type === 'P') {
                                subjects = Ext.getCmp('CcpDiscountParentGrd').getSelectionModel().getSelection();
                                Ext.each(subjects, function(val) {
                                data.subject_ids.push(parseInt(val.get('id_parente')));
                                });
                                } else {
                                subjects =  Ext.getStore('CcpReportStudentsParents').getRange();
                                Ext.each(subjects, function(val) {
                                data.subject_ids.push(parseInt(val.get('id')));
                                });
                                }*/

                                subjects =  Ext.getStore('CcpReportStudentsParents').getRange();
                                Ext.each(subjects, function(val) {
                                    data.subject_ids.push(parseInt(val.get('id')));
                                });




                                var movement_types = Ext.getCmp('CcpDiscountAdditionalTemplateTypeGrd').getSelectionModel().getSelection();
                                Ext.each(movement_types, function(val) {
                                    data.movement_type_ids.push(parseInt(val.get('id')));
                                });
                                data.subject_ids = Ext.encode(data.subject_ids);
                                data.movement_type_ids = Ext.encode(data.movement_type_ids);

                                if(data.discount_type === 'P') {
                                    url = '/mc2-api/ccp/save_subject_discount';
                                } else {
                                    url = '/mc2-api/ccp/credits/save_subject_credits';
                                }

                                Ext.Ajax.request({
                                    method: 'POST',
                                    url: url,
                                    params: data,
                                    success: function(res) {
                                        var r = Ext.decode(res.responseText);
                                        if(r.success === true) {
                                            Ext.Msg.alert('SUCCESSO', 'Sconti assegnati correttamente');
                                            Ext.getCmp('CcpDiscountWin').close();
                                        } else {
                                            Ext.Msg.alert('ERRORE', r.message);
                                        }
                                    }
                                });
                            },
                            iconCls: 'icon-disk',
                            text: 'Applica'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpDiscountS0Change: function(field, newValue, oldValue, eOpts) {
        if(newValue.discount_type === 'A') {
            Ext.getCmp('CcpDiscountS1_A').show();
            Ext.getCmp('CcpDiscountS1').hide();
            if(Ext.getCmp('CcpDiscountS5').isVisible()) Ext.getCmp('CcpDiscountS5').hide();
            Ext.getStore('CreditsType').load({params: {discount: 1}});
        }
        else if(newValue.discount_type === 'P') {
            Ext.getCmp('CcpDiscountS1_A').hide();
            Ext.getCmp('CcpDiscountS1').show();
            Ext.getStore('CcpAdditionals').load({params: {type: 'D'}});
        }
    },

    onCcpDiscountS0AfterRender: function(component, eOpts) {
        component.items.items[0].setValue(true);

    },

    onCcpDiscountS1Change: function(field, newValue, oldValue, eOpts) {
        if(newValue !== oldValue) {
            Ext.getCmp('CcpDiscountS2').show();

            var sel = Ext.getCmp('CcpDiscountAdditionalTemplateTypeGrd').getSelectionModel(),
            additional_id = newValue,
            ts = Ext.getStore('CcpTypes');

            sel.deselectAll();
            Ext.getStore('CcpTypes').load({

                callback: function() {
                    Ext.Ajax.request({
                        url: '/mc2-api/ccp/additional_templates/' + additional_id,
                        method:'GET',
                        success: function(res) {
                            var r = Ext.decode(res.responseText);
                            if(r.success === true) {
                                Ext.each(r.results.movement_types, function(type_r){
                                    sel.select(ts.getById(type_r.id).index, true);
                                });
                            }
                        }

                    });
                }
            });

            var defAmount = Ext.getStore('CcpAdditionals').getById(newValue).get('amount');
            if(defAmount>0) Ext.getCmp('CcpDiscountS4').setValue(defAmount);
        }
    },

    onCcpDiscountS1_AChange: function(field, newValue, oldValue, eOpts) {
        if(newValue !== oldValue) {
            Ext.getCmp('CcpDiscountS2').show();
            Ext.getCmp('CcpDiscountS4').setValue();
        }
    },

    onCcpDiscountS2Change: function(field, newValue, oldValue, eOpts) {


        if(newValue.subject_type === 'S') {
            Ext.getCmp('CcpDiscountS3_S').show();
            Ext.getCmp('CcpDiscountS3_P').hide();
            Ext.getCmp('CcpDiscountWizardTree').nextHideShow();
        }
        else if(newValue.subject_type === 'P') {
            Ext.getCmp('CcpDiscountS3_S').hide();
            Ext.getCmp('CcpDiscountS3_P').show();
            Ext.getCmp('CcpDiscountParentGrd').nextHideShow();
        }
    },

    onTextfieldChange: function(field, newValue, oldValue, eOpts) {
        var selection = Ext.getCmp('CcpDiscountParentGrd').getSelectionModel().getSelection();
        Ext.getStore('CcpParents').clearFilter();

        if(newValue && newValue !== oldValue) {


             Ext.getStore('CcpParents').filterBy(function(rec, id) {
                if(rec.get('text').toLowerCase().indexOf(newValue.toLowerCase()) > -1 ||
                   rec.get('parentele').toLowerCase().indexOf(newValue.toLowerCase()) > -1) {
                    return true;
                }
                else {
                    return false;
                }
            });

        }
        Ext.getCmp('CcpDiscountParentGrd').getSelectionModel().select(selection);
    },

    onComboboxSelect: function(combo, records, eOpts) {


        Ext.getCmp('CcpDiscountStudentGrd').filterStudents();
    },

    onTextfieldChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getCmp('CcpDiscountStudentGrd').filterStudents();
    },

    onTreepanelCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('CcpDiscountWizardTree').nextHideShow();
    },

    onCcpDiscountWinShow: function(component, eOpts) {
        Ext.getStore('CcpAdditionals').load({
            params: {
                type:'D'
            }
        });

        Ext.getStore('CcpParents').load();
        component.setY(50);
        Ext.getStore('ListaFratelli').load();

        //Ext.getCmp('CcpDiscountWizardTree').deselectAll();
        Ext.getCmp('CcpDiscountStudentGrd').getSelectionModel();
        Ext.getCmp('CcpDiscountParentGrd').getSelectionModel();
        Ext.getStore('CcpReportStudentsParents').removeAll();
        Ext.getStore('CcpParents').clearFilter();
        Ext.getStore('CcpStudentsDiscount').clearFilter();
    },

    onCcpDiscountWinClose: function(panel, eOpts) {
        Ext.getStore('CreditsType').load();
    }

});