/*
 * File: app/view/AbsenceNewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AbsenceNewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AbsenceNewWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.form.field.Time',
        'Ext.form.field.TextArea',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.Label'
    ],

    height: 300,
    id: 'AbsenceNewWin',
    itemId: 'AbsenceNewWin',
    width: 534,
    resizable: false,
    layout: 'fit',
    title: 'Assenza',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'AbsenceAddFrm',
                    itemId: 'AbsenceAddFrm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default',
                        'x-box-layout-ct'
                    ],
                    bodyPadding: 10,
                    url: '/mc2/applications/employees/absences/write.php',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            id: 'AbsenceAddAbsH',
                            itemId: 'AbsenceAddAbsH',
                            fieldLabel: 'Label',
                            inputId: 'absence_id'
                        },
                        {
                            xtype: 'numberfield',
                            hidden: true,
                            id: 'EmployeeAddAbsH',
                            itemId: 'EmployeeAddAbsH',
                            fieldLabel: 'Label',
                            name: 'employee_id'
                        },
                        {
                            xtype: 'combobox',
                            id: 'AbsKindCmb',
                            fieldLabel: 'Tipo',
                            inputId: 'ab_kind',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            displayField: 'description_code',
                            forceSelection: true,
                            minChars: 2,
                            store: 'AbsenceKindsLinked',
                            typeAhead: true,
                            valueField: 'code',
                            listeners: {
                                select: {
                                    fn: me.onAbsKindCmbSelect,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            endDateField: 'AbsEndDate',
                                            id: 'AbsStartDate',
                                            itemId: 'AbsStartDate',
                                            fieldLabel: 'Data inizio',
                                            inputId: 'start_date',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            vtype: 'daterange',
                                            editable: false,
                                            altFormats: 'd-m-Y',
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y'
                                        },
                                        {
                                            xtype: 'datefield',
                                            startDateField: 'AbsStartDate',
                                            id: 'AbsEndDate',
                                            itemId: 'AbsEndDate',
                                            fieldLabel: 'Data fine',
                                            name: 'end_date',
                                            inputId: 'end_date',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            vtype: 'daterange',
                                            editable: false,
                                            altFormats: 'd-m-Y',
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y'
                                        },
                                        {
                                            xtype: 'datefield',
                                            id: 'AbsReqDate',
                                            itemId: 'AbsReqDate',
                                            fieldLabel: 'Data richiesta',
                                            inputId: 'date_of_req',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            altFormats: 'd-m-Y',
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    height: 94,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'timefield',
                                            endTimeField: 'AbsTimeEndField',
                                            flex: 0,
                                            id: 'AbsTimeStartField',
                                            itemId: 'AbsTimeStartField',
                                            padding: '2 0 0 5',
                                            fieldLabel: 'Ora inizio',
                                            inputId: 'start_time',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            vtype: 'timerange',
                                            format: 'H:i',
                                            submitFormat: 'H:i'
                                        },
                                        {
                                            xtype: 'timefield',
                                            startTimeField: 'AbsTimeStartField',
                                            id: 'AbsTimeEndField',
                                            itemId: 'AbsTimeEndField',
                                            padding: '4 0 0 5',
                                            fieldLabel: 'Ora fine',
                                            inputId: 'end_time',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            vtype: 'timerange',
                                            format: 'H:i',
                                            submitFormat: 'H:i'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'textareafield',
                            id: 'AbsNoteField',
                            itemId: 'AbsNoteField',
                            fieldLabel: 'Note',
                            inputId: 'note'
                        },
                        {
                            xtype: 'label',
                            flex: 1,
                            id: 'AbsenceWarningLbl',
                            itemId: 'AbsenceWarningLbl'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onAbsenceNewWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onAbsKindCmbSelect: function(combo, records, eOpts) {
        Ext.getCmp('AbsenceNewWin').enableFields();
    },

    onButtonClick: function(button, e, eOpts) {
        // Get panel and relative form
        var pnl = Ext.getCmp('AbsenceAddFrm'),
            form = pnl.getForm(),
            selectionModel = Ext.getCmp('AbsencesListPnl').getSelectionModel();

        // Get Absences store
        var as = Ext.getStore('Absences');
        var employee = Ext.getCmp('EmployeePnl').getSelectedEmployee();

        if (form.isValid()) {
            pnl.setLoading();
            form.submit({
                success: function(action, response) {
                    Ext.getStore('Absences').load({
                        params: {
                            employee_id: employee.employee_id
                        }
                    });
                    selectionModel.deselectAll();
                    pnl.setLoading(false);
                    Ext.getCmp('AbsenceNewWin').close();
                }
            });
        }

    },

    onAbsenceNewWinBoxReady: function(component, width, height, eOpts) {
        var abs_kinds = Ext.getStore('AbsenceKindsLinked'),
            employee = Ext.getCmp('EmployeePnl').getSelectedEmployee(),
            record = Ext.getCmp('AbsencesListPnl').getSelectionModel().getSelection()[0],
            form = Ext.getCmp('AbsenceAddFrm').getForm();

        abs_kinds.clearFilter();
        abs_kinds.load();

        Ext.getCmp('EmployeeAddAbsH').setValue(employee.employee_id);

        if (typeof record !== "undefined") {
            form.loadRecord(record);
        }

        Ext.getCmp('AbsenceNewWin').setForm();
    },

    setForm: function() {
        var dateStart = Ext.getCmp('AbsStartDate'),
            dateEnd = Ext.getCmp('AbsEndDate'),
            dateReq = Ext.getCmp('AbsReqDate'),
            timeStart = Ext.getCmp('AbsTimeStartField'),
            timeEnd = Ext.getCmp('AbsTimeEndField'),
            date = Ext.Date.format(new Date(),'d/m/Y');

        // Sets starting date value if empty
        if(!dateStart.getValue()){
            dateStart.setValue(date);
        }
        if(!dateReq.getValue()){
            dateReq.setValue(date);
        }
        if(!timeStart.getValue()){
            timeStart.setValue('07:30');
        }
        if(!timeEnd.getValue()){
            timeEnd.setValue('13:30');
        }

        Ext.getCmp('AbsenceNewWin').enableFields();
    },

    enableFields: function() {
        // Set disabled the pickers based on absence stack unit
        var absKind = Ext.getCmp('AbsKindCmb'),
            timeStart = Ext.getCmp('AbsTimeStartField'),
            timeEnd = Ext.getCmp('AbsTimeEndField'),
            st = Ext.getStore('AbsenceKindsLinked'),
            warningLbl = Ext.getCmp('AbsenceWarningLbl'),
            unit = null;

        if (typeof absKind.getValue() !== 'undefined' && absKind.getValue() !== null) {
            if (st.getById(absKind.getValue()) !== null) {
                unit = st.getById(absKind.getValue()).raw.unit;
            }
        }

        if (unit == 'd') {
            timeStart.disable();
            timeStart.setValue('00:00');
            timeEnd.disable();
            timeEnd.setValue('00:00');
            warningLbl.setText('');
        } else {
            timeStart.enable();
            timeEnd.enable();
            if (((!timeStart.getValue()) || (timeStart.getSubmitValue() == '00:00')))  {
                timeStart.setValue('07:30');
            }
            if (((!timeEnd.getValue()) || (timeEnd.getSubmitValue() == '00:00')))  {
                timeEnd.setValue('13:30');
            }
            if (typeof Ext.getCmp('AbsenceAddFrm').getForm()._record == 'undefined') {
                warningLbl.setText('NOTA: Verrà inserita un\'assenza per ogni giorno indicato.');
            } else {
                warningLbl.setText('');
            }
        }
    }

});