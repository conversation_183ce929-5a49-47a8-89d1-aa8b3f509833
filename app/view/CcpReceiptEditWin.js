/*
 * File: app/view/CcpReceiptEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpReceiptEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpReceiptEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.form.field.Hidden'
    ],

    id: 'CcpReceiptEditWin',
    width: 250,
    resizable: false,
    title: 'Ricevuta',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'CcpReceiptEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    method: 'POST',
                    url: '/mc2-api/ccp/receipt',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var component = Ext.getCmp('CcpMovementsStudentGrid').isHierarchicallyHidden() === false ? Ext.getCmp('CcpMovementsStudentGrid') : Ext.getCmp('CcpMovementsGrid'),
                                            m = component.getSelectionModel().getSelection()[0],
                                            ids = Ext.getCmp('CcpReceiptEditLinkedPayments').getValue(),
                                            form = Ext.getCmp('CcpReceiptEditForm').getForm();

                                        form.submit({
                                            success: function(form, action) {
                                                var p = Ext.getStore('CcpPayments');

                                                p.load({
                                                    params: {
                                                        linked: 'on',
                                                        movement: m.get('id')
                                                    },
                                                    callback: function() {
                                                        var r = p.getById(Ext.decode(ids)[0]);

                                                        Ext.getCmp('CcpPaymentsGrid').getSelectionModel().deselectAll();
                                                        Ext.getCmp('CcpReceiptEditWin').close();
                                                        Ext.getStore('CcpReceipts').load();

                                                        if (r) {
                                                            Ext.Ajax.request({
                                                                url: '/mc2-api/core/print',
                                                                params: {
                                                                    newSpool: 1,
                                                                    print: 'Receipt',
                                                                    namespace: 'CCP',
                                                                    type: 'PDF',
                                                                    mime: 'application/pdf',
                                                                    receipt_id: r.get('receipt_id'),
                                                                    number: r.get('receipt_number'),
                                                                    date: r.get('receipt_date')
                                                                },
                                                                success: function(response, opts) {
                                                                    var res = Ext.decode(response.responseText);
                                                                    mc2ui.app.showNotifyPrint(res);
                                                                }
                                                            });
                                                        }
                                                    }
                                                });
                                            },
                                            failure: function() {
                                                Ext.Msg.alert('Attenzione', 'Ricevuta NON elaborata');
                                            }
                                        });
                                    },
                                    iconCls: 'icon-page',
                                    text: 'Emetti'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'numberfield',
                            fieldLabel: 'Numero',
                            labelAlign: 'right',
                            name: 'number',
                            hideTrigger: true,
                            allowDecimals: false,
                            allowExponential: false,
                            minValue: 1
                        },
                        {
                            xtype: 'datefield',
                            fieldLabel: 'Data',
                            labelAlign: 'right',
                            name: 'date',
                            editable: false,
                            format: 'd/m/Y',
                            startDay: 1,
                            submitFormat: 'c'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'CcpReceiptEditLinkedPayments',
                            name: 'linked_payments'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});