/*
 * File: app/view/TimeTableDeleteWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TimeTableDeleteWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.TimeTableDeleteWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Date',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column'
    ],

    height: 426,
    id: 'TimeTableDeleteWin',
    itemId: 'TimeTableDeleteWin',
    minWidth: 300,
    width: 300,
    title: 'Cancella periodo',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: 0,
                    height: 116,
                    id: 'TimeTablePeriodFrm',
                    itemId: 'TimeTablePeriodFrm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default',
                        'x-box-layout-ct'
                    ],
                    bodyPadding: 10,
                    header: false,
                    title: 'My Form',
                    layout: {
                        type: 'vbox',
                        align: 'center',
                        pack: 'center'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            padding: '5 0',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    disabled: true,
                                    id: 'TimeTableDeleteBtnDelete',
                                    itemId: 'TimeTableDeleteBtnDelete',
                                    iconCls: 'icon-calendar_delete',
                                    text: 'Cancella',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'datefield',
                            endDateField: 'TimeTableDeleteTo',
                            id: 'TimeTableDeleteFrom',
                            itemId: 'TimeTableDeleteFrom',
                            fieldLabel: 'Da',
                            labelAlign: 'right',
                            labelWidth: 50,
                            inputId: 'from',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            vtype: 'daterange',
                            editable: false,
                            format: 'd/m/Y',
                            startDay: 1,
                            submitFormat: 'Y-m-d'
                        },
                        {
                            xtype: 'datefield',
                            startDateField: 'TimeTableDeleteFrom',
                            id: 'TimeTableDeleteTo',
                            itemId: 'TimeTableDeleteTo',
                            fieldLabel: 'a',
                            labelAlign: 'right',
                            labelWidth: 50,
                            inputId: 'to',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            vtype: 'daterange',
                            editable: false,
                            format: 'd/m/Y',
                            startDay: 1,
                            submitFormat: 'Y-m-d'
                        }
                    ]
                },
                {
                    xtype: 'treepanel',
                    flex: 1,
                    border: false,
                    height: 250,
                    id: 'TimeTableDeleteEmployeesGrid',
                    itemId: 'TimeTableDeleteEmployeesGrid',
                    autoScroll: true,
                    title: 'Personale',
                    titleAlign: 'center',
                    emptyText: 'Nessun Personale',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'EmployeesTreeActive',
                    displayField: 'denomination',
                    rootVisible: false,
                    useArrows: true,
                    viewConfig: {
                        rootVisible: false
                    },
                    columns: [
                        {
                            xtype: 'treecolumn',
                            resizable: false,
                            dataIndex: 'denomination',
                            text: '',
                            flex: 1
                        }
                    ],
                    listeners: {
                        checkchange: {
                            fn: me.onTimeTableDeleteEmployeesGridCheckChange,
                            scope: me
                        }
                    }
                }
            ],
            listeners: {
                activate: {
                    fn: me.onTimeTableDeleteWinActivate,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        // Take period (from, to)
        var period = Ext.getCmp('TimeTablePeriodFrm').getForm().getValues(),
            form = Ext.getCmp('TimeTablePeriodFrm').getForm();

        // Take the merge id to print and put it in a JSON encoded array
        var sel = Ext.getCmp('TimeTableDeleteEmployeesGrid').getChecked(),
            mergeSelect = new Array();

        Ext.each(sel, function(a) {
            if (a.data.leaf === true) {
                mergeSelect = mergeSelect.concat(a.raw.employee_id);
            }
        });
        var mergeSelectJSON = Ext.JSON.encode(mergeSelect);


        if (form.isValid()) {
            Ext.getCmp('TimeTableDeleteWin').setLoading();

            Ext.Ajax.request({
                url: '/mc2/applications/employees/timetables/calendar/destroy.php',
                params:{
                    period: true,
                    employees: mergeSelectJSON,
                    from: period.from,
                    to: period.to
                },
                success: function(response, opts) {
                    Ext.getCmp('TimeTableDeleteWin').setLoading(false);
                    Ext.getCmp('TimeTableDeleteWin').close();
                    Ext.getCmp('TimeTableTabPnl').loadData();
                }
            });
        }
    },

    onTimeTableDeleteEmployeesGridCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        Ext.getCmp('TimeTableDeleteWin').enableDelete();
    },

    onTimeTableDeleteWinActivate: function(window, eOpts) {
        Ext.getCmp('TimeTableDeleteFrom').setValue(new Date());
        var t = Ext.getCmp('TimeTableDeleteEmployeesGrid');
        t.getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });
    },

    enableDelete: function() {
        var employees = Ext.getCmp('TimeTableDeleteEmployeesGrid').getChecked();

        if (employees.length > 0) {
            Ext.getCmp('TimeTableDeleteBtnDelete').setDisabled(false);
        } else {
            Ext.getCmp('TimeTableDeleteBtnDelete').setDisabled(true);
        }
    }

});