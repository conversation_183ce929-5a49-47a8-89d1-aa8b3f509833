/*
 * File: app/view/CcpSignLoginWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpSignLoginWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpSignLoginWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Text',
        'Ext.button.Button'
    ],

    height: 137,
    id: 'CcpSignLoginWin',
    itemId: 'CcpSignLoginWin',
    width: 319,
    layout: 'fit',
    title: 'Login firma massiva',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpSignLoginFrm',
                    itemId: 'CcpSignLoginFrm',
                    bodyPadding: 10,
                    title: '',
                    items: [
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            fieldLabel: 'Alias',
                            name: 'alias'
                        },
                        {
                            xtype: 'textfield',
                            anchor: '100%',
                            fieldLabel: 'Pin',
                            name: 'pin',
                            inputType: 'password'
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'vbox',
                                align: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var formData = Ext.getCmp('CcpSignLoginFrm').getValues(),
                                            ids = [];


                                        Ext.each(Ext.getCmp('CcpInvoicesGrid').getSelectionModel().getSelection(), function(val){
                                            ids.push(val.get('id'));
                                        });

                                        Ext.getCmp('CcpSignLoginWin').setLoading(true);

                                        Ext.Ajax.request({
                                            url: '/mc2-api/ccp/send_fpa_invoice',
                                            params: {
                                                ids: Ext.encode(ids),
                                                alias: formData.alias,
                                                pin: formData.pin
                                            },
                                            success: function(res) {
                                                var r = Ext.decode(res.responseText);
                                                if (r.success === true) {
                                                    Ext.getCmp('CcpSignLoginWin').close();
                                                    Ext.Msg.alert('SUCCESSO', 'Fatture inviate correttamente');
                                                } else {
                                                    Ext.getCmp('CcpSignLoginWin').setLoading(false);
                                                    Ext.Msg.alert('ERRORE', r.message);
                                                }
                                            }
                                        });
                                    },
                                    flex: 1,
                                    text: 'Invia'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});