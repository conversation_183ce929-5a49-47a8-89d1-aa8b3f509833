/*
 * File: app/view/ArchiveDocumentViewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDocumentViewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDocumentViewWin',

    requires: [
        'Ext.form.Label',
        'Ext.form.FieldSet'
    ],

    id: 'ArchiveDocumentViewWin',
    itemId: 'ArchiveDocumentViewWin',
    width: 591,
    title: 'Dettaglio',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                padding: 5
                            },
                            items: [
                                {
                                    xtype: 'label',
                                    width: 150,
                                    text: 'Data caricamento:'
                                },
                                {
                                    xtype: 'label',
                                    flex: 1,
                                    id: 'ArchiveDocumentUploadDate',
                                    itemId: 'ArchiveDocumentUploadDate',
                                    text: ''
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: 5,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'label',
                                    width: 150,
                                    text: 'Data completamento'
                                },
                                {
                                    xtype: 'label',
                                    flex: 1,
                                    id: 'ArchiveDocumentCompleteDate',
                                    itemId: 'ArchiveDocumentCompleteDate',
                                    text: ''
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'container',
                    padding: 5,
                    layout: {
                        type: 'hbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'label',
                            width: 150,
                            text: 'Descrizione breve:'
                        },
                        {
                            xtype: 'label',
                            flex: 1,
                            id: 'ArchiveDocumentShortDescription',
                            itemId: 'ArchiveDocumentShortDescription',
                            text: ''
                        }
                    ]
                },
                {
                    xtype: 'container',
                    padding: 5,
                    layout: {
                        type: 'hbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'label',
                            width: 150,
                            text: 'Note'
                        },
                        {
                            xtype: 'label',
                            flex: 1,
                            id: 'ArchiveDocumentNote',
                            itemId: 'ArchiveDocumentNote',
                            text: ''
                        }
                    ]
                },
                {
                    xtype: 'container',
                    flex: 1,
                    padding: 5,
                    layout: 'fit',
                    items: [
                        {
                            xtype: 'fieldset',
                            id: 'ArchiveDocumentFiles',
                            itemId: 'ArchiveDocumentFiles',
                            collapsible: true,
                            title: 'Files',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                },
                {
                    xtype: 'container',
                    flex: 1,
                    padding: 5,
                    layout: 'fit',
                    items: [
                        {
                            xtype: 'fieldset',
                            id: 'ArchiveDocumentChecks',
                            itemId: 'ArchiveDocumentChecks',
                            collapsed: true,
                            collapsible: true,
                            title: 'Presa visione',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                },
                {
                    xtype: 'container',
                    flex: 1,
                    padding: 5,
                    layout: 'fit',
                    items: [
                        {
                            xtype: 'fieldset',
                            id: 'ArchiveDocumentHistory',
                            itemId: 'ArchiveDocumentHistory',
                            collapsed: true,
                            collapsible: true,
                            title: 'Storico',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});