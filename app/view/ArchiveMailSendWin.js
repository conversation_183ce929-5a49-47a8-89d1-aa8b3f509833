/*
 * File: app/view/ArchiveMailSendWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveMailSendWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveMailSendWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.view.BoundList',
        'Ext.XTemplate',
        'Ext.form.FieldSet',
        'Ext.button.Button',
        'Ext.grid.Panel',
        'Ext.grid.column.Action',
        'Ext.grid.View',
        'Ext.form.field.TextArea',
        'Ext.form.Label',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Toolbar'
    ],

    id: 'ArchiveMailSendWin',
    layout: 'fit',
    title: 'Invio mail',
    modal: true,

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'SendMailFrm',
                    itemId: 'SendMailFrm',
                    bodyPadding: 5,
                    title: '',
                    url: '/mc2-api/archive/mail/mail',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            id: 'CcpSentMailFromCmb',
                            fieldLabel: 'Da',
                            name: 'account_id',
                            inputId: '',
                            editable: false,
                            displayField: 'email',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'ArchiveMailAccounts',
                            valueField: 'id'
                        },
                        {
                            xtype: 'combobox',
                            hidden: true,
                            id: 'ArchiveSendMailToCmb',
                            itemId: 'ArchiveSendMailToCmb',
                            fieldLabel: 'A',
                            name: 'to_old',
                            displayField: 'email',
                            multiSelect: true,
                            queryMode: 'local',
                            store: 'AddressBook',
                            valueField: 'email',
                            listConfig: {
                                xtype: 'boundlist',
                                itemSelector: 'div',
                                itemTpl: [
                                    '{name}'
                                ]
                            },
                            listeners: {
                                select: {
                                    fn: me.onArchiveSendMailToCmbSelect,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'fieldset',
                            height: 148,
                            width: 576,
                            title: 'Destinatari',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    layout: {
                                        type: 'hbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            fieldLabel: 'A',
                                            emptyText: 'Cerca ...',
                                            hideTrigger: true,
                                            displayField: 'displayCmb',
                                            forceSelection: true,
                                            minChars: 2,
                                            store: 'Contacts',
                                            typeAhead: true,
                                            valueField: 'id',
                                            listeners: {
                                                select: {
                                                    fn: me.onComboboxSelect,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function (button, e) {
                                                Ext.widget('ContactAddWin').show();
                                                Ext.getCmp('ContactAddFromMail').setValue(1);
                                            },
                                            margin: '0 5',
                                            iconCls: 'icon-add',
                                            text: ''
                                        }
                                    ]
                                },
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    margin: '2 0',
                                    title: 'Destinatari',
                                    emptyText: 'Nessun destinatario',
                                    hideHeaders: true,
                                    store: 'ArchiveMailSendContact',
                                    columns: [
                                        {
                                            xtype: 'actioncolumn',
                                            width: 30,
                                            items: [
                                                {
                                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                                        if (r.get('type') == 'G') {
                                                            return 'icon-group';
                                                        }
                                                        return 'icon-user';
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'name',
                                            text: '',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 35,
                                            items: [
                                                {
                                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.getStore('ArchiveMailSendContact').remove(record);

                                                    },
                                                    iconCls: 'icon-delete'
                                                }
                                            ]
                                        }
                                    ],
                                    viewConfig: {
                                        emptyText: 'Nessun destinatario'
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Oggetto',
                            name: 'subject'
                        },
                        {
                            xtype: 'htmleditor',
                            flex: 1,
                            fieldLabel: 'Messaggio',
                            name: 'message',
                            id: 'ArchiveMailSendMessageHtmlEditor',
                        },
                        {
                            xtype: 'container',
                            id: 'ArchiveMailSendFilesCnt',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'label',
                                    width: 100,
                                    text: 'Alegati:'
                                },
                                {
                                    xtype: 'dataview',
                                    flex: 1,
                                    //itemSelector: 'div',
                                    itemTpl: [
                                        '{filename} <br />'
                                    ],
                                    store: 'ArchiveDocumentFiles'
                                }
                            ]
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            fieldLabel: 'Label',
                            name: 'document'
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            fieldLabel: 'Label',
                            name: 'protocol'
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            name: 'mail_id'
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            name: 'mail_account_id'
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function (button, e) {
                                var account_id = Ext.getCmp('CcpSentMailFromCmb').getValue(),
                                    email_from = account_id ? Ext.getCmp('CcpSentMailFromCmb').getStore().getById(account_id).get('email') : null,
                                    recipients = [];
                                Ext.each(Ext.getStore('ArchiveMailSendContact').getRange(), function (val) {
                                    recipients.push(val.data);
                                });

                                Ext.getCmp('ArchiveMailSendWin').setLoading();

                                Ext.getCmp('SendMailFrm').submit({
                                    params: {
                                        to: Ext.encode(recipients),
                                        email_from: email_from
                                    },
                                    success: function (r, res) {
                                        Ext.getCmp('ArchiveMailSendWin').close();
                                        Ext.Msg.alert('SUCCESSO', 'Mail inviata correttamente');
                                    },
                                    failure: function (r, res) {
                                        Ext.getCmp('ArchiveMailSendWin').setLoading(false);
                                        Ext.Msg.alert('ATTENZIONE', Ext.decode(res.response.responseText).message);
                                    },
                                    callback: function () {
                                        Ext.getCmp('ArchiveMailSendWin').setLoading(false);
                                    }
                                });
                            },
                            iconCls: 'icon-mail',
                            text: 'Invia'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onArchiveMailSendWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onArchiveSendMailToCmbSelect: function (combo, records, eOpts) {
        combo.collapse();
    },

    onComboboxSelect: function (combo, records, eOpts) {
        var contacts = Ext.getStore('ArchiveMailSendContact');

        if (!contacts.getById(records[0].get('id'))) {
            contacts.add(records);
        }
        combo.reset();

    },

    onArchiveMailSendWinShow: function (component, eOpts) {
        Ext.getStore('ArchiveMailSendContact').removeAll();
        Ext.getStore('ArchiveMailAccounts').load({
            params: {
                out: true,
                mailer: 1
            },
            callback: function (r, res) {
                var response = Ext.decode(res.response.responseText);
                if (response.success === true) {
                    if (response.results.length === 0) {
                        Ext.getCmp('CcpSentMailFromCmb').disable();
                    }
                }
            }
        });

        Ext.getStore('AddressBook').load();

    }

});