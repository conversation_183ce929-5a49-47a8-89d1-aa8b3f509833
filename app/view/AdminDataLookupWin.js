/*
 * File: app/view/AdminDataLookupWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AdminDataLookupWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AdminDataLookupWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.form.field.ComboBox',
        'Ext.button.Button'
    ],

    height: 400,
    id: 'AdminDataLookupWin',
    itemId: 'AdminDataLookupWin',
    minHeight: 400,
    minWidth: 600,
    width: 600,
    title: 'Data lookup',
    maximizable: true,
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'AdminDataLookupQueryGrid',
                    itemId: 'AdminDataLookupQueryGrid',
                    header: false,
                    emptyText: 'No data queried',
                    store: 'DataLookupResults',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            dataIndex: 'data',
                            hideable: false,
                            text: 'Data',
                            flex: 1
                        }
                    ],
                    viewConfig: {
                        enableTextSelection: true
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            layout: {
                                type: 'hbox',
                                padding: 5
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    id: 'AdminDataLookupQueryID',
                                    itemId: 'AdminDataLookupQueryID',
                                    fieldLabel: 'ID value(s)',
                                    labelAlign: 'right',
                                    labelWidth: 60,
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    emptyText: 'id1,id2,...'
                                },
                                {
                                    xtype: 'combobox',
                                    id: 'AdminDataLookupQueryTable',
                                    itemId: 'AdminDataLookupQueryTable',
                                    fieldLabel: 'Table',
                                    labelAlign: 'right',
                                    labelWidth: 45,
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    editable: false,
                                    displayField: 'name',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'MC2Tables',
                                    valueField: 'table'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        // Get record of the specified table
                                        var record = Ext.getStore('MC2Tables').getById(Ext.getCmp('AdminDataLookupQueryTable').getValue());

                                        // Loads results store with extra params
                                        Ext.getStore('DataLookupResults').load({
                                            params:
                                            {
                                                query_ids: Ext.getCmp('AdminDataLookupQueryID').getValue(),
                                                query_table: record.raw.table,
                                                query_field: record.raw.field,
                                                query_field_type: record.raw.field_type === true ? 1 : 0
                                            }
                                        });
                                    },
                                    margins: '0 0 0 20',
                                    id: 'AdminDataLookupQuerySearch',
                                    itemId: 'AdminDataLookupQuerySearch',
                                    iconCls: 'icon-find',
                                    text: 'Search'
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onDataLookupWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onDataLookupWinBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('DataLookupResults').removeAll();
    }

});