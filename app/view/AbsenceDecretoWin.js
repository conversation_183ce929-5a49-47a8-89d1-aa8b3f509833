/*
 * File: app/view/AbsenceDecretoWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AbsenceDecretoWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AbsenceDecretoWin',

    requires: [
        'Ext.form.field.HtmlEditor',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 642,
    width: 749,
    layout: 'fit',
    title: 'Anteprima decreto',
    maximizable: true,
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'htmleditor',
                    height: 150,
                    id: 'DecretoEditorHtml',
                    itemId: 'DecretoEditorHtml',
                    enableColors: false,
                    enableFont: false,
                    enableLinks: false,
                    enableLists: false,
                    enableSourceEdit: false
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'container',
                            flex: 1,
                            padding: '5 0',
                            layout: {
                                type: 'vbox',
                                align: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    iconCls: 'icon-application_view_list',
                                    text: 'Stampa',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        childWindow = window.open('','childWindow','location=yes, menubar=yes, toolbar=yes');
        childWindow.document.open();
        childWindow.document.write('<html><head></head><body>');
        childWindow.document.write(Ext.getCmp('DecretoEditorHtml').getValue());
        childWindow.document.write('</body></html>');
        childWindow.print();
        childWindow.document.close();
        childWindow.close();
    }

});