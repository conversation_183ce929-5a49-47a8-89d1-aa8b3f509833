/*
 * File: app/view/ProtocolProtocolNewLinkedProtocolsPickerWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolProtocolNewLinkedProtocolsPickerWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolProtocolNewLinkedProtocolsPickerWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.form.field.Text',
        'Ext.grid.column.Date',
        'Ext.grid.View',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Paging'
    ],

    height: 400,
    id: 'ProtocolProtocolNewLinkedProtocolsPickerWin',
    itemId: 'ProtocolProtocolNewLinkedProtocolsPickerWin',
    width: 600,
    resizable: false,
    title: 'Collega protocolli',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'ProtocolProtocolNewLinkedProtocolsPickerGrid',
                    itemId: 'ProtocolProtocolNewLinkedProtocolsPickerGrid',
                    emptyText: 'Nessun protocollo da selezionare.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'ProtocolProtocolsForm',
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'ProtocolProtocolNewLinkedProtocolsPickerFilterName',
                                    itemId: 'ProtocolProtocolNewLinkedProtocolsPickerFilterName',
                                    checkChangeBuffer: 500,
                                    emptyText: 'Ricerca...',
                                    listeners: {
                                        change: {
                                            fn: me.onProtocolProtocolNewLinkedDocumentsPickerFilterNameChange1,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'pagingtoolbar',
                            dock: 'bottom',
                            displayInfo: true,
                            store: 'ProtocolProtocolsForm'
                        }
                    ],
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            width: 75,
                            resizable: false,
                            align: 'right',
                            dataIndex: 'protocol_number',
                            hideable: false,
                            text: 'Numero'
                        },
                        {
                            xtype: 'datecolumn',
                            resizable: false,
                            align: 'center',
                            dataIndex: 'date',
                            hideable: false,
                            text: 'Data',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'type_text',
                            hideable: false,
                            text: 'Titolario'
                        },
                        {
                            xtype: 'gridcolumn',
                            resizable: false,
                            dataIndex: 'description',
                            hideable: false,
                            text: 'Oggetto',
                            flex: 1
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true,
                        showHeaderCheckbox: false,
                        listeners: {
                            select: {
                                fn: me.onCheckboxModelSelect,
                                scope: me
                            },
                            deselect: {
                                fn: me.onCheckboxModelDeselect,
                                scope: me
                            }
                        }
                    })
                }
            ],
            listeners: {
                close: {
                    fn: me.onProtocolProtocolNewLinkedProtocolsPickerWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onProtocolProtocolNewLinkedDocumentsPickerFilterNameChange1: function(field, newValue, oldValue, eOpts) {
        var store = Ext.getStore('ProtocolProtocolsForm');

        store.clearFilter(true);

        if (newValue) {
            store.filter('value', newValue);
        } else {
            store.clearFilter();
        }
    },

    onCheckboxModelSelect: function(rowmodel, record, index, eOpts) {
        Ext.getStore('ProtocolLinkedProtocolsForm').add(record);
    },

    onCheckboxModelDeselect: function(rowmodel, record, index, eOpts) {
        Ext.getStore('ProtocolLinkedProtocolsForm').remove(record);
    },

    onProtocolProtocolNewLinkedProtocolsPickerWinClose: function(panel, eOpts) {
        Ext.getStore('ProtocolProtocolsForm').clearFilter();
    }

});