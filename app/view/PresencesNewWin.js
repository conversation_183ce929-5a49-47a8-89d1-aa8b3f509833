/*
 * File: app/view/PresencesNewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.PresencesNewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.PresencesNewWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.form.field.Hidden',
        'Ext.form.field.Number',
        'Ext.form.field.Time',
        'Ext.form.field.TextArea',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 190,
    id: 'PresencesNewWin',
    itemId: 'PresencesNewWin',
    minHeight: 180,
    width: 524,
    resizable: false,
    layout: 'fit',
    title: 'Timbratura',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'PresencesForm',
                    itemId: 'PresencesForm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default',
                        'x-box-layout-ct'
                    ],
                    header: false,
                    title: 'My Form',
                    url: '/mc2/applications/employees/presences/write.php',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                padding: 5
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'datefield',
                                            id: 'PresenceDate',
                                            itemId: 'PresenceDate',
                                            fieldLabel: 'Data',
                                            labelAlign: 'right',
                                            inputId: 'date_day',
                                            tabIndex: 1,
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            format: 'd/m/Y',
                                            startDay: 1,
                                            submitFormat: 'd-m-Y'
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'PresenceInOut',
                                            itemId: 'PresenceInOut',
                                            fieldLabel: 'Entrata / Uscita',
                                            labelAlign: 'right',
                                            value: 1,
                                            inputId: 'original_inout_edit',
                                            tabIndex: 3,
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            queryMode: 'local',
                                            store: 'InOut',
                                            valueField: 'id'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'PersPresId',
                                            itemId: 'PersPresId',
                                            inputId: 'personnel_presence_id'
                                        },
                                        {
                                            xtype: 'numberfield',
                                            hidden: true,
                                            id: 'PresencesEmployeeId',
                                            itemId: 'PresencesEmployeeId',
                                            name: 'employee_id',
                                            allowBlank: false,
                                            editable: false
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        pack: 'center'
                                    },
                                    items: [
                                        {
                                            xtype: 'timefield',
                                            id: 'PresenceTime',
                                            itemId: 'PresenceTime',
                                            fieldLabel: 'Ora',
                                            labelAlign: 'right',
                                            inputId: 'date_hour',
                                            tabIndex: 2,
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            altFormats: 'H:i',
                                            format: 'H:i',
                                            submitFormat: 'H:i'
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'PresenceInOutType',
                                            itemId: 'PresenceInOutType',
                                            fieldLabel: 'Tipo passaggio',
                                            labelAlign: 'right',
                                            value: 1,
                                            inputId: 'type_edit',
                                            tabIndex: 4,
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            queryMode: 'local',
                                            store: 'InOutType',
                                            valueField: 'id'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            id: 'PresenceDescription',
                            itemId: 'PresenceDescription',
                            padding: '0 5 0 5',
                            fieldLabel: 'Note',
                            labelAlign: 'right',
                            name: 'description',
                            inputId: 'description',
                            cols: 60,
                            rows: 2
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onPresencesNewWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        // Get panel and relative form
        var pnl = Ext.getCmp('PresencesForm');
        var form = pnl.getForm();

        // Get selected Year and Month
        var year = Ext.getCmp('PresencesYearCmb').getValue();
        var month = Ext.getCmp('PresencesMonthCmb').getValue();

        Ext.getCmp('PresencesPerDayList').getSelectionModel().clearSelections();

        // Get Presences store
        var as = Ext.getStore('Presences');

        if (form.isValid()){
            pnl.setLoading();
            form.submit({
                success: function(action, response){
                    Ext.getStore('Presences').load({
                        params: {
                            employee_id: Ext.getCmp('EmployeePnl').getSelectedEmployee().employee_id,
                            year: year,
                            month: month
                        }
                    });
                    pnl.setLoading(false);
                    Ext.getCmp('PresencesNewWin').close();
                }
            });
        }
    },

    onPresencesNewWinBoxReady: function(component, width, height, eOpts) {
        var employee = Ext.getCmp('EmployeePnl').getSelectedEmployee(),
            record = Ext.getCmp('PresencesPerDayList').getSelectionModel().getSelection()[0],
            form = Ext.getCmp('PresencesForm').getForm();

        Ext.getCmp('PresencesEmployeeId').setValue(employee.employee_id);

        if (typeof record !== "undefined") {
            form.loadRecord(record);
        }
    }

});