/*
 * File: app/view/AlboPublicationEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AlboPublicationEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AlboPublicationEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.HtmlEditor',
        'Ext.form.FieldSet',
        'Ext.form.field.Date',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Checkbox',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.toolbar.Fill',
        'Ext.form.field.File',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 700,
    id: 'AlboPublicationEditWin',
    itemId: 'AlboPublicationEditWin',
    width: 600,
    resizable: false,
    title: 'Pubblicazione',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'AlboPublicationEditForm',
                    itemId: 'AlboPublicationEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'htmleditor',
                            height: 100,
                            id: 'AlboPublicationEditTitle',
                            itemId: 'AlboPublicationEditTitle',
                            fieldLabel: 'Titolo',
                            labelAlign: 'right',
                            name: 'title'
                        },
                        {
                            xtype: 'htmleditor',
                            height: 150,
                            id: 'AlboPublicationEditDescription',
                            itemId: 'AlboPublicationEditDescription',
                            fieldLabel: 'Descrizione',
                            labelAlign: 'right',
                            name: 'description'
                        },
                        {
                            xtype: 'fieldset',
                            padding: 5,
                            title: 'Periodo validità',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'datefield',
                                    endDateField: 'AlboPublicationEditExpirationDate',
                                    id: 'AlboPublicationEditStartDate',
                                    itemId: 'AlboPublicationEditStartDate',
                                    fieldLabel: 'Dal*',
                                    labelAlign: 'right',
                                    name: 'start_date',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    vtype: 'daterange',
                                    editable: false,
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'c'
                                },
                                {
                                    xtype: 'datefield',
                                    startDateField: 'AlboPublicationEditStartDate',
                                    id: 'AlboPublicationEditExpirationDate',
                                    itemId: 'AlboPublicationEditExpirationDate',
                                    fieldLabel: 'Al*',
                                    labelAlign: 'right',
                                    name: 'expiration_date',
                                    allowBlank: false,
                                    allowOnlyWhitespace: false,
                                    vtype: 'daterange',
                                    editable: false,
                                    format: 'd/m/Y',
                                    startDay: 1,
                                    submitFormat: 'c'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                padding: '5 0 0 0'
                            },
                            items: [
                                {
                                    xtype: 'container',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        padding: '0 5 0 0'
                                    },
                                    items: [
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboPublicationEditCategory',
                                            itemId: 'AlboPublicationEditCategory',
                                            fieldLabel: 'Categoria*',
                                            labelAlign: 'right',
                                            name: 'category_id',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            store: 'AlboCategories',
                                            valueField: 'id'
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboPublicationEditArea',
                                            itemId: 'AlboPublicationEditArea',
                                            fieldLabel: 'Area*',
                                            labelAlign: 'right',
                                            name: 'area_id',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            store: 'AlboAreas',
                                            valueField: 'id'
                                        },
                                        {
                                            xtype: 'combobox',
                                            id: 'AlboPublicationEditEntity',
                                            itemId: 'AlboPublicationEditEntity',
                                            fieldLabel: 'Ente*',
                                            labelAlign: 'right',
                                            name: 'entity_id',
                                            allowBlank: false,
                                            allowOnlyWhitespace: false,
                                            editable: false,
                                            matchFieldWidth: false,
                                            displayField: 'name',
                                            forceSelection: true,
                                            store: 'AlboEntities',
                                            valueField: 'id'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch',
                                        padding: '0 0 0 5'
                                    },
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            id: 'AlboPublicationEditOmissis',
                                            itemId: 'AlboPublicationEditOmissis',
                                            fieldLabel: 'Omissis',
                                            labelAlign: 'right',
                                            name: 'omissis',
                                            uncheckedValue: 'off'
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            hidden: true,
                                            id: 'AlboPublicationEditPublished',
                                            itemId: 'AlboPublicationEditPublished',
                                            fieldLabel: 'Pubblicato',
                                            name: 'published',
                                            uncheckedValue: 'off'
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'AlboPublicationEditId',
                            itemId: 'AlboPublicationEditId',
                            name: 'id'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var data = Ext.getCmp('AlboPublicationEditForm').getForm().getValues(),
                                            docs = Ext.getStore('AlboLinkedDocumentsForm').getRange(),
                                            store = Ext.getStore('AlboPublications'),
                                            url = store.getProxy().url,
                                            method = 'POST',
                                            record = {
                                                title: data.title,
                                                description: data.description,
                                                start_date: data.start_date,
                                                expiration_date: data.expiration_date,
                                                category_id: data.category_id,
                                                entity_id: data.entity_id,
                                                area_id: data.area_id,
                                                omissis: data.omissis === 'on' ? true : false,
                                                publication_date: data.published === 'on' ? new Date() : null,
                                                linked_documents: []
                                            },
                                            a = 'salvata';

                                        docs.forEach(function(doc) {
                                            record.linked_documents.push(doc.get('id'));
                                        });

                                        // Update or Creation
                                        if (data.id) {
                                            url = url + '/' + data.id;
                                            a = 'aggiornata';
                                            method = 'PUT';
                                            rec = store.getById(parseInt(data.id));
                                            rec.set(record);
                                        } else {
                                            store.add(record);
                                        }

                                        store.sync({
                                            //Ext.Ajax.request({
                                            //url: url,
                                            //method: method,
                                            //params: record,
                                            callback: function() {
                                                store.load();
                                                Ext.getStore('ArchiveDocumentsArchived').load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('AlboGrid').getSelectionModel().deselectAll();
                                                Ext.getCmp('AlboPublicationEditWin').close();
                                                Ext.Msg.alert('Successo', 'Pubblicazione in Albo ' + a);
                                                Ext.getStore('ArchiveDocumentsUser').load();
                                                Ext.getStore('ArchiveDocumentsOffice').load();
                                            },
                                            failure: function(form, action) {
                                                var res = form.proxy.getReader().jsonData;
                                                if(res.status === 0){
                                                    Ext.Msg.alert('Attenzione', 'Pubblicazione in Albo NON ' + a);
                                                } else {
                                                    Ext.Msg.alert('Attenzione', res.message);
                                                }
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'AlboPublicationEditLinkedDocumentsGrid',
                    itemId: 'AlboPublicationEditLinkedDocumentsGrid',
                    title: 'Documenti',
                    titleAlign: 'center',
                    emptyText: 'Nessun documento allegato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'AlboLinkedDocumentsForm',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'filename',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            id: 'AlboPublicationEditLinkedTlb',
                            itemId: 'AlboPublicationEditLinkedTlb',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('AlboPublicationLinkedDocumentsPickerWin').show();

                                        Ext.getStore('AlboDocumentsForm').load();
                                    },
                                    hidden: true,
                                    iconCls: 'icon-attach',
                                    text: 'Allega Documenti'
                                },
                                {
                                    xtype: 'tbfill'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('ArchiveDocumentUploadWin').archiveFrom = 'A';
                                        Ext.getCmp('ArchiveDocumentUploadWin').show();
                                    },
                                    hidden: true,
                                    id: 'AlboPublicationEditArchiveNewBtn',
                                    itemId: 'AlboPublicationEditArchiveNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Archivia nuovo'
                                },
                                {
                                    xtype: 'form',
                                    id: 'UploadFromAlboFrm',
                                    layout: 'fit',
                                    url: '/mc2-api/archive/document_file',
                                    items: [
                                        {
                                            xtype: 'filefield',
                                            fieldLabel: '',
                                            name: 'file',
                                            buttonOnly: true,
                                            buttonText: 'Carica nuovo',
                                            listeners: {
                                                change: {
                                                    fn: me.onFilefieldChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onAlboPublicationEditLinkedDocumentsGridItemContextMenu,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'menu',
                    flex: 1,
                    floating: false,
                    hidden: true,
                    id: 'AlboPublicationEditLinkedDocumentEditMn',
                    itemId: 'AlboPublicationEditLinkedDocumentEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var record = Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').getSelectionModel().getSelection()[0],
                                    storeDocs = Ext.getStore('AlboLinkedDocumentsForm');

                                storeDocs.remove(record);
                            },
                            id: 'contextAlboPublicationEditLinkedDocumentDelete',
                            itemId: 'contextAlboPublicationEditLinkedDocumentDelete',
                            iconCls: 'icon-cancel',
                            text: 'Rimuovi'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onFilefieldChange: function(filefield, value, eOpts) {
        Ext.getCmp('UploadFromAlboFrm').submit({
            success: function(form, action) {
                var r = Ext.decode(action.response.responseText);
                Ext.getCmp('AlboPublicationEditLinkedDocumentsGrid').getStore().add(r.results);
            }
        });
    },

    onAlboPublicationEditLinkedDocumentsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('AlboPublicationEditLinkedDocumentEditMn').showAt([newX,newY]);
    }

});