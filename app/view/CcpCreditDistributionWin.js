/*
 * File: app/view/CcpCreditDistributionWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpCreditDistributionWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpCreditDistributionWin',

    requires: [
        'Ext.form.FieldSet',
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 403,
    id: 'CcpCreditDistributionWin',
    width: 473,
    title: 'Distribuzione crediti',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'fieldset',
                    hidden: true,
                    id: 'DistributionErrors',
                    title: 'Report',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    }
                },
                {
                    xtype: 'form',
                    flex: 1,
                    id: 'CcpDistributionFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            id: 'CcpDistributionCreditCmb',
                            fieldLabel: 'Credito',
                            name: 'creditTypeId',
                            allowBlank: false,
                            displayField: 'description',
                            queryMode: 'local',
                            store: 'CreditsType',
                            valueField: 'id'
                        },
                        {
                            xtype: 'combobox',
                            id: 'CcpDistributionBankCmb',
                            fieldLabel: 'Banca',
                            name: 'bankAccountId',
                            allowBlank: false,
                            displayField: 'denomination',
                            queryMode: 'local',
                            store: 'CoreBankAccounts',
                            valueField: 'id'
                        },
                        {
                            xtype: 'container',
                            margin: '2 0',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    fieldLabel: 'Tipo movimento',
                                    name: 'schoolYear',
                                    allowBlank: false,
                                    displayField: 'value',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'CcpTypeSchoolYears',
                                    valueField: 'value',
                                    listeners: {
                                        select: {
                                            fn: me.onComboboxSelect,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    disabled: true,
                                    id: 'CcpDistributionTypeCmb',
                                    fieldLabel: '',
                                    name: 'ccp_type',
                                    displayField: 'name',
                                    multiSelect: true,
                                    queryMode: 'local',
                                    store: 'CcpTypes',
                                    valueField: 'id'
                                }
                            ]
                        },
                        {
                            xtype: 'datefield',
                            fieldLabel: 'Data pagamento',
                            name: 'payment_date',
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d',
                            listeners: {
                                afterrender: {
                                    fn: me.onDatefieldAfterRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'checkboxfield',
                            width: 150,
                            fieldLabel: '',
                            name: 'expiredOnly',
                            boxLabel: 'Solo movimenti scaduti',
                            checked: true
                        },
                        {
                            xtype: 'treepanel',
                            flex: 1,
                            id: 'CcpDistributionStudents',
                            title: 'Studenti',
                            hideHeaders: true,
                            store: 'CcpStudentsTree',
                            viewConfig: {

                            },
                            columns: [
                                {
                                    xtype: 'treecolumn',
                                    dataIndex: 'text',
                                    text: '',
                                    flex: 1
                                }
                            ],
                            listeners: {
                                checkchange: {
                                    fn: me.onTreepanelCheckChange,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onCcpCreditDistributionWinShow,
                    scope: me
                }
            },
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var f = Ext.getCmp('CcpDistributionFrm').getForm(),
                                    students = Ext.getCmp('CcpDistributionStudents').getChecked(),
                                    s = [],
                                    data = Ext.getCmp('CcpDistributionFrm').getValues();

                                if(f.isValid()) {


                                    for (var k in students) {
                                        if (students[k].data.leaf === true) {
                                            last = students[k].data.id.split('/').length - 1;
                                            s.push(students[k].data.id.split('/')[last]);
                                        }
                                    }

                                    if(s.length === 0) {
                                        Ext.Msg.alert('ERRORE', 'Seleziona almeno uno studente');
                                        return;
                                    }

                                    data.students = Ext.encode(s);
                                    data.ccp_type = Ext.encode(data.ccp_type);


                                    Ext.Ajax.request({
                                        url: '/mc2-api/ccp/credits/paywithcredit',
                                        method:'POST',
                                        params: data,
                                        success: function(response, opts) {
                                            var res = Ext.decode(response.responseText);
                                            Ext.Msg.alert('INFO', 'Distribuzione dei crediti in corso. L\'operazione potrebbe richiedere qualche minuto. Controllare periodicamente lo stato dei crediti degli studenti');
                                            Ext.getCmp('CcpCreditDistributionWin').close();
                                        }
                                    });
                                }
                            },
                            text: 'Distribuisci'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onComboboxSelect: function(combo, records, eOpts) {


        Ext.getStore('CcpTypes').load({params: {school_year: records[0].get('value')}});


        Ext.getCmp('CcpDistributionTypeCmb').enable();
    },

    onDatefieldAfterRender: function(component, eOpts) {
        var dd = new Date();
        component.setValue(dd);
    },

    onTreepanelCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);
    },

    onCcpCreditDistributionWinShow: function(component, eOpts) {
        var b = Ext.getStore('CoreBankAccounts').getRange()[0];
        if (b) Ext.getCmp('CcpDistributionBankCmb').setValue(b.get('id'));

        var c = Ext.getStore('CreditsType').getRange()[0];
        if (c) Ext.getCmp('CcpDistributionCreditCmb').setValue(c.get('id'));

        Ext.getStore('CcpStudentsTree').load();


        Ext.getCmp('DistributionErrors').removeAll();
        Ext.Ajax.request({
            url: '/mc2-api/core/reports',
            success: function(res) {
                var r = Ext.decode(res.responseText);console.log(r);
                if(r.success === true) {
                    if(r.results.PAGAMENTI_CREDITO_SUCCESS) {
                        lb = Ext.create('Ext.form.Label', {
                            html: r.results.PAGAMENTI_CREDITO_SUCCESS.xlsx.date_time + ': <a href="/mc2-api/core/print/0?path='+r.results.PAGAMENTI_CREDITO_SUCCESS.xlsx.path+'">Scarica report pagamenti</a>',
                            margin: '2 0'
                        });
                        Ext.getCmp('DistributionErrors').add(lb);
                        Ext.getCmp('DistributionErrors').show();
                    }

                }
            }
        });
    }

});