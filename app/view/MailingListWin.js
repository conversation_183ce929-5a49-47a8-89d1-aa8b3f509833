/*
 * File: app/view/MailingListWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.MailingListWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.MailingListWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.column.Action',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 369,
    id: 'MailingListWin',
    itemId: 'MailingListWin',
    width: 745,
    title: 'Gestione Mailing List',
    modal: true,

    layout: {
        type: 'hbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            },
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'ContactGroupGrid',
                    itemId: 'ContactGroupGrid',
                    title: 'Gruppi',
                    hideHeaders: true,
                    store: 'ContactGroups',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'String',
                            flex: 1,
                            editor: {
                                xtype: 'textfield'
                            }
                        }
                    ],
                    viewConfig: {
                        listeners: {
                            itemclick: {
                                fn: me.onViewItemClick,
                                scope: me
                            },
                            itemcontextmenu: {
                                fn: me.onViewItemContextMenu,
                                scope: me
                            }
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('ContactGroupGrid').getSelectionModel().deselectAll();
                                        Ext.getStore('ContactGroupLinked').removeAll();
                                        Ext.getCmp('ContactGeneralGroupPnl').disable();

                                        Ext.getStore('ContactGroups').insert(0, {name: 'Nuova lista'});
                                        Ext.getCmp('ContactGroupGrid').editingPlugin.startEdit(0);
                                    },
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                }
                            }
                        })
                    ]
                },
                {
                    xtype: 'panel',
                    flex: 3,
                    id: 'ContactGeneralGroupPnl',
                    itemId: 'ContactGeneralGroupPnl',
                    title: 'Seleziona un gruppo',
                    layout: {
                        type: 'hbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            title: 'Elenco contatti',
                            store: 'Contacts',
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'name',
                                    text: 'Nome',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'email',
                                    text: 'Email',
                                    flex: 1
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 30,
                                    align: 'center',
                                    iconCls: 'icon-add',
                                    items: [
                                        {
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                var s = Ext.getStore('ContactGroupLinked');

                                                if (!s.getById(record.get('id'))) {
                                                    s.add(record);
                                                }

                                            }
                                        }
                                    ]
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            flex: 1,
                                            fieldLabel: '',
                                            emptyText: 'Cerca ...',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange1,
                                                    delay: 300,
                                                    buffer: 300,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'gridpanel',
                            flex: 1,
                            title: 'Appartenenti al gruppo',
                            store: 'ContactGroupLinked',
                            columns: [
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'name',
                                    text: 'Nome',
                                    flex: 1
                                },
                                {
                                    xtype: 'gridcolumn',
                                    dataIndex: 'email',
                                    text: 'Email',
                                    flex: 1
                                },
                                {
                                    xtype: 'actioncolumn',
                                    width: 30,
                                    align: 'center',
                                    iconCls: 'icon-delete',
                                    items: [
                                        {
                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                Ext.getStore('ContactGroupLinked').remove(record);
                                            }
                                        }
                                    ]
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'textfield',
                                            flex: 1,
                                            fieldLabel: '',
                                            emptyText: 'Cerca ...',
                                            listeners: {
                                                change: {
                                                    fn: me.onTextfieldChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    flex: 1,
                    hidden: true,
                    id: 'ContactGroupMn',
                    itemId: 'ContactGroupMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var selectedRecord = Ext.getCmp('ContactGroupGrid').getSelectionModel().getSelection()[0];

                                Ext.getCmp('ContactGroupGrid').editingPlugin.startEdit(selectedRecord.index);
                            },
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var selectedRecord = Ext.getCmp('ContactGroupGrid').getSelectionModel().getSelection()[0],
                                    store = Ext.getStore('ContactGroups');

                                store.remove(selectedRecord);
                                store.save();
                            },
                            iconCls: 'icon-cancel',
                            text: 'Cancella'
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {

                                var groupId = Ext.getCmp('ContactGroupGrid').getSelectionModel().getSelection()[0].get('id'),
                                    url = '/mc2-api/core/contactgroups/manage/' + groupId,
                                    members = [];

                                Ext.each(Ext.getStore('ContactGroupLinked').getRange(), function(v){
                                    members.push(v.get('id').replace('U', ''));
                                });

                                Ext.getCmp('MailingListWin').setLoading();

                                Ext.Ajax.request({
                                    method: 'POST',
                                    url: url,
                                    params: {
                                        contacts: Ext.encode(members)
                                    },
                                    callback: function() {
                                        Ext.getCmp('MailingListWin').setLoading(false);
                                    }
                                });

                            },
                            iconCls: 'icon-disk',
                            text: 'Salva'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onWindowShow: function(component, eOpts) {
        Ext.getStore('ContactGroups').load();
        Ext.getStore('Contacts').load({
            params: {
                type: 'U'
            }
        });
        Ext.getStore('ContactGroupLinked').removeAll();
        Ext.getCmp('ContactGeneralGroupPnl').disable();

        Ext.getCmp('MailingListWin').setWidth(Ext.getCmp('MainView').getWidth()/100*80);
        Ext.getCmp('MailingListWin').setHeight(Ext.getCmp('MainView').getHeight()/100*80);
        Ext.getCmp('MailingListWin').center()

    },

    onViewItemClick: function(dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ContactGeneralGroupPnl').setTitle(record.get('name'));

        Ext.getStore('ContactGroupLinked').removeAll();

        Ext.Ajax.request({
            url: '/mc2-api/core/contactgroups/' + record.get('id'),
            success: function(r){
                var res = Ext.decode(r.responseText);
                Ext.getStore('ContactGroupLinked').loadData(res.results.contacts);
                Ext.getCmp('ContactGeneralGroupPnl').enable();
            }
        });

    },

    onViewItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ContactGroupMn').showAt(e.getX(), e.getY());
    },

    onRowEditingEdit: function(editor, context, eOpts) {

        Ext.getStore('ContactGroups').save({
            callback: function(records, operation, success) {
                Ext.getStore('ContactGroups').load();
            }
        });
        Ext.getCmp('ContactGeneralGroupPnl').enable();

    },

    onTextfieldChange1: function(field, newValue, oldValue, eOpts) {
        Ext.getStore('ContactGroups').load();
        Ext.getStore('Contacts').load({
            params: {
                type: 'U',
                query: newValue
            }
        });
    },

    onTextfieldChange: function(field, newValue, oldValue, eOpts) {
        var s = Ext.getStore('ContactGroupLinked');
        s.clearFilter();

        if (newValue) {
            s.filterBy(function (record, id) {
                return record.get('name').toLowerCase().indexOf(newValue) > -1 || record.get('email').toLowerCase().indexOf(newValue) > -1;
            });
        }
    }

});