/*
 * File: app/view/ArchiveDocumentArchiveWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDocumentArchiveWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDocumentArchiveWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.Display',
        'Ext.form.field.ComboBox',
        'Ext.Img',
        'Ext.form.Label',
        'Ext.form.RadioGroup',
        'Ext.form.field.Radio',
        'Ext.form.field.TextArea',
        'Ext.form.FieldSet',
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.form.field.Hidden'
    ],

    height: 600,
    id: 'ArchiveDocumentArchiveWin',
    itemId: 'ArchiveDocumentArchiveWin',
    width: 500,
    resizable: false,
    title: 'Archiviazione Documento',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'ArchiveArchiveForm',
                    itemId: 'ArchiveArchiveForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    url: '/mc2-api/archive/document',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var form = Ext.getCmp('ArchiveArchiveForm').getForm(),
                                            metaStore = Ext.getStore('ArchiveClassMetadata'),
                                            meta = [];

                                        metaStore.each(function (metadata) {
                                            meta.push(metadata.getData());
                                        });

                                        if (form.isValid()) {
                                            form.submit({
                                                params: {
                                                    metadata: Ext.encode(meta)
                                                },
                                                waitMsg: 'Archiviazione file...',
                                                success: function(fp, o) {
                                                    Ext.Msg.alert('Successo', 'Archiviazione file avvenuto');
                                                    Ext.getStore('ArchiveDocumentsArchived').load();
                                                    Ext.getCmp('ArchiveDocumentArchiveWin').close();
                                                },
                                                failure: function(form, action) {
                                                    var msg = action.result.message !== '' ? action.result.message : 'Comunicazione col server non riuscita';
                                                    Ext.Msg.alert('Attenzione', msg);
                                                }
                                            });
                                        }
                                    },
                                    formBind: true,
                                    iconCls: 'icon-database',
                                    text: 'Archivia'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'displayfield',
                            id: 'ArchiveArchiveFile',
                            itemId: 'ArchiveArchiveFile',
                            fieldLabel: 'Documento',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'filename'
                        },
                        {
                            xtype: 'combobox',
                            id: 'ArchiveArchiveClass',
                            itemId: 'ArchiveArchiveClass',
                            margin: '10 0 0 0',
                            fieldLabel: 'Tipo',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'class_id',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            editable: false,
                            displayField: 'display_field',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'ArchiveClasses',
                            valueField: 'id',
                            listeners: {
                                change: {
                                    fn: me.onComboboxChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'container',
                            margin: '10 0 10 0',
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'image',
                                    height: 16,
                                    hidden: true,
                                    id: 'ArchiveArchiveActionIconL',
                                    itemId: 'ArchiveArchiveActionIconL',
                                    style: 'background-repeat: none;',
                                    width: 16,
                                    imgCls: 'icon-key'
                                },
                                {
                                    xtype: 'label',
                                    id: 'ArchiveArchiveActionLabel',
                                    itemId: 'ArchiveArchiveActionLabel',
                                    margin: '0 10 0 10'
                                },
                                {
                                    xtype: 'image',
                                    height: 16,
                                    hidden: true,
                                    id: 'ArchiveArchiveActionIconR',
                                    itemId: 'ArchiveArchiveActionIconR',
                                    style: 'background-repeat: none;',
                                    width: 16,
                                    imgCls: 'icon-key'
                                }
                            ]
                        },
                        {
                            xtype: 'radiogroup',
                            hidden: true,
                            id: 'ArchiveArchiveDestination',
                            itemId: 'ArchiveArchiveDestination',
                            margin: '10 0 0 0',
                            fieldLabel: 'Destinazione',
                            labelWidth: 120,
                            allowBlank: false,
                            items: [
                                {
                                    xtype: 'radiofield',
                                    name: 'destination',
                                    boxLabel: 'Locale',
                                    checked: true,
                                    inputValue: 'L'
                                },
                                {
                                    xtype: 'radiofield',
                                    name: 'destination',
                                    boxLabel: 'Cloud',
                                    inputValue: 'C'
                                }
                            ]
                        },
                        {
                            xtype: 'textfield',
                            id: 'ArchiveArchiveShortDescription',
                            itemId: 'ArchiveArchiveShortDescription',
                            margin: '10 0 0 0',
                            fieldLabel: 'Breve descrizione',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'short_description'
                        },
                        {
                            xtype: 'textareafield',
                            id: 'ArchiveArchiveDescription',
                            itemId: 'ArchiveArchiveDescription',
                            margin: '10 0 0 0',
                            fieldLabel: 'Descrizione',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'description'
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            id: 'ArchiveArchiveMetadata',
                            itemId: 'ArchiveArchiveMetadata',
                            margin: '10 0 0 0',
                            collapsible: true,
                            title: 'Dati',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    flex: 1,
                                    id: 'ArchiveArchiveMetadataGrid',
                                    itemId: 'ArchiveArchiveMetadataGrid',
                                    margin: '0 0 10 0',
                                    emptyText: 'Nessun metadata associato al tipo di documento selezionato.',
                                    enableColumnHide: false,
                                    enableColumnMove: false,
                                    sortableColumns: false,
                                    store: 'ArchiveClassMetadata',
                                    columns: [
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                metaData.tdAttr = 'data-qtip="' + record.get('description') + '"';
                                                return value;
                                            },
                                            width: 150,
                                            align: 'right',
                                            dataIndex: 'name',
                                            text: 'Nome'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            dataIndex: 'value',
                                            text: 'Valore',
                                            flex: 1,
                                            editor: {
                                                xtype: 'textfield',
                                                validator: function(value) {
                                                    //var record = Ext.getCmp('ArchiveUploadMetadataGrid').getSelectionModel().getSelected();//context.record;

                                                    //console.log('-------');
                                                    //console.log(editor);
                                                    //console.log(context);
                                                    //console.log(eOpts);
                                                    //console.log('-------');

                                                    // Check value against metadata type: DATE (yyy-mm-dd), Integer (> 0), String (No checks).
                                                    // Check for mandatory.
                                                    //if (record.get('kind') === 'D') {
                                                    //    var regex = /^(19|20)([0-9]{2})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
                                                    //    if (!regex.test(value)) {
                                                    //        return false;
                                                    //    }
                                                    //} else if (record.get('kind') === 'I') {
                                                    //    if (parseInt(value) < 0) {
                                                    //        //context.cancel = true;
                                                    //        return false;
                                                    //    }
                                                    //}
                                                    return true;
                                                }
                                            }
                                        }
                                    ],
                                    plugins: [
                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                            listeners: {
                                                validateedit: {
                                                    fn: me.onRowEditingValidateedit,
                                                    scope: me
                                                }
                                            }
                                        })
                                    ],
                                    features: [
                                        {
                                            ftype: 'grouping',
                                            collapsible: false,
                                            enableGroupingMenu: false,
                                            enableNoGroups: false,
                                            groupHeaderTpl: [
                                                '{name}'
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ArchiveArchiveArchiveUserId',
                            itemId: 'ArchiveArchiveArchiveUserId',
                            name: 'archive_user_id',
                            value: 3
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ArchiveArchiveReferenceYear',
                            itemId: 'ArchiveArchiveReferenceYear',
                            name: 'reference_year'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ArchiveArchiveId',
                            itemId: 'ArchiveArchiveId',
                            name: 'id'
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            fieldLabel: 'Label',
                            name: 'origin'
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onArchiveUploadWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {
        var metadataStore = Ext.getStore('ArchiveMetadata'),
            classStore = Ext.getStore('ArchiveClasses'),
            iconL = Ext.getCmp('ArchiveArchiveActionIconL'),
            iconR = Ext.getCmp('ArchiveArchiveActionIconR'),
            label = Ext.getCmp('ArchiveArchiveActionLabel'),
            radio = Ext.getCmp('ArchiveArchiveDestination'),
            year = Ext.getCmp('ArchiveArchiveReferenceYear'),
            classAction = classStore.getById(newValue).get('action'),
            classFormat = classStore.getById(newValue).get('format');

        if (classFormat !== 'PDF') {
            radio.setValue({destination: 'L'});
            radio.items.items[0].enable();
            radio.items.items[1].disable();
        } else if (classAction === 'A' || classAction === 'N') {
            radio.setValue({destination: 'L'});
            radio.items.items[0].enable();
            radio.items.items[1].enable();
        } else {
            radio.setValue({destination: 'C'});
            radio.items.items[0].disable();
            radio.items.items[1].enable();
        }

        if (classAction === 'C') {
            iconL.show();
            iconR.show();
            label.setText('Il documento verrà Archiviato e Conservato');
        } else if (classAction === 'A' || classAction === 'N') {
            iconL.hide();
            iconR.hide();
            label.setText('Il documento verrà Archiviato');
        } else {
            iconL.hide();
            iconR.hide();
            label.setText('Selezionare il tipo di documento da caricare');
        }

        year.setValue(new Date().getFullYear());

        if (newValue) {
            metadataStore.load({
                params: {
                    class_id: newValue
                }
            });
        }
    },

    onRowEditingValidateedit: function(editor, context, eOpts) {
        var record = context.record,
            newValue = context.newValues.value,
            oldValue = context.originalValues.value;

        // Check value against metadata type: DATE (yyy-mm-dd), Integer (> 0), String (No checks).
        // TODO Check for mandatory.
        if (record.get('kind') === 'D') {
            var regex = /^(19|20)([0-9]{2})-(0[1-9]|1[0-2])-(0[1-9]|[1-2][0-9]|3[0-1])$/;
            if (!regex.test(newValue)) {
                context.cancel();
                return false;
            }
        } else if (record.get('kind') === 'I') {
            if (parseInt(newValue) < 0) {
                context.cancel();
                return false;
            }
        }

        return true;
    },

    onArchiveUploadWinClose: function(panel, eOpts) {
        var classmetaStore = Ext.getStore('ArchiveClassMetadata');
        classmetaStore.removeAll();
    }

});