/*
 * File: app/view/ImportPaymentsEasyWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ImportPaymentsEasyWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ImportPaymentsEasyWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.File',
        'Ext.form.field.ComboBox',
        'Ext.button.Button',
        'Ext.form.FieldSet'
    ],

    height: 509,
    id: 'ImportPaymentsEasyWin',
    itemId: 'ImportPaymentsEasyWin',
    width: 584,
    layout: 'fit',
    title: 'Importazione da EASY dei pagamenti',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'EasyImportPaymentFrm',
                    itemId: 'EasyImportPaymentFrm',
                    bodyPadding: 10,
                    title: '',
                    method: 'POST',
                    url: '/mc2-api/ccp/import_easy_payment',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'filefield',
                            fieldLabel: '',
                            name: 'easyPaymentUpload',
                            emptyText: 'Caricare il file exportato da EASY dei pagamenti ...'
                        },
                        {
                            xtype: 'combobox',
                            fieldLabel: '',
                            name: 'bank_account',
                            emptyText: 'Selezionare banca ...',
                            displayField: 'denomination',
                            store: 'CoreBankAccounts',
                            valueField: 'id'
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {



                                        if (Ext.getCmp('EasyImportPaymentFrm').getForm().isValid()) {
                                            Ext.getCmp('EasyPaymentsErrorFs').removeAll();
                                            //Ext.getCmp('ImportPaymentsEasyWin').setLoading(true);

                                            Ext.getCmp('EasyImportPaymentFrm').submit({
                                                success: function (r, res) {
                                                    var importErrors = Ext.decode(res.response.responseText).errors,
                                                        lb;
                                                    Ext.getCmp('ImportPaymentsEasyWin').setLoading(false);
                                                    Ext.getStore('CcpMovements').load();
                                                    if (importErrors.length > 0 ) {

                                                        Ext.each(importErrors, function(e) {
                                                            lb = Ext.create('Ext.form.Label', {
                                                                text: e,
                                                                margin: '2 0'
                                                            });
                                                            Ext.getCmp('EasyPaymentsErrorFs').add(lb);
                                                        });

                                                    }
                                                }
                                            });
                                        }

                                    },
                                    text: 'Importa'
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            id: 'EasyPaymentsErrorFs',
                            itemId: 'EasyPaymentsErrorFs',
                            autoScroll: true,
                            collapsible: true,
                            title: 'Errori',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            }
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});