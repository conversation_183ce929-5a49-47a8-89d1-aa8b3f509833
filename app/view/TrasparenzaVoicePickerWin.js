/*
 * File: app/view/TrasparenzaVoicePickerWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TrasparenzaVoicePickerWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.TrasparenzaVoicePickerWin',

    requires: [
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.selection.CheckboxModel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 600,
    id: 'TrasparenzaVoicePickerWin',
    itemId: 'TrasparenzaVoicePickerWin',
    width: 400,
    title: 'Scegliere una voce di Trasparenza',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'treepanel',
                    flex: 1,
                    border: false,
                    id: 'TrasparenzaVoicePickerGrid',
                    itemId: 'TrasparenzaVoicePickerGrid',
                    store: 'TrasparenzaVoicesPickerTree',
                    rootVisible: false,
                    viewConfig: {

                    },
                    columns: [
                        {
                            xtype: 'treecolumn',
                            dataIndex: 'title',
                            text: 'Titolo',
                            flex: 1
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        mode: 'SIMPLE',
                        checkOnly: true
                    })
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var selections = Ext.getCmp('TrasparenzaVoicePickerGrid').getSelectionModel().getSelection(),
                                    doc = Ext.getCmp('TrasparenzaVoicePickerWin').record,
                                    to_link = [],
                                    files = Ext.getStore('ArchiveDocumentFiles').getRange(),
                                    record,
                                    store = Ext.getStore('TrasparenzaVoicesTree');


                                selections.forEach(function(sel){
                                    to_link = [];
                                    record = store.getById(sel.get('id'));
                                    record.get('linked_documents').forEach(function(val){
                                        to_link.push(val.id);
                                    });
                                    files.forEach(function(fileObj){
                                        to_link.push(fileObj.get('id'));
                                    });
                                    record.set('linked_documents', to_link);

                                });

                                store.sync({
                                    callback: function() {
                                        store.load();
                                        Ext.getStore('ArchiveDocumentsArchived').load();
                                        Ext.getCmp('TrasparenzaVoicePickerWin').close();
                                    },
                                    success: function(form, action) {
                                        Ext.Msg.alert('Successo', 'Documento allegato a Trasparenza');
                                        Ext.getStore('ArchiveDocumentsUser').load();
                                        Ext.getStore('ArchiveDocumentsOffice').load();
                                    },
                                    failure: function(form, action) {
                                        var res = form.proxy.getReader().jsonData;
                                        if(res.status === 0){
                                            Ext.Msg.alert('Attenzione', 'Documento NON allegato a Trasparenza');
                                        } else {
                                            Ext.Msg.alert('Attenzione', res.message);
                                        }
                                    }
                                });
                            },
                            iconCls: 'icon-attach',
                            text: 'Allega documento'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});