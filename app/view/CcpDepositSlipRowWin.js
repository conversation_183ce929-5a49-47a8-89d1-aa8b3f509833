/*
 * File: app/view/CcpDepositSlipRowWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpDepositSlipRowWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpDepositSlipRowWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.form.field.Text',
        'Ext.grid.Panel',
        'Ext.grid.column.Number',
        'Ext.grid.column.CheckColumn',
        'Ext.grid.column.Action',
        'Ext.grid.View'
    ],

    height: 600,
    id: 'CcpDepositSlipRowWin',
    itemId: 'CcpDepositSlipRowWin',
    width: '80%',
    layout: 'fit',
    title: 'Gestione righe distinta',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'textfield',
                            fieldLabel: '',
                            emptyText: 'Cerca ...',
                            listeners: {
                                change: {
                                    fn: me.onTextfieldChange,
                                    delay: 300,
                                    buffer: 300,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'CcpInvoiceDepositSlipGrd',
                    title: '',
                    store: 'CcpInvoiceDepositSlips',
                    columns: [
                        {
                            xtype: 'numbercolumn',
                            width: 44,
                            align: 'center',
                            dataIndex: 'row_number',
                            text: 'Riga',
                            format: '0'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'student',
                            text: 'Studente',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'accountholder',
                            text: 'Pagante',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'description',
                            text: 'Informazioni riga',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                return Ext.Date.format(record.get('unpaid_date'), "d-m-Y h:i") + ' ' + record.get('unpaid_note');
                            },
                            width: 164,
                            dataIndex: 'unpaid_note',
                            text: 'Nota',
                            flex: 1
                        },
                        {
                            xtype: 'numbercolumn',
                            width: 100,
                            align: 'right',
                            dataIndex: 'movements_total',
                            text: 'Movimenti'
                        },
                        {
                            xtype: 'numbercolumn',
                            width: 100,
                            align: 'right',
                            dataIndex: 'total',
                            text: 'Totale'
                        },
                        {
                            xtype: 'checkcolumn',
                            width: 50,
                            dataIndex: 'bollo',
                            text: 'Bollo',
                            listeners: {
                                checkchange: {
                                    fn: me.onCheckcolumnCheckChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 60,
                            align: 'center',
                            text: 'Stato',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if(r.get('unpaid_date')) {
                                            return 'icon-money_delete';
                                        }
                                        return 'icon-money';
                                    },
                                    iconCls: 'icon-delete'
                                }
                            ]
                        }
                    ],
                    viewConfig: {
                        listeners: {
                            itemclick: {
                                fn: me.onViewItemClick,
                                scope: me
                            }
                        }
                    }
                }
            ],
            listeners: {
                close: {
                    fn: me.onCcpDepositSlipRowWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onTextfieldChange: function(field, newValue, oldValue, eOpts) {
        var store = Ext.getStore('CcpInvoiceDepositSlips');

        store.removeFilter();

        newValue = newValue.toLowerCase();

        if(newValue) {
            store.filter(function(v) {
                return v.get('description').toLowerCase().indexOf(newValue)!== -1 ||
                    v.get('accountholder').toLowerCase().indexOf(newValue)!== -1 ||
                    v.get('student').toLowerCase().indexOf(newValue)!== -1;
            });
        }
    },

    onCheckcolumnCheckChange: function(checkcolumn, rowIndex, checked, eOpts) {
        var store = Ext.getStore('CcpInvoiceDepositSlips'),
            row = store.getAt(0);
        store.update();
    },

    onViewItemClick: function(dataview, record, item, index, e, eOpts) {
        if(record.get('unpaid_date')) {
            Ext.MessageBox.alert ({
                title: 'ANNULLAMENTO INSOLUTO',
                msg: 'Sicuro di voler annullare l\'insoluto?',
                buttons: Ext.MessageBox.YESNO,
                fn: function(res, text) {
                    if (res === 'yes') {
                        Ext.Ajax.request({
                            url: '/mc2-api/ccp/deposit_slip_unpaid',
                            params : {
                                row_number: record.get('row_number'),
                                deposit_slip: record.get('ccp_deposit_slip'),
                                restore: 1
                            },
                            method: 'POST',
                            success: function(res){
                                var r = Ext.decode(res.responseText);
                                if(r.success === true) {
                                    Ext.getCmp('CcpDepositSlipPnl').loadDepositSlipRows(record.get('ccp_deposit_slip'));
                                    Ext.getStore('CcpDepositSlips').load();
                                } else {
                                    Ext.Msg.alert('ERRORE', r.message);
                                }
                            }
                        });
                    }
                }
            });

        } else {
            Ext.MessageBox.prompt ({
                title: 'INSERIMENTO INSOLUTO',
                msg: 'E\' possibile inserire una nota. Si ricorda che l\'insoluto di una riga cancellerà anche i pagamenti relativi.',
                width:300,
                buttons: Ext.MessageBox.OKCANCEL,
                multiline: true,
                fn: function(res, text) {
                    if (res === 'ok') {
                        Ext.Ajax.request({
                            url: '/mc2-api/ccp/deposit_slip_unpaid',
                            params : {
                                row_number: record.get('row_number'),
                                deposit_slip: record.get('ccp_deposit_slip'),
                                note: text
                            },
                            method: 'POST',
                            success: function(){
                                Ext.getCmp('CcpDepositSlipPnl').loadDepositSlipRows(record.get('ccp_deposit_slip'));
                                Ext.getStore('CcpDepositSlips').load();
                            }
                        });
                    }
                }
            });
        }

    },

    onCcpDepositSlipRowWinClose: function(panel, eOpts) {
        Ext.getStore('CcpDepositSlips').load();
    }

});