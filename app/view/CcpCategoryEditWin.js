/*
 * File: app/view/CcpCategoryEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpCategoryEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpCategoryEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Text',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    id: 'CcpCategoryEditWin',
    itemId: 'CcpCategoryEditWin',
    width: 400,
    resizable: false,
    title: 'Categoria',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'CcpCategoryEditForm',
                    itemId: 'CcpCategoryEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'textfield',
                            id: 'CcpCategoryEditName',
                            itemId: 'CcpCategoryEditName',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            name: 'name',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'hiddenfield',
                            flex: 1,
                            id: 'CcpCategoryEditId',
                            itemId: 'CcpCategoryEditId',
                            fieldLabel: 'Label',
                            name: 'id'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var store = Ext.getStore('CcpCategories'),
                                            form = Ext.getCmp('CcpCategoryEditForm').getForm(),
                                            values = form.getValues(),
                                            a = 'salvata';

                                        // Update or Creation
                                        if (values.id) {
                                            a = 'aggiornata';
                                            record = store.getById(parseInt(values.id));
                                            record.set('name', values.name);
                                        } else {
                                            store.add({name: values.name});
                                        }

                                        store.sync({
                                            callback: function() {
                                                store.load();
                                            },
                                            success: function(form, action) {
                                                Ext.getCmp('CcpCategoryEditWin').close();
                                                Ext.Msg.alert('Successo', 'Categoria ' + a);
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Categoria NON ' + a);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});