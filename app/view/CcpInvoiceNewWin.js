/*
 * File: app/view/CcpInvoiceNewWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpInvoiceNewWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpInvoiceNewWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.form.field.TextArea',
        'Ext.grid.Panel',
        'Ext.grid.column.Number',
        'Ext.grid.column.Date',
        'Ext.grid.View',
        'Ext.toolbar.Spacer',
        'Ext.container.ButtonGroup',
        'Ext.form.CheckboxGroup',
        'Ext.selection.CheckboxModel'
    ],

    height: 600,
    id: 'CcpInvoiceNewWin',
    width: 800,
    title: 'Emissione fatture',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var // E abbastanza come controllo perchè tanto da qui non ci passi se il parametro è a true.. Carica un'altra finestra
                                invoiceEnabled = Ext.getCmp('CcpMainPnl').getActiveTab().id == 'CcpInvoicePanel',
                                values = Ext.getCmp('CcpInvoiceAddFrm').getValues(),
                                records = Ext.getCmp('CcpInvoiceovementsGrid').getSelectionModel().getSelection(),
                                invoiceRecords,
                                ids = [],
                                data = {};




                                Ext.getStore('CoreBankAccounts').load();

                                Ext.each(records, function(v) {
                                    ids.push(v.get('id'));
                                });

                                Ext.getCmp('CcpInvoiceNewWin').setLoading(true);

                                data =  {
                                    date: values.date,
                                    expiration_date: values.expiration_date,
                                    number: values.number,
                                    bank_id: values.bank_id,
                                    linked_movements: ids,
                                    multiple_expiration_dates: values.multiple_expiration_dates === '1' ? true : false
                                };

                                if(values.table_text_ch) {
                                    data.table_text = values.table_text;
                                }
                                if(values.expiration_text_ch) {
                                    data.expiration_text = values.expiration_text;
                                }


                                if(!invoiceEnabled) {

                                    invoiceRecords = Ext.getCmp('CcpInvoicesToDepositSlipGrid').getSelectionModel().getSelection();
                                    data.number = -1;
                                    data.multiple_expiration_dates=0;
                                    data.force_split=1;
                                    if(records.length === 0 && invoiceRecords.length === 0) {
                                        Ext.Msg.alert('ERRORE', 'Seleziona almeno un movimento o una fattura');
                                        Ext.getCmp('CcpInvoiceNewWin').setLoading(false);
                                        return;
                                    }



                                }
                                if(!values.number) {
                                    Ext.Msg.alert('ERRORE', 'Numero obbligatorio');
                                    Ext.getCmp('CcpInvoiceNewWin').setLoading(false);
                                    return;
                                }

                                if(records.length > 0) {
                                    if(records[0].get('incoming') === false) {
                                        data.credit_note=true;
                                    }
                                }

                                Ext.Ajax.request({
                                    url: '/mc2-api/ccp/invoice',
                                    method: 'POST',
                                    timeout:999999,
                                    jsonData: data,
                                    success: function(r) {
                                        var response = Ext.decode(r.responseText);
                                        if (response.success) {

                                            if(!invoiceEnabled) {
                                                var ids = response.results,
                                                    data =  {
                                                        number: values.number,
                                                        bank_account: values.bank_id,
                                                        // payment_method: values.payment_method,
                                                        invoices: ids,
                                                        sons_merge: parseInt(values.sons_merge),
                                                        school_year: values.school_year,
                                                        expiration_date_end: Ext.getCmp('CcpDepositSlipInvoiceExpirationEnd').getValue()
                                                        // group: values.group
                                                    };

                                                Ext.each(invoiceRecords, function(v){
                                                    ids.push(v.get('id'));
                                                });

                                                Ext.Ajax.request({
                                                    url: '/mc2-api/ccp/deposit_slip',
                                                    method: 'POST',
                                                    timeout:999999,
                                                    jsonData: data,
                                                    success: function(r) {
                                                        var response = Ext.decode(r.responseText);
                                                        if (response.success) {
                                                            //Ext.getCmp('CcpInvoiceNewWin').close();
                                                            Ext.getCmp('CcpDepositSlipNewWin').close();

                                                            Ext.getStore('CcpDepositSlips').load();
                                                        } else {
                                                            Ext.Msg.alert('ERRORE', response.message);
                                                            Ext.getCmp('CcpInvoiceNewWin').setLoading(false);

                                                        }
                                                    }
                                                });

                                            } else {
                                                // Siamo in fatture
                                                Ext.getCmp('CcpInvoiceNewWin').setLoading(false);
                                                Ext.getCmp('CcpInvoiceNewWin').close();
                                                Ext.getStore('CcpInvoices').load();
                                            }

                                        } else {
                                            Ext.Msg.alert('ERRORE', response.message);
                                            Ext.getCmp('CcpInvoiceNewWin').setLoading(false);
                                        }

                                    }
                                });
                            },
                            id: 'CcpInvoiceDefaultBtn',
                            itemId: 'CcpInvoiceDefaultBtn',
                            iconCls: 'icon-page',
                            text: 'Genera fattura'
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.widget('CcpInvoiceAccountHolderWin').show();
                            },
                            id: 'CcpInvoiceCustomBtn',
                            iconCls: 'icon-page_edit',
                            text: 'Genera fattura personalizzata'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                }
            },
            items: [
                {
                    xtype: 'form',
                    id: 'CcpInvoiceAddFrm',
                    itemId: 'CcpInvoiceAddFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            flex: 1,
                            id: 'CcpSchoolYearEmitInvoiceCmb',
                            fieldLabel: 'Anno scolastico',
                            labelWidth: 120,
                            name: 'school_year',
                            displayField: 'name',
                            store: 'McDbs',
                            valueField: 'name',
                            listeners: {
                                select: {
                                    fn: me.onComboboxSelect1,
                                    scope: me
                                },
                                afterrender: {
                                    fn: me.onComboboxAfterRender1,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'numberfield',
                            flex: 1,
                            id: 'CcpInvoiceNumber',
                            itemId: 'CcpInvoiceNumber',
                            fieldLabel: 'Numero partenza della fattura',
                            labelWidth: 123,
                            name: 'number',
                            hideTrigger: true
                        },
                        {
                            xtype: 'datefield',
                            flex: 1,
                            id: 'CcpInvoiceDate',
                            itemId: 'CcpInvoiceDate',
                            fieldLabel: 'Data fattura',
                            labelWidth: 123,
                            name: 'date',
                            format: 'd/m/Y',
                            submitFormat: 'c'
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            id: 'CcpInvoiceParamsText1Cnt',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    handler: function(checkbox, checked) {
                                        Ext.getCmp('CcpTableText').disable();
                                        if (checked === true) {
                                            Ext.getCmp('CcpTableText').enable();
                                        }
                                    },
                                    fieldLabel: '',
                                    name: 'table_text_ch',
                                    boxLabel: ''
                                },
                                {
                                    xtype: 'textareafield',
                                    flex: 1,
                                    disabled: true,
                                    id: 'CcpTableText',
                                    itemId: 'CcpTableText',
                                    padding: '0 10',
                                    fieldLabel: 'Testo tabella',
                                    name: 'table_text'
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            id: 'CcpInvoiceParamsText2Cnt',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    handler: function(checkbox, checked) {
                                        Ext.getCmp('CcpExpirationText').disable();
                                        if (checked === true) {
                                            Ext.getCmp('CcpExpirationText').enable();
                                        }
                                    },
                                    fieldLabel: '',
                                    name: 'expiration_text_ch',
                                    boxLabel: ''
                                },
                                {
                                    xtype: 'textareafield',
                                    flex: 1,
                                    disabled: true,
                                    id: 'CcpExpirationText',
                                    itemId: 'CcpExpirationText',
                                    padding: '0 10',
                                    fieldLabel: 'Testo scadenza',
                                    name: 'expiration_text'
                                }
                            ]
                        },
                        {
                            xtype: 'combobox',
                            flex: 1,
                            hidden: true,
                            id: 'CcpInvoiceBank',
                            fieldLabel: 'Banca',
                            labelWidth: 170,
                            displayField: 'denomination',
                            queryMode: 'local',
                            store: 'CoreBankAccounts',
                            valueField: 'id'
                        },
                        {
                            xtype: 'checkboxfield',
                            flex: 1,
                            id: 'CcpInvoiceMultipleExpirationDate',
                            fieldLabel: 'Forza fattura singola',
                            labelWidth: 125,
                            name: 'multiple_expiration_dates',
                            boxLabel: '<i>Unisce i movimenti con scadenze diverse e stesso pagante in un\'unica fattura</i>',
                            checked: true,
                            inputValue: '1',
                            uncheckedValue: '0'
                        },
                        {
                            xtype: 'combobox',
                            flex: 1,
                            id: 'CcpInvoiceCreateBankCmb',
                            fieldLabel: 'Banca',
                            labelWidth: 125,
                            name: 'bank_id',
                            allowBlank: false,
                            displayField: 'denomination',
                            multiSelect: true,
                            store: 'CoreBankAccounts',
                            valueField: 'id',
                            listeners: {
                                render: {
                                    fn: me.onComboboxRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'checkboxfield',
                            flex: 1,
                            id: 'CcpInvoiceSonsMergeCh',
                            fieldLabel: 'Raggruppa fatture fratelli',
                            labelWidth: 125,
                            name: 'sons_merge',
                            boxLabel: '',
                            checked: true,
                            inputValue: '1',
                            uncheckedValue: '0'
                        }
                    ]
                },
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'CcpInvoiceovementsGrid',
                    itemId: 'CcpInvoiceovementsGrid',
                    title: 'Elenco movimenti',
                    store: 'CcpInvoiceMovements',
                    columns: [
                        {
                            xtype: 'numbercolumn',
                            width: 60,
                            align: 'center',
                            dataIndex: 'number',
                            text: 'Numero',
                            format: '0'
                        },
                        {
                            xtype: 'datecolumn',
                            align: 'center',
                            dataIndex: 'expiration_date',
                            text: 'Scadenza',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'type_text',
                            text: 'Tipo movimento',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'subject_data',
                            text: 'Debitore',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'accountholder',
                            text: 'Intestatario',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                return Ext.util.Format.number(value, '0,000.00');
                            },
                            id: 'CcpInvoiceNewAmountCol',
                            align: 'right',
                            dataIndex: 'total',
                            text: 'Importo',
                            listeners: {
                                beforerender: {
                                    fn: me.onCcpInvoiceNewAmountColBeforeRender,
                                    scope: me
                                }
                            }
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            id: 'CcpInvoceToolBarIncomeTb',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'buttongroup',
                                    width: 205,
                                    title: '',
                                    columns: 2,
                                    items: [
                                        {
                                            xtype: 'button',
                                            width: 100,
                                            enableToggle: true,
                                            pressed: true,
                                            text: 'Fattura',
                                            toggleGroup: 'invoice',
                                            listeners: {
                                                toggle: {
                                                    fn: me.onButtonToggle,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'button',
                                            id: 'CcpInvoiceMovementNcBtn',
                                            width: 100,
                                            allowDepress: false,
                                            enableToggle: true,
                                            text: 'Nota di credito',
                                            toggleGroup: 'invoice',
                                            listeners: {
                                                toggle: {
                                                    fn: me.onButtonToggle1,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'combobox',
                                    id: 'CcpInvoiceSchoolAddress',
                                    fieldLabel: 'Indirizzo',
                                    labelWidth: 50,
                                    displayField: 'description',
                                    multiSelect: true,
                                    queryMode: 'local',
                                    store: 'Indirizzi',
                                    valueField: 'id',
                                    listeners: {
                                        select: {
                                            fn: me.onComboboxSelect,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'combobox',
                                    id: 'CcpInvoiceClass',
                                    fieldLabel: 'Classe',
                                    labelWidth: 50,
                                    displayField: 'display',
                                    multiSelect: true,
                                    queryMode: 'local',
                                    store: 'Classi',
                                    valueField: 'id',
                                    listeners: {
                                        select: {
                                            fn: me.onCcpInvoiceClassSelect,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'datefield',
                                    id: 'CcpInvoiceDateFrom',
                                    itemId: 'CcpInvoiceDateFrom',
                                    width: 150,
                                    fieldLabel: 'Da',
                                    labelWidth: 15,
                                    allowBlank: false,
                                    format: 'd/m/Y',
                                    submitFormat: 'c',
                                    listeners: {
                                        change: {
                                            fn: me.onCcpInvoiceDateFromChange,
                                            delay: 300,
                                            buffer: 300,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'datefield',
                                    id: 'CcpInvoiceDateTo',
                                    itemId: 'CcpInvoiceDateTo',
                                    width: 150,
                                    fieldLabel: 'A',
                                    labelWidth: 15,
                                    allowBlank: false,
                                    format: 'd/m/Y',
                                    submitFormat: 'c',
                                    listeners: {
                                        change: {
                                            fn: me.onCcpInvoiceDateToChange,
                                            delay: 300,
                                            buffer: 300,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'CcpInvoiceAccountHolderSerach',
                                    itemId: 'CcpInvoiceAccountHolderSerach',
                                    width: 100,
                                    fieldLabel: '',
                                    emptyText: 'Intestatario/Debitore ...',
                                    listeners: {
                                        change: {
                                            fn: me.onCcpInvoiceAccountHolderSerachChange,
                                            delay: 300,
                                            buffer: 300,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'combobox',
                                    id: 'CcpInvoiceMovFilterStudentState',
                                    fieldLabel: 'Stato studente',
                                    name: 'student_state',
                                    editable: false,
                                    displayField: 'descrizione',
                                    forceSelection: true,
                                    store: 'StudentStates',
                                    valueField: 'id_stato_studente_personalizzato',
                                    listeners: {
                                        select: {
                                            fn: me.onComboboxSelect3,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'combobox',
                                    id: 'CcpInvoicePaymentMethod',
                                    fieldLabel: '',
                                    emptyText: 'Tipo addebito',
                                    displayField: 'name',
                                    store: 'CcpPaymentMethods',
                                    valueField: 'id',
                                    listeners: {
                                        select: {
                                            fn: me.onCcpInvoicePaymentMethodSelect,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'CcpInvoiceTypeId',
                                    fieldLabel: '',
                                    name: 'type_id',
                                    emptyText: 'Tipi movimento',
                                    displayField: 'name_school_year',
                                    multiSelect: true,
                                    store: 'CcpTypesFilter',
                                    valueField: 'id',
                                    listeners: {
                                        select: {
                                            fn: me.onComboboxSelect2,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'checkboxgroup',
                                    width: 200,
                                    fieldLabel: '',
                                    items: [
                                        {
                                            xtype: 'checkboxfield',
                                            id: 'CcpInvoiceMovFilterPaid',
                                            boxLabel: 'Pagati',
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpInvoiceMovFilterPaidChange,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'checkboxfield',
                                            id: 'CcpInvoiceMovFilterNotPaid',
                                            value: 1,
                                            boxLabel: 'Non pagati',
                                            inputValue: '1',
                                            uncheckedValue: '0',
                                            listeners: {
                                                change: {
                                                    fn: me.onCcpInvoiceMovFilterNotPaidChange,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ]
                                }
                            ]
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        listeners: {
                            selectionchange: {
                                fn: me.onCheckboxModelSelectionChange,
                                scope: me
                            }
                        }
                    })
                }
            ]
        });

        me.callParent(arguments);
    },

    onWindowShow: function(component, eOpts) {
        var invoiceEnabled = mc2ui.app.settings.invoiceEnabled;

        if(!mc2ui.app.settings.invoiceEnabled) {
            invoiceEnabled = Ext.getCmp('CcpMainPnl').getActiveTab().id == 'CcpInvoicePanel';
        }

        Ext.getStore('CcpInvoiceMovements').removeAll();

        if(invoiceEnabled) {

            Ext.Ajax.request({
                url: '/mc2-api/ccp/invoice_params',
                success: function(r) {
                    var res = Ext.decode(r.responseText).results;

                    Ext.each(res, function(val){
                        if(val.name === 'CCP_EXPIRATION_TEXT') {
                            Ext.getCmp('CcpExpirationText').setValue(val.value);
                        }
                        if(val.name === 'CCP_TABLE_TEXT') {
                            Ext.getCmp('CcpTableText').setValue(val.value);
                        }
                    });
                }
            });

            Ext.Ajax.request({
                url: '/mc2-api/ccp/next_invoice_number',
                success: function(r) {
                    var number = Ext.decode(r.responseText).results;
                    Ext.getCmp('CcpInvoiceNumber').setValue(number);
                }
            });
            Ext.getCmp('CcpInvoiceSonsMergeCh').hide();
        } else {
            Ext.getCmp('CcpInvoiceParamsText1Cnt').hide();
            Ext.getCmp('CcpInvoiceParamsText2Cnt').hide();
            Ext.getCmp('CcpInvoiceDate').hide();
            Ext.getCmp('CcpInvoiceMultipleExpirationDate').hide();
            Ext.getCmp('CcpInvoceToolBarIncomeTb').hide();
            Ext.getCmp('CcpInvoiceCustomBtn').hide();

            Ext.getCmp('CcpInvoiceNewWin').setTitle('Generazione distinta');
            Ext.getCmp('CcpInvoiceDefaultBtn').setText('Genera distinta');
            Ext.getCmp('CcpInvoiceNumber').setFieldLabel("Numero");
            Ext.getCmp('CcpInvoiceCreateBankCmb').setFieldLabel("Conto corrente di accredito");
            Ext.getCmp('CcpInvoicePaymentMethod').select(9); //SEPA
            Ext.getCmp('CcpInvoicePaymentMethod').hide(); //SEPA

        }

        var date = new Date();
        Ext.getCmp('CcpInvoiceDate').setValue(date);
        // Ext.getCmp('CcpInvoiceExpirationDate').setValue(date);

        date.setDate(1);
        Ext.getCmp('CcpInvoiceDateFrom').setValue(date);
        date.setMonth(date.getMonth()+1);
        date.setDate(date.getDate()-1);
        Ext.getCmp('CcpInvoiceDateTo').setValue(date);



        //Ext.getStore('CcpInvoiceMovements').load();

        Ext.getStore('CoreBankAccounts').load({
            callback: function(){
                var id = null;
                Ext.each(Ext.getStore('CoreBankAccounts').getRange(), function(v){
                    if (v.get('invoice_default') === true) {
                        id = v.get('id');
                    }
                });
                if(id !== null) Ext.getCmp('CcpInvoiceBank').setValue(id);
            }
        });

    },

    onComboboxSelect1: function(combo, records, eOpts) {
        Ext.getCmp('CcpInvoiceSchoolAddress').setValue();
        Ext.getCmp('CcpInvoiceClass').setValue();
        Ext.getStore('Indirizzi').load({params: {subject_school_year: records[0].get('name')}});
        Ext.getStore('Classi').load({params: {school_year: records[0].get('name')}});

    },

    onComboboxAfterRender1: function(component, eOpts) {
        Ext.getStore('McDbs').filterBy(function(v){
            return v.get('current')===true;
        });
        var year = Ext.getStore('McDbs').getRange()[0];
        Ext.getStore('McDbs').clearFilter();
        if(year) {
            component.select(year);


        }
    },

    onComboboxRender: function(component, eOpts) {
        Ext.Ajax.request({
            url: '/mc2-api/core/bank_account',
            method: 'GET',
            params: {
                invoice_default: true
            },
            success: function(res) {
                var r=Ext.decode(res.responseText);
                if(r.success===true) {
                    var bankId = r.results[0].id;
                    component.setValue(bankId);
                }
            }
        });

        if(!mc2ui.app.settings.invoiceEnabled) {
            var combo = Ext.getCmp('CcpInvoiceCreateBankCmb');
            combo.multiSelect = false;
            combo.clearValue();
            combo.getPicker().getSelectionModel().setSelectionMode('SINGLE');
        }
    },

    onCcpInvoiceNewAmountColBeforeRender: function(component, eOpts) {
        if (Ext.getCmp('CcpMainPnl').getActiveTab().id != 'CcpInvoicePanel') component.dataIndex = 'remaining';
    },

    onButtonToggle: function(button, pressed, eOpts) {
        if (pressed) Ext.getStore('CcpInvoiceMovements').load();
    },

    onButtonToggle1: function(button, pressed, eOpts) {
        if(pressed) Ext.getStore('CcpInvoiceMovements').load({params: {credit:0, debit:1}});
    },

    onComboboxSelect: function(combo, records, eOpts) {
        var r = records[0],
            address_ids=[];

        Ext.getStore('Classi').removeAll();

        Ext.each(records, function(r){
            address_ids.push(r.get('id'));
        });

        Ext.getStore('Classi').load({
            params: {
                "school_address_id[]": address_ids,
                school_year: Ext.getCmp('CcpSchoolYearEmitInvoiceCmb').getValue()
            }
        });

        if(combo.getValue()) Ext.getStore('CcpInvoiceMovements').load();
        Ext.getCmp('CcpInvoiceClass').setValue();
    },

    onCcpInvoiceClassSelect: function(combo, records, eOpts) {
        if(combo.getValue()) Ext.getStore('CcpInvoiceMovements').load();
    },

    onCcpInvoiceDateFromChange: function(field, newValue, oldValue, eOpts) {
        if(Ext.getCmp('CcpInvoiceDateTo').getValue()) Ext.getStore('CcpInvoiceMovements').load();
    },

    onCcpInvoiceDateToChange: function(field, newValue, oldValue, eOpts) {
        if(Ext.getCmp('CcpInvoiceDateFrom').getValue()) Ext.getStore('CcpInvoiceMovements').load();
    },

    onCcpInvoiceAccountHolderSerachChange: function(field, newValue, oldValue, eOpts) {
        Ext.getStore('CcpInvoiceMovements').load();
    },

    onComboboxSelect3: function(combo, records, eOpts) {
        Ext.getStore('CcpInvoiceMovements').load();
    },

    onCcpInvoicePaymentMethodSelect: function(combo, records, eOpts) {
        if(combo.getValue()) Ext.getStore('CcpInvoiceMovements').load();
    },

    onComboboxSelect2: function(combo, records, eOpts) {
        Ext.getStore('CcpInvoiceMovements').load();
    },

    onCcpInvoiceMovFilterPaidChange: function(field, newValue, oldValue, eOpts) {
        Ext.getStore('CcpInvoiceMovements').load();
    },

    onCcpInvoiceMovFilterNotPaidChange: function(field, newValue, oldValue, eOpts) {
        Ext.getStore('CcpInvoiceMovements').load();
    },

    onCheckboxModelSelectionChange: function(model, selected, eOpts) {
        var hideDefBtn = false;
        Ext.each(selected, function(v){
            if (v.get('subject_type') != 'S') {
                hideDefBtn = true;
            }
        });

        if(hideDefBtn) Ext.getCmp('CcpInvoiceDefaultBtn').hide();
        else Ext.getCmp('CcpInvoiceDefaultBtn').show();

    }

});