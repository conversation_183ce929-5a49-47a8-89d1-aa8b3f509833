/*
 * File: app/view/CcpDichiarazionePrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpDichiarazionePrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpDichiarazionePrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Date',
        'Ext.form.FieldSet',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    id: 'CcpDichiarazionePrintWin',
    width: 414,
    layout: 'fit',
    title: 'Stampa dichiarazione',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpDichiarazionePrintFrm',
                    bodyPadding: 10,
                    title: '',
                    items: [
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            id: 'CcpDeclarationSchoolYearCmb',
                            fieldLabel: 'Anno scolastico',
                            name: 'subject_school_year',
                            emptyText: 'Anno scolastico corrente ...',
                            displayField: 'name',
                            store: 'McDbs',
                            valueField: 'name',
                            listeners: {
                                select: {
                                    fn: me.onCcpDeclarationSchoolYearCmbSelect,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            fieldLabel: 'Studente',
                            name: 'subject_id',
                            allowBlank: false,
                            emptyText: 'Cerca ...',
                            hideTrigger: true,
                            displayField: 'display',
                            queryMode: 'local',
                            store: 'CcpSubjects',
                            typeAhead: true,
                            valueField: 'db_id',
                            listeners: {
                                change: {
                                    fn: me.onComboboxChange,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            fieldLabel: 'Data emissione',
                            name: 'creation_date',
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d',
                            listeners: {
                                render: {
                                    fn: me.onDatefieldRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'fieldset',
                            title: 'Periodo',
                            items: [
                                {
                                    xtype: 'datefield',
                                    anchor: '100%',
                                    fieldLabel: 'Inizio',
                                    name: 'start_date',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d'
                                },
                                {
                                    xtype: 'datefield',
                                    anchor: '100%',
                                    fieldLabel: 'Fine',
                                    name: 'end_date',
                                    format: 'd/m/Y',
                                    submitFormat: 'Y-m-d'
                                }
                            ]
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'bottom',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var filter = Ext.getCmp('CcpDichiarazionePrintFrm').getForm().getValues();

                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/print',
                                            params: {
                                                newSpool: 1,
                                                print: mc2ui.app.settings.prints.declaration_invoice,
                                                namespace: 'CCP',
                                                type: 'PDF',
                                                mime: 'application/pdf',
                                                subject_id: filter.subject_id,
                                                creation_date: filter.creation_date,
                                                start_date: filter.start_date,
                                                end_date: filter.end_date,
                                                subject_school_year: filter.subject_school_year
                                            },
                                            success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                                Ext.getCmp('CcpDichiarazionePrintWin').close();
                                            }
                                        });
                                    },
                                    text: 'Stampa'
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpDeclarationSchoolYearCmbSelect: function(combo, records, eOpts) {
        Ext.getStore('CcpSubjects').load({params: {subject_school_year: records[0].get('name')}});
    },

    onComboboxChange: function(field, newValue, oldValue, eOpts) {
        var store = field.store;

        store.clearFilter();
        store.filter({
            property: 'display',
            anyMatch: true,
            value   : field.getValue(),
            caseSensitive: false
        });
    },

    onDatefieldRender: function(component, eOpts) {
        component.setValue(new Date());
    }

});