/*
 * File: app/view/CcpInvoiceAccountHolderWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpInvoiceAccountHolderWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpInvoiceAccountHolderWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Date',
        'Ext.form.field.Checkbox',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    id: 'CcpInvoiceAccountHolderWin',
    itemId: 'CcpInvoiceAccountHolderWin',
    width: 847,
    layout: 'fit',
    title: 'Intestatario fattura',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    layout: {
                        type: 'hbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'form',
                            flex: 1,
                            id: 'CcpInvoiceAccountholderFrm',
                            itemId: 'CcpInvoiceAccountholderFrm',
                            width: 412,
                            bodyPadding: 10,
                            title: 'Pagante',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Cognome',
                                    name: 'surname'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Nome',
                                    name: 'name'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Codice fiscale',
                                    name: 'fiscal_code'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Email',
                                    name: 'email'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'P. IVA',
                                    name: 'piva'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'IBAN',
                                    name: 'iban'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Codice SDI',
                                    name: 'sdi_code'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Indirizzo',
                                    name: 'address'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Città',
                                    name: 'city'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'Provincia',
                                    name: 'province'
                                },
                                {
                                    xtype: 'textfield',
                                    fieldLabel: 'CAP',
                                    name: 'zip_code'
                                },
                                {
                                    xtype: 'textfield',
                                    hidden: true,
                                    fieldLabel: 'Rif. Mandato',
                                    name: 'codice_rid'
                                },
                                {
                                    xtype: 'datefield',
                                    flex: 1,
                                    hidden: true,
                                    fieldLabel: 'Data mand. RID',
                                    name: 'data_mandato_rid',
                                    format: 'd/m/Y',
                                    submitFormat: 'c'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    hidden: true,
                                    fieldLabel: 'Primo invio SEPA',
                                    name: 'first_sepa',
                                    boxLabel: '',
                                    inputValue: 'SI',
                                    uncheckedValue: 'NO'
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    fieldLabel: 'Tipo di addebito',
                                    name: 'payment_method',
                                    displayField: 'name',
                                    queryMode: 'local',
                                    store: 'CcpPaymentMethodsFilter',
                                    valueField: 'id'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    flex: 1,
                                    fieldLabel: 'Label',
                                    name: 'type',
                                    value: 'O'
                                },
                                {
                                    xtype: 'hiddenfield',
                                    flex: 1,
                                    fieldLabel: 'Label',
                                    name: 'subject_id'
                                }
                            ],
                            dockedItems: [
                                {
                                    xtype: 'toolbar',
                                    flex: 1,
                                    dock: 'top',
                                    items: [
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        },
                                        {
                                            xtype: 'button',
                                            handler: function(button, e) {
                                                var subjectId = Ext.getCmp('CcpInvoiceAccountholderFrm').getForm().getValues().subject_id;

                                                Ext.Ajax.request({
                                                    method: 'GET',
                                                    url: '/mc2-api/ccp/parents?student_id='+subjectId+'&only_holder=1',
                                                    success: function (r) {
                                                        var res = Ext.decode(r.responseText);
                                                        if(res.success === true) {
                                                            var data = res.results[0],
                                                                dd = new Date(1970,0,1);

                                                            dd.setSeconds(data.data_mandato_rid+60*60*3);
                                                            // data.data_mandato_rid = Ext.Date.format(dd, 'Y-m-d');
                                                            data.tipo_addebito = parseInt(data.tipo_addebito);
                                                            Ext.getCmp('CcpInvoiceAccountholderFrm').getForm().setValues(data);
                                                        }
                                                    }
                                                });
                                            },
                                            text: 'Imposta dati aggiornati'
                                        },
                                        {
                                            xtype: 'tbspacer',
                                            flex: 1
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'form',
                            flex: 1,
                            id: 'CcpInvoiceHeaderFrm',
                            bodyPadding: 10,
                            title: 'Intestatario',
                            items: [
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Cognome',
                                    name: 'surname'
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Nome',
                                    name: 'name'
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Codice fiscale',
                                    name: 'fiscal_code'
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Indirizzo',
                                    name: 'address'
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Città',
                                    name: 'city'
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'Provincia',
                                    name: 'province'
                                },
                                {
                                    xtype: 'textfield',
                                    anchor: '100%',
                                    fieldLabel: 'CAP',
                                    name: 'zip_code'
                                }
                            ]
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'bottom',
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var values = Ext.getCmp('CcpInvoiceAddFrm').getValues(),
                                            accountHolder = Ext.getCmp('CcpInvoiceAccountholderFrm').getValues(),
                                            records = Ext.getCmp('CcpInvoiceovementsGrid').getSelectionModel().getSelection(),
                                            ids = [],
                                            data = {};

                                        Ext.each(records, function(v) {
                                            ids.push(v.get('id'));
                                        });

                                        Ext.getStore('CoreBankAccounts').load();

                                        data =  {
                                            date: values.date,
                                            expiration_date: values.expiration_date,
                                            number: values.number,
                                            bank_id: values.bank_id,
                                            linked_movements: ids,
                                            accountholder: accountHolder
                                        };

                                        if(values.table_text_ch) {
                                            data.table_text = values.table_text;
                                        }
                                        if(values.expiration_text_ch) {
                                            data.expiration_text = values.expiration_text;
                                        }

                                        if(records.length > 0) {
                                            if(records[0].get('incoming') === false) {
                                                data.credit_note=true;
                                            }
                                        }

                                        Ext.Ajax.request({
                                            url: '/mc2-api/ccp/invoice',
                                            method: 'POST',
                                            jsonData: data,
                                            success: function(r) {
                                                var response = Ext.decode(r.responseText);
                                                if (response.success) {
                                                    Ext.getCmp('CcpInvoiceNewWin').close();
                                                    Ext.getCmp('CcpInvoiceAccountHolderWin').close();
                                                    Ext.getStore('CcpInvoices').load();
                                                } else {
                                                    Ext.Msg.alert('ERRORE', response.message);
                                                }
                                            }
                                        });
                                    },
                                    id: 'CcpInvoiceGenerationBtn',
                                    itemId: 'CcpInvoiceGenerationBtn',
                                    text: 'Genera fattura'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var id = Ext.getCmp('CcpInvoiceAccountHolderWin').recordId,
                                            accountHolder = Ext.getCmp('CcpInvoiceAccountholderFrm').getValues(),
                                            header = Ext.getCmp('CcpInvoiceHeaderFrm').getValues();

                                        data =  {
                                            account_holder: [accountHolder],
                                            header: header,
                                            payment_method: accountHolder.payment_method
                                        };

                                        Ext.Ajax.request({
                                            url: '/mc2-api/ccp/invoice/' + id,
                                            method: 'PUT',
                                            jsonData: data,
                                            success: function(r) {
                                                var response = Ext.decode(r.responseText);
                                                if (response.success) {
                                                    Ext.getCmp('CcpInvoiceAccountHolderWin').close();
                                                    Ext.getStore('CcpInvoices').load();
                                                } else {
                                                    Ext.Msg.alert('ERRORE', response.message);
                                                }
                                            }
                                        });
                                    },
                                    hidden: true,
                                    id: 'CcpInvoiceUpdateAccountHolderBtn',
                                    itemId: 'CcpInvoiceUpdateAccountHolderBtn',
                                    text: 'Salva'
                                }
                            ]
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});