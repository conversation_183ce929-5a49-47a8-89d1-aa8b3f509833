/*
 * File: app/view/ProtocolCorrespondentsWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolCorrespondentsWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolCorrespondentsWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.toolbar.Paging',
        'Ext.grid.feature.Grouping',
        'Ext.XTemplate',
        'Ext.button.Button',
        'Ext.toolbar.Fill',
        'Ext.form.field.Text',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 500,
    id: 'ProtocolCorrespondentsWin',
    itemId: 'ProtocolCorrespondentsWin',
    width: 700,
    title: 'Mittenti / Destinatari',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function () {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    permissible: true,
                    flex: 1,
                    border: false,
                    id: 'ProtocolCorrespondentsGrid',
                    itemId: 'ProtocolCorrespondentsGrid',
                    emptyText: 'Nessun Corrispondente caricato.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    sortableColumns: false,
                    store: 'ProtocolCorrespondents',
                    viewConfig: {
                        listeners: {
                            groupclick: function (view, groupField, groupValue, eOpts) {
                                // Previene il toggle dei gruppi (rimangono sempre espansi)
                                return false;
                            }
                        }
                    },
                    cls: 'protocol-correspondents-grid',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'title',
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'phone',
                            text: 'Telefono'
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 200,
                            dataIndex: 'email',
                            text: 'Email'
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 40,
                            items: [
                                {
                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                        var legal = record.get('legal_person') ? 'Giuridica' : 'Fisica',
                                            msg = '';

                                        msg = '<div style="display: table">' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Persona:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + legal + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Categoria:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('correspondent_type_text') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Codice Fiscale / P.IVA:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('fiscal_code') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Indirizzo:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('address') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>CAP:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('zipcode') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Città:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('city_name') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Telefono:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('phone') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Fax:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('fax') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Cellulare:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('mobile') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Email:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('email') + '</div>' +
                                            '</div>' +
                                            '<div style="display: table-row">' +
                                            '<div style="display: table-cell"><b>Sito web:&nbsp;</b></div>' +
                                            '<div style="display: table-cell">' + record.get('web') + '</div>' +
                                            '</div>' +
                                            '</div>';
                                        Ext.Msg.alert('Dettagli "' + record.get('title') + '"', msg);
                                    },
                                    iconCls: 'icon-information',
                                    tooltip: 'Dettagli'
                                },
                                {
                                    handler: function (view, rowIndex, colIndex, item, e, record, row) {
                                        Ext.Msg.alert('Note "' + record.get('title') + '"', record.get('note'));
                                    },
                                    getClass: function (v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('note') !== '') {
                                            return 'icon-note';
                                        }
                                    },
                                    getTip: function (v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('note') !== '') {
                                            return 'Note';
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    listeners: {
                        itemcontextmenu: {
                            fn: me.onProtocolCorrespondentsGridItemContextMenu,
                            scope: me
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'pagingtoolbar',
                            dock: 'bottom',
                            displayInfo: true,
                            store: 'ProtocolCorrespondents'
                        },
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function (button, e) {
                                        Ext.widget('ProtocolCorrespondentEditWin').show();
                                    },
                                    id: 'ProtocolCorrespondentNewBtn',
                                    itemId: 'ProtocolCorrespondentNewBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                },
                                {
                                    xtype: 'tbfill'
                                },
                                {
                                    xtype: 'textfield',
                                    id: 'ProtocolCorrespondentsFilterTitle',
                                    itemId: 'ProtocolCorrespondentsFilterTitle',
                                    checkChangeBuffer: 500,
                                    emptyText: 'Ricerca',
                                    listeners: {
                                        change: {
                                            fn: me.onProtocolCorrespondentsFilterTitleChange,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ],
                    features: [
                        {
                            ftype: 'grouping',
                            enableGroupingMenu: false,
                            enableNoGroups: false,
                            groupHeaderTpl: [
                                '{name} ({rows.length})'
                            ],
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    flex: 1,
                    hidden: true,
                    id: 'ProtocolCorrespondentsEditMn',
                    itemId: 'ProtocolCorrespondentsEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function (item, e) {
                                var pg = Ext.getCmp('ProtocolCorrespondentsGrid'),
                                    record = pg.getSelectionModel().getSelection()[0],
                                    type = '',
                                    legal = '',
                                    l = record.get('legal_person');

                                Ext.widget('ProtocolCorrespondentEditWin').show();

                                Ext.getCmp('ProtocolCorrespondentEditForm').getForm().loadRecord(record);

                                if (l) {
                                    legal = 'on';
                                } else {
                                    legal = 'off';
                                }

                                Ext.getCmp('ProtocolCorrespondentEditLegalPerson').setValue({ legal_person: legal });
                            },
                            id: 'contextProtocolCorrespondentEdit',
                            itemId: 'contextProtocolCorrespondentEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica'
                        },
                        {
                            xtype: 'menuitem',
                            handler: function (item, e) {
                                var record = Ext.getCmp('ProtocolCorrespondentsGrid').getSelectionModel().getSelection()[0];

                                Ext.Msg.show({
                                    title: record.get('denomination'),
                                    msg: 'Sei sicuro di voler eliminare questo Corrispondente?',
                                    buttons: Ext.Msg.YESNO,
                                    fn: function (r) {
                                        if (r == 'yes') {
                                            store = Ext.getStore('ProtocolCorrespondents');
                                            store.remove(record);
                                            store.sync({
                                                callback: function () {
                                                    store.load();
                                                },
                                                success: function () {
                                                    Ext.Msg.alert('Successo', 'Mittente / Destinatario eliminato');
                                                },
                                                failure: function () {
                                                    Ext.Msg.alert('Attenzione', 'Mittente / Destinatario NON eliminato');
                                                }
                                            });
                                        }
                                    }
                                });
                            },
                            id: 'contextProtocolCorrespondentDelete',
                            itemId: 'contextProtocolCorrespondentDelete',
                            iconCls: 'icon-cancel',
                            text: 'Elimina'
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onProtocolCorrespondentsWinClose,
                    scope: me
                },
                boxready: {
                    fn: me.onProtocolCorrespondentsWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onProtocolCorrespondentsGridItemContextMenu: function (dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        var menu = Ext.getCmp('ProtocolCorrespondentsEditMn');
        menu.showAt([newX, newY]);

        // Already in use correspondents cannot be deleted
        if (record.get('locked')) {
            menu.items.get('contextProtocolCorrespondentDelete').setDisabled(true);
        } else {
            menu.items.get('contextProtocolCorrespondentDelete').setDisabled(false);
        }
    },

    onProtocolCorrespondentsFilterTitleChange: function (field, newValue, oldValue, eOpts) {
        var store = Ext.getStore('ProtocolCorrespondents');

        store.clearFilter(true);

        if (newValue) {
            store.filter("title", newValue);
        } else {
            store.clearFilter();
        }
    },

    onProtocolCorrespondentsWinClose: function (panel, eOpts) {
        Ext.getStore('ProtocolCorrespondents').clearFilter();
    },

    onProtocolCorrespondentsWinBoxReady: function (component, width, height, eOpts) {
        Ext.getStore('CoreCities').load();
    }

});