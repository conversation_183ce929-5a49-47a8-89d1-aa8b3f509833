/*
 * File: app/view/ArchiveClassWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveClassWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveClassWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.column.Action',
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.toolbar.Spacer',
        'Ext.form.field.ComboBox',
        'Ext.form.FieldSet',
        'Ext.grid.column.Number',
        'Ext.form.field.Checkbox',
        'Ext.grid.plugin.DragDrop',
        'Ext.util.Point',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 491,
    width: 913,
    title: 'Tipi di flusso',
    modal: true,

    layout: {
        type: 'hbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    id: 'ArchiveClassGrid',
                    itemId: 'ArchiveClassGrid',
                    width: 300,
                    title: '',
                    hideHeaders: true,
                    store: 'ArchiveClasses',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'String',
                            flex: 1,
                            editor: {
                                xtype: 'textfield',
                                name: 'name'
                            }
                        },
                        {
                            xtype: 'actioncolumn',
                            width: 40,
                            align: 'center',
                            dataIndex: 'editable',
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if(v === false){
                                            return 'icon-lock';
                                        }
                                        return '';
                                    }
                                }
                            ]
                        }
                    ],
                    viewConfig: {
                        listeners: {
                            itemclick: {
                                fn: me.onViewItemClick,
                                scope: me
                            },
                            itemcontextmenu: {
                                fn: me.onViewItemContextMenu,
                                scope: me
                            }
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('ArchiveClassGrid').getSelectionModel().deselectAll();
                                        Ext.getCmp('ArchiveClassFrm').disable();
                                        Ext.getStore('ArchiveClassSteps').removeAll();

                                        Ext.getStore('ArchiveClasses').insert(0, {editable: true});
                                        Ext.getCmp('ArchiveClassGrid').editingPlugin.startEdit(0);

                                    },
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                },
                                beforeedit: {
                                    fn: me.onRowEditingBeforeEdit,
                                    scope: me
                                },
                                canceledit: {
                                    fn: me.onRowEditingCanceledit,
                                    scope: me
                                }
                            }
                        })
                    ]
                },
                {
                    xtype: 'form',
                    flex: 1,
                    disabled: true,
                    id: 'ArchiveClassFrm',
                    itemId: 'ArchiveClassFrm',
                    bodyPadding: 10,
                    title: '',
                    layout: {
                        type: 'vbox',
                        align: 'stretch',
                        pack: 'center'
                    },
                    dockedItems: [
                        {
                            xtype: 'hiddenfield',
                            dock: 'left',
                            width: 100,
                            fieldLabel: 'Label',
                            name: 'id'
                        }
                    ],
                    items: [
                        {
                            xtype: 'container',
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'textfield',
                                    flex: 1,
                                    id: 'ArchiveClassNameTxt',
                                    name: 'name',
                                    emptyText: 'Nome ..'
                                },
                                {
                                    xtype: 'tbspacer'
                                },
                                {
                                    xtype: 'combobox',
                                    hidden: true,
                                    fieldLabel: 'Classe conservazione',
                                    labelWidth: 130,
                                    name: 'code',
                                    displayField: 'name',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'RemoteClass',
                                    valueField: 'code'
                                }
                            ]
                        },
                        {
                            xtype: 'fieldset',
                            flex: 1,
                            layout: 'fit',
                            title: 'Flusso',
                            items: [
                                {
                                    xtype: 'gridpanel',
                                    id: 'ArchiveClassStepsGrid',
                                    itemId: 'ArchiveClassStepsGrid',
                                    store: 'ArchiveClassSteps',
                                    columns: [
                                        {
                                            xtype: 'numbercolumn',
                                            hidden: true,
                                            dataIndex: 'user_id',
                                            text: 'MyNumberColumn'
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                /*if(!isNaN(value)){
                                                if(parseInt(value) > 0){
                                                console.log(record);
                                                Ext.getStore('Assignees').filterBy('type', record.get('type'));
                                                Ext.getStore('Assignees').filterBy('id', record.get('id'));
                                                return Ext.getStore('Assignees').getRange()[0].get('name');


                                                //return Ext.getStore('SettingsUsers').getById(value.toString()).get('user_name');
                                            } else {
                                                return 'TUTTI';
                                            }
                                        } else {
                                            return value;
                                        }

                                        */
                                        return record.get('user_name');
                                            },
                                            dataIndex: 'user_name',
                                            text: 'Utente',
                                            flex: 1,
                                            editor: {
                                                xtype: 'combobox',
                                                displayField: 'name',
                                                queryMode: 'local',
                                                store: 'Assignees',
                                                valueField: 'id',
                                                listeners: {
                                                    select: {
                                                        fn: me.onComboboxSelect,
                                                        scope: me
                                                    }
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value === true){
                                                    return '<img src="./resources/icons/accept.png">';
                                                } else {
                                                    return '<img src="./resources/icons/delete.png">';
                                                }
                                            },
                                            width: 75,
                                            align: 'center',
                                            dataIndex: 'sign',
                                            text: 'Firma',
                                            editor: {
                                                xtype: 'checkboxfield',
                                                name: 'sign'
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value === true){
                                                    return '<img src="./resources/icons/accept.png">';
                                                } else {
                                                    return '<img src="./resources/icons/delete.png">';
                                                }
                                            },
                                            width: 75,
                                            align: 'center',
                                            dataIndex: 'albo',
                                            text: 'Albo',
                                            editor: {
                                                xtype: 'checkboxfield'
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value === true){
                                                    return '<img src="./resources/icons/accept.png">';
                                                } else {
                                                    return '<img src="./resources/icons/delete.png">';
                                                }
                                            },
                                            width: 75,
                                            align: 'center',
                                            dataIndex: 'trasparenza',
                                            text: 'Trasparenza',
                                            editor: {
                                                xtype: 'checkboxfield'
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value === true){
                                                    return '<img src="./resources/icons/accept.png">';
                                                } else {
                                                    return '<img src="./resources/icons/delete.png">';
                                                }
                                            },
                                            width: 75,
                                            align: 'center',
                                            dataIndex: 'protocol',
                                            text: 'Protocollo',
                                            editor: {
                                                xtype: 'checkboxfield'
                                            }
                                        },
                                        {
                                            xtype: 'gridcolumn',
                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                if (value === true){
                                                    return '<img src="./resources/icons/accept.png">';
                                                } else {
                                                    return '<img src="./resources/icons/delete.png">';
                                                }
                                            },
                                            align: 'center',
                                            dataIndex: 'archive',
                                            text: 'Conservazione',
                                            editor: {
                                                xtype: 'checkboxfield'
                                            }
                                        },
                                        {
                                            xtype: 'actioncolumn',
                                            width: 40,
                                            align: 'center',
                                            items: [
                                                {
                                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                        return 'icon-cancel';
                                                    },
                                                    handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                        Ext.getStore('ArchiveClassSteps').remove(record);
                                                    }
                                                }
                                            ],
                                            listeners: {
                                                show: {
                                                    fn: me.onActioncolumnShow,
                                                    scope: me
                                                }
                                            }
                                        }
                                    ],
                                    viewConfig: {
                                        plugins: [
                                            Ext.create('Ext.grid.plugin.DragDrop', {

                                            })
                                        ]
                                    },
                                    plugins: [
                                        Ext.create('Ext.grid.plugin.RowEditing', {
                                            listeners: {
                                                edit: {
                                                    fn: me.onRowEditingEdit1,
                                                    scope: me
                                                }
                                            }
                                        })
                                    ],
                                    dockedItems: [
                                        {
                                            xtype: 'toolbar',
                                            dock: 'top',
                                            items: [
                                                {
                                                    xtype: 'button',
                                                    handler: function(button, e) {
                                                        var archive_class = Ext.getCmp('ArchiveClassGrid').getSelectionModel().getSelection()[0].get('id');
                                                        Ext.getStore('ArchiveClassSteps').add({archive_class: archive_class});
                                                        Ext.getCmp('ArchiveClassStepsGrid').editingPlugin.startEdit(Ext.getStore('ArchiveClassSteps').count() - 1);
                                                    },
                                                    iconCls: 'icon-add',
                                                    text: 'Aggiungi'
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            padding: 5,
                            layout: {
                                type: 'hbox',
                                align: 'middle',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var i = 1,
                                            rec = Ext.getCmp('ArchiveClassGrid').getSelectionModel().getSelection()[0],
                                            values = Ext.getCmp('ArchiveClassFrm').getForm().getValues(),
                                            name = values.name,
                                            code = values.code,
                                            acs = Ext.getStore('ArchiveClasses'),
                                            acss = Ext.getStore('ArchiveClassSteps');

                                        Ext.getCmp('ArchiveClassFrm').setLoading();

                                        rec.set('name', name);
                                        rec.set('code', code);
                                        if(acs.getModifiedRecords().length > 0){
                                            acs.save({
                                                success: function(){
                                                    Ext.getCmp('ArchiveClassFrm').setLoading(false);
                                                }
                                            });
                                        } else {
                                            Ext.getCmp('ArchiveClassFrm').setLoading(false);
                                        }

                                        Ext.each(acss.getRange(), function(step){
                                            step.set('sort', i);
                                            i++;
                                        });

                                        Ext.getCmp('ArchiveClassFrm').setLoading();
                                        if(acss.getModifiedRecords().length > 0){
                                            Ext.getStore('ArchiveClassSteps').save({
                                                success: function(){
                                                    Ext.getCmp('ArchiveClassFrm').setLoading(false);
                                                    Ext.getStore('ArchiveClassSteps').load({
                                                        params: {
                                                            archive_class: rec.get('id')
                                                        }
                                                    });
                                                }
                                            });
                                        } else {
                                            Ext.getCmp('ArchiveClassFrm').setLoading(false);
                                        }
                                        Ext.getCmp('ArchiveClassStepsGrid').getSelectionModel().deselectAll();




                                    },
                                    id: 'ArchiveClassStepSaveBtn',
                                    itemId: 'ArchiveClassStepSaveBtn',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    hidden: true,
                    id: 'ArchiveClassMn',
                    itemId: 'ArchiveClassMn',
                    width: 120,
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var sel = Ext.getCmp('ArchiveClassGrid').getSelectionModel().getSelection()[0];
                                Ext.getStore('ArchiveClasses').remove(sel);
                                Ext.getStore('ArchiveClasses').save();
                                Ext.getStore('ArchiveClassSteps').removeAll();
                                Ext.getCmp('ArchiveClassFrm').getForm().reset();
                                Ext.getCmp('ArchiveClassFrm').disable();
                            },
                            iconCls: 'icon-cancel',
                            text: 'Cancella'
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onWindowShow,
                    scope: me
                },
                close: {
                    fn: me.onWindowClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onViewItemClick: function(dataview, record, item, index, e, eOpts) {
        Ext.getCmp('ArchiveClassFrm').enable();
        Ext.getCmp('ArchiveClassFrm').getForm().loadRecord(record);
        Ext.getStore('ArchiveClassSteps').load({
            params: {
                archive_class: record.get('id')
            }
        });

        if(!record.get('editable')){
            Ext.getCmp('ArchiveClassFrm').disable();
        }
    },

    onViewItemContextMenu: function(dataview, record, item, index, e, eOpts) {

        if (!record.get('editable')){
            return false;
        }
        Ext.getCmp('ArchiveClassMn').showAt(e.getX(), e.getY());
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        Ext.getStore('ArchiveClasses').save();
    },

    onRowEditingBeforeEdit: function(editor, context, eOpts) {
        if (!context.record.get('editable')){
            return false;
        }
    },

    onRowEditingCanceledit: function(editor, context, eOpts) {
        if(context.record.get('id') < 1){
            Ext.getStore('ArchiveClasses').remove(context.record);
        }
    },

    onComboboxSelect: function(combo, records, eOpts) {
        var stepObj = Ext.getCmp('ArchiveClassStepsGrid').getSelectionModel().getSelection()[0];
        stepObj.set('user_id', records[0].get('id'));
        stepObj.set('type', records[0].get('type'));
        stepObj.set('user_name', records[0].get('name'));

    },

    onRowEditingEdit1: function(editor, context, eOpts) {
        Ext.getCmp('ArchiveClassStepSaveBtn').handler();
    },

    onActioncolumnShow: function(component, eOpts) {
        Ext.getStore('ArchiveClasses').load();
        Ext.getStore('ArchiveClassSteps').removeAll();
    },

    onWindowShow: function(component, eOpts) {
        Ext.getStore('ArchiveClasses').load();
        Ext.getStore('RemoteClass').load();
        Ext.getStore('ArchiveClassSteps').removeAll();

        Ext.getStore('Assignees').filter(function(rec){
            var val = rec.get('type');
            return val != 'F';
        });
    },

    onWindowClose: function(panel, eOpts) {
        Ext.getStore('Assignees').clearFilter();
        Ext.getStore('Assignees').load();
    }

});