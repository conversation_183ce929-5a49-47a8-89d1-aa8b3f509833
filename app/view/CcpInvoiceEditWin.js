/*
 * File: app/view/CcpInvoiceEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpInvoiceEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpInvoiceEditWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 171,
    id: 'CcpInvoiceEditWin',
    itemId: 'CcpInvoiceEditWin',
    width: 875,
    title: 'Modifica intestatari',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    title: '',
                    store: 'CcpInvoiceAccountHolders',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'surname',
                            text: 'Cognome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 142,
                            dataIndex: 'fiscal_code',
                            text: 'Codice fiscale',
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 138,
                            dataIndex: 'address',
                            text: 'Indirizzo',
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'city',
                            text: 'Città',
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 64,
                            dataIndex: 'province',
                            text: 'Provincia',
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 70,
                            dataIndex: 'zip_code',
                            text: 'Cap',
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            text: 'PIVA',
                            editor: {
                                xtype: 'textfield'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            text: 'Codice SDI',
                            editor: {
                                xtype: 'textfield'
                            }
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            clicksToEdit: 1
                        })
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var id = Ext.getCmp('CcpInvoiceEditWin').recordId = id,
                                    data = [];

                                Ext.each(Ext.getStore('CcpInvoiceAccountHolders').getRange(), function(v){
                                    data.push(v.data);
                                });

                                Ext.Ajax.request({
                                    url: '/mc2-api/ccp/invoice/' + id,
                                    method:'PUT',
                                    params: {
                                        account_holder: Ext.encode(data)
                                    }
                                });
                            },
                            iconCls: 'icon-disk',
                            text: 'Salva'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});