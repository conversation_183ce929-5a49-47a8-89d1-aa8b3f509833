/*
 * File: app/view/AdminParametersWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.AdminParametersWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.AdminParametersWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing'
    ],

    height: 400,
    id: 'AdminParametersWin',
    itemId: 'AdminParametersWin',
    minHeight: 400,
    minWidth: 600,
    width: 600,
    title: 'Parameters management',
    maximizable: true,
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'AdminParametersGrid',
                    itemId: 'AdminParametersGrid',
                    emptyText: 'No parameters present',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    store: 'MC2Parameters',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Name',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            width: 200,
                            resizable: false,
                            dataIndex: 'value',
                            hideable: false,
                            text: 'Value',
                            flex: 1,
                            editor: {
                                xtype: 'textfield'
                            }
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                }
                            }
                        })
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onAdminParametersWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        Ext.getStore('MC2Parameters').sync();
    },

    onAdminParametersWinBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('MC2Parameters').load();
    }

});