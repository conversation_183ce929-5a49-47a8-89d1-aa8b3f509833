/*
 * File: app/view/ProtocolPrintsBarcodeWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ProtocolPrintsBarcodeWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ProtocolPrintsBarcodeWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    single: false,
    height: 105,
    id: 'ProtocolPrintsBarcodeWin',
    itemId: 'ProtocolPrintsBarcodeWin',
    width: 200,
    title: 'Stampa codici a barre',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    flex: 1,
                    border: false,
                    id: 'ProtocolPrintsBarcodeForm',
                    itemId: 'ProtocolPrintsBarcodeForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'combobox',
                            id: 'ProtocolPrintsBarcodeModel',
                            itemId: 'ProtocolPrintsBarcodeModel',
                            name: 'barcode',
                            allowBlank: false,
                            allowOnlyWhitespace: false,
                            emptyText: 'Modello...',
                            editable: false,
                            forceSelection: true,
                            store: 'ProtocolBarcodes',
                            valueField: 'id'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            layout: {
                                type: 'hbox',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var rec = {};

                                        rec.newSpool = 1;
                                        rec.print = 'Barcodes';
                                        rec.namespace = 'Protocol';
                                        rec.type = 'PDF';
                                        rec.mime = 'application/pdf';
                                        rec.filter = Ext.JSON.encode(Ext.getCmp('ProtocolFilterForm').getForm().getValues());
                                        rec.barcode = Ext.getCmp('ProtocolPrintsBarcodeModel').getValue();

                                        if (Ext.getCmp('ProtocolPrintsBarcodeWin').single) {
                                            var  protocol = Ext.getCmp('ProtocolsGrid').getSelectionModel().getSelection()[0];
                                            rec.print = 'Barcode';
                                            rec.protocol = protocol.get('id');
                                            rec.protocol_number = protocol.get('protocol_number');
                                            rec.date = protocol.get('date');
                                        }

                                        Ext.Ajax.request({
                                            url: '/mc2-api/core/print',
                                            params: rec,
                                            success: function(response, opts) {
                                                var res = Ext.decode(response.responseText);
                                                mc2ui.app.showNotifyPrint(res);
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-printer',
                                    text: 'Stampa'
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});