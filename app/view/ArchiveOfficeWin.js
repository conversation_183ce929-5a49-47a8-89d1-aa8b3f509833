/*
 * File: app/view/ArchiveOfficeWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveOfficeWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveOfficeWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.grid.Panel',
        'Ext.grid.column.Column',
        'Ext.form.field.Text',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.selection.CheckboxModel'
    ],

    height: 250,
    id: 'ArchiveOfficeWin',
    itemId: 'ArchiveOfficeWin',
    width: 400,
    title: 'Uffici',

    layout: {
        type: 'hbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                Ext.getCmp('ArchiveOfficeWin').setLoading();

                                var uList = [],
                                    users = Ext.getCmp('ArchiveOfficeUsersGrid').getSelectionModel().getSelection(),
                                    officeId = Ext.getCmp('ArchiveOfficeGrid').getSelectionModel().getSelection()[0].get('id');

                                Ext.each(users, function(u){
                                    uList.push(parseInt(u.get('user_id')));
                                });

                                Ext.Ajax.request({
                                    url: '/mc2-api/archive/office/' + officeId,
                                    method: 'PUT',
                                    params: {
                                        users: Ext.encode(uList)
                                    },
                                    success: function(){
                                        Ext.getCmp('ArchiveOfficeWin').setLoading(false);
                                    }
                                });
                            },
                            iconCls: 'icon-disk',
                            text: 'Salva'
                        }
                    ]
                }
            ],
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    id: 'ArchiveOfficeGrid',
                    itemId: 'ArchiveOfficeGrid',
                    title: '',
                    hideHeaders: true,
                    store: 'ArchiveOffices',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'String',
                            flex: 1,
                            editor: {
                                xtype: 'textfield'
                            }
                        }
                    ],
                    viewConfig: {
                        listeners: {
                            itemclick: {
                                fn: me.onViewItemClick,
                                scope: me
                            }
                        }
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        console.log('Add');

                                        Ext.getStore('ArchiveOffices').insert(0, {name: 'Nuovo ufficio'});
                                        Ext.getCmp('ArchiveOfficeGrid').editingPlugin.startEdit(0);

                                    },
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                }
                            }
                        })
                    ]
                },
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    disabled: true,
                    id: 'ArchiveOfficeUsersGrid',
                    itemId: 'ArchiveOfficeUsersGrid',
                    title: 'Utenti nell\'ufficio',
                    hideHeaders: true,
                    store: 'ArchiveOfficeUsers',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'user_name',
                            text: '',
                            flex: 1
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {

                    })
                }
            ],
            listeners: {
                show: {
                    fn: me.onArchiveOfficeWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onViewItemClick: function(dataview, record, item, index, e, eOpts) {
        var id = record.get('id'),
            store = Ext.getStore('ArchiveOfficeUsers'),
            v = Ext.getCmp('ArchiveOfficeUsersGrid'),
            s = v.getStore(),
            indexUser;

        v.getSelectionModel().deselectAll();

        Ext.Ajax.request({
            url: '/mc2-api/archive/office/' + id,
            success: function(r){
                var res = Ext.decode(r.responseText);
                if(res.success === true) {
                    Ext.each(res.results.users, function(u){
                        indexUser = s.getById(u.uid.toString()).index;
                        v.getSelectionModel().select(indexUser, true);
                    });
                    Ext.getCmp('ArchiveOfficeUsersGrid').enable();
                }

            }
        });
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        Ext.getStore('ArchiveOffices').save();
    },

    onArchiveOfficeWinShow: function(component, eOpts) {
        Ext.getStore('ArchiveOffices').load();
        Ext.getStore('ArchiveOfficeUsers').load();
    }

});