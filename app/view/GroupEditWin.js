/*
 * File: app/view/GroupEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.GroupEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.GroupEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.Text',
        'Ext.form.field.Checkbox',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    height: 127,
    id: 'GroupEditWin',
    itemId: 'GroupEditWin',
    width: 295,
    resizable: false,
    layout: 'fit',
    title: 'Gruppo',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'EditGroupFrm',
                    itemId: 'EditGroupFrm',
                    bodyCls: [
                        'bck-content',
                        'x-panel-body-default',
                        'x-box-layout-ct'
                    ],
                    bodyPadding: 10,
                    url: '/mc2/applications/core/groups/write.php',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'hiddenfield',
                            fieldLabel: 'Label',
                            inputId: 'gid'
                        },
                        {
                            xtype: 'textfield',
                            fieldLabel: 'Nome',
                            labelAlign: 'right',
                            labelWidth: 60,
                            inputId: 'group_name',
                            allowBlank: false,
                            allowOnlyWhitespace: false
                        },
                        {
                            xtype: 'checkboxfield',
                            fieldLabel: 'Abilitato',
                            labelAlign: 'right',
                            labelWidth: 60,
                            inputId: 'enabled',
                            inputValue: '1'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    id: 'GroupSaveBtn',
                                    itemId: 'GroupSaveBtn',
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onGroupSaveBtnClick,
                                            scope: me
                                        }
                                    }
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onGroupSaveBtnClick: function(button, e, eOpts) {
        var form = Ext.getCmp('EditGroupFrm').getForm();
        form.submit({
            success : function(){
                Ext.getCmp('GroupEditWin').close();
                Ext.getStore('SettingsGroups').load();
            }
        });
    }

});