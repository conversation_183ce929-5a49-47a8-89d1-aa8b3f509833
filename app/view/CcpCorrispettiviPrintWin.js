/*
 * File: app/view/CcpCorrispettiviPrintWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpCorrispettiviPrintWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpCorrispettiviPrintWin',

    requires: [
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.Date',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button'
    ],

    id: 'CcpCorrispettiviPrintWin',
    width: 400,
    layout: 'fit',
    title: 'Stampa corrispettivi',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    id: 'CcpCorrispettiviPrintFrm',
                    bodyPadding: 10,
                    title: '',
                    url: '/mc2-api/core/print',
                    items: [
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            id: 'CcpCorrSchoolYearCmb',
                            fieldLabel: 'Anno scolastico',
                            name: 'subject_school_year',
                            store: 'CcpSchoolYearsFilter',
                            valueField: 'id',
                            listeners: {
                                afterrender: {
                                    fn: me.onCcpCorrSchoolYearCmbAfterRender,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'combobox',
                            anchor: '100%',
                            id: 'CcpCorrispettiviPrintTypeCmb',
                            fieldLabel: 'Tipo',
                            name: 'print',
                            allowBlank: false,
                            store: [
                                [
                                    'CorrispettiviRegistro',
                                    'Registro Corrispettivi'
                                ],
                                [
                                    'CorrispettiviImponibili',
                                    'Registro Imponibili'
                                ],
                                [
                                    'CorrispettiviModalita',
                                    'Corrispettivi per modalità pagamento'
                                ],
                                [
                                    'CorrispettiviCausaleIndirizzo',
                                    'Corrispettivi per causale e indirizzo'
                                ],
                                [
                                    'CorrispettiviIndirizziTipoCausale',
                                    'Corrispettivi indirizzo, tipo e causale'
                                ],
                                [
                                    'CorrispettiviModalitaIndirizzo',
                                    'Corrispettivi per indirizzo e modalità di pagamento'
                                ]
                            ]
                        },
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            fieldLabel: 'Da',
                            name: 'from',
                            allowBlank: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        },
                        {
                            xtype: 'datefield',
                            anchor: '100%',
                            fieldLabel: 'A',
                            name: 'to',
                            allowBlank: false,
                            format: 'd/m/Y',
                            submitFormat: 'Y-m-d'
                        }
                    ]
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'top',
                    items: [
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var form = Ext.getCmp('CcpCorrispettiviPrintFrm'),
                                    values = form.getValues(),
                                    rec={},
                                    printType= Ext.getCmp('CcpCorrispettiviPrintWin').printType;

                                rec.newSpool = 1;
                                rec.namespace = 'CCP';
                                rec.type = printType === 'xls' ? 'XLS' :'PDF';
                                rec.mime = printType === 'xls' ? 'application/vnd.ms-excel' : 'application/pdf';
                                rec.print = values.print;
                                rec.date_from = values.from;
                                rec.date_to = values.to;
                                rec.subject_school_year = values.subject_school_year;
                                if (form.isValid()) {
                                    Ext.Ajax.request({
                                        url: '/mc2-api/core/print',
                                        params: rec,
                                        success: function(response, opts) {
                                            var res = Ext.decode(response.responseText);
                                            mc2ui.app.showNotifyPrint(res);
                                            Ext.getCmp('CcpCorrispettiviPrintWin').close();
                                        }
                                    });
                                }
                            },
                            text: 'Stampa'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onCcpCorrSchoolYearCmbAfterRender: function(component, eOpts) {
        component.setValue(Ext.getCmp('CcpCorrispettiviSchoolYearCmb').getValue());
    }

});