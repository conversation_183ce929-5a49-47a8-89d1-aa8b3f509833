/*
 * File: app/view/MpExtraTimeEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.MpExtraTimeEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.MpExtraTimeEditWin',

    requires: [
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    id: 'MpExtraTimeEditWin',
    width: 800,
    title: 'Inserimento servizio extra time',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            dockedItems: [
                {
                    xtype: 'toolbar',
                    flex: 1,
                    dock: 'bottom',
                    margin: '10 0 0 0',
                    layout: {
                        type: 'hbox',
                        align: 'bottom'
                    },
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var service = Ext.clone(Ext.getCmp('MpExtraTimeEditWin').service.raw),
                                    student = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0],
                                    jsonData = {opzioni: {}};



                                for(var key in service.caratteristiche.elenco_opzioni) {
                                    var idCmb = 'MpCmb-'+key;
                                    valToKeep = Ext.getCmp(idCmb).getValue();

                                    jsonData.opzioni[key] = Ext.clone(service.caratteristiche.elenco_opzioni[key]);
                                    delete jsonData.opzioni[key].elenco_opzioni;

                                    for(var subkey in service.caratteristiche.elenco_opzioni[key].elenco_opzioni) {
                                        if(subkey === valToKeep) {
                                            jsonData.opzioni[key].opzioni = {};
                                            jsonData.opzioni[key].opzioni[valToKeep] = Ext.clone(service.caratteristiche.elenco_opzioni[key].elenco_opzioni[valToKeep]);
                                        }

                                    }

                                }

                                jsonData.id_marketplace = service.id_marketplace;
                                jsonData.id_studente = student.get('db_id');

                                Ext.getCmp('MpExtraTimeEditWin').setLoading(true);
                                Ext.Ajax.request({
                                    method: 'POST',
                                    url: '/mc2-api/ccp/marketplace/purchase/add',
                                    jsonData: jsonData,
                                    success: function() {
                                        Ext.getCmp('MpExtraTimeEditWin').close();
                                        Ext.getStore('CcpStudentMarkets').removeAll();
                                        Ext.getCmp('CcpStudentsGrd').filterByStudent(student.get('db_id'));
                                    }
                                });


                            },
                            text: 'Salva'
                        },
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        }
                    ]
                }
            ],
            listeners: {
                close: {
                    fn: me.onMpExtraTimeEditWinClose,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onMpExtraTimeEditWinClose: function(panel, eOpts) {
        Ext.getStore('CcpStudentMarkets').removeAll();

        var student = Ext.getCmp('CcpStudentsGrd').getSelectionModel().getSelection()[0];
        Ext.getCmp('CcpStudentsGrd').filterByStudent(student.get('db_id'));
    },

    loadData: function(service) {
        var data = service.raw,
            firstKey1, firstKey2,
            headerCnt = Ext.create('Ext.container.Container', {
                padding: 5,
                id: 'MpCnt-headers',
                layout: 'hbox',
                align: 'stretch'
            });

        Ext.getCmp('MpExtraTimeEditWin').service = service;

        for( firstKey1 in data.caratteristiche.elenco_opzioni) break;
        for( firstKey2 in data.caratteristiche.elenco_opzioni[firstKey1].elenco_opzioni) break;


        headerCnt.add(Ext.create('Ext.form.Label', {
            flex: null,
            text: '',
            width: 240
        }));
        for (var headerKey in data.caratteristiche.elenco_opzioni[firstKey1].elenco_opzioni[firstKey2]) {

            if(headerKey == 'descrizione') continue;

            headerCnt.add(Ext.create('Ext.form.Label', {
                flex: null,
                text: (headerKey.charAt(0).toUpperCase() + headerKey.slice(1)).split('_').join(' '),
                width: 120,
                style: 'text-align:center'
            }));
        }
        Ext.getCmp('MpExtraTimeEditWin').add(headerCnt);

        for(var key in data.caratteristiche.elenco_opzioni) {
            var cnt = Ext.create('Ext.container.Container', {
                padding: 5,
                id: 'MpCnt-' + key,
                layout: 'hbox',
                align: 'stretch'
            }),
                lbl = Ext.create('Ext.form.Label', {
                    flex: null,
                    text: data.caratteristiche.elenco_opzioni[key].descrizione,
                    width: 120,
                    padding: '0 10'
                }),
                cmb,
                cmbData = [],
                lastKey;



            for(var skey in data.caratteristiche.elenco_opzioni[key].elenco_opzioni) {
                cmbData.push([skey,data.caratteristiche.elenco_opzioni[key].elenco_opzioni[skey].descrizione]);
                lastKey=skey;
            }


            cmb = Ext.create('Ext.form.field.ComboBox', {
                flex: null,
                fieldText: '',
                id: 'MpCmb-'+key,
                store: cmbData,
                width: 120,
                forceSelection: true,
                editable: false,
                listeners: {
                    change: function(field, newVal, oldVal) {

                        var row = field.id.split('-')[1];
                        for(var hkey in data.caratteristiche.elenco_opzioni[key].elenco_opzioni[lastKey]) {
                            if(hkey == 'descrizione') continue;
                            Ext.getCmp(hkey+'_'+row).setText(data.caratteristiche.elenco_opzioni[row].elenco_opzioni[newVal][hkey]);
                        }
                    }
                }
            });


            cnt.add(lbl);
            cnt.add(cmb);



            for(var hkey in data.caratteristiche.elenco_opzioni[key].elenco_opzioni[lastKey]) {
                if(hkey == 'descrizione') continue;

                var hCol = Ext.create('Ext.form.Label', {
                    flex: null,
                    id: hkey+'_'+key,
                    width: 120,
                    style: 'text-align:center'
                });

                cnt.add(hCol);

            }
            Ext.getCmp('MpExtraTimeEditWin').add(cnt);

            cmb.select(cmbData[0][0]);

        }
    }

});