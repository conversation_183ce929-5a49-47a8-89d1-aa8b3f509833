/*
 * File: app/view/MyPanel29.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.MyPanel29', {
    extend: 'Ext.panel.Panel',
    alias: 'widget.mypanel29',

    requires: [
        'Ext.container.Container'
    ],

    layout: 'fit',
    title: 'Saldi',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'container',
                    html: '<saldi-panel></saldi-panel>',
                    layout: 'fit',
                }
            ]
        });

        me.callParent(arguments);
    }

});