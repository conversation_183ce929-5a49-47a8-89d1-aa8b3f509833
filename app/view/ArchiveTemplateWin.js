/*
 * File: app/view/ArchiveTemplateWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveTemplateWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveTemplateWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.toolbar.Toolbar',
        'Ext.grid.plugin.RowEditing',
        'Ext.tab.Panel',
        'Ext.tab.Tab',
        'Ext.form.Panel',
        'Ext.form.field.ComboBox',
        'Ext.form.field.TextArea',
        'Ext.form.field.Number',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.selection.CheckboxModel'
    ],

    height: 445,
    id: 'ArchiveTemplateWin',
    itemId: 'ArchiveTemplateWin',
    width: 765,
    layout: 'border',
    title: 'Tipi di documento',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    region: 'west',
                    split: true,
                    id: 'ArchiveTemplateGrid',
                    itemId: 'ArchiveTemplateGrid',
                    width: 200,
                    title: '',
                    store: 'ArchiveTemplates',
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'name',
                            text: 'Nome',
                            flex: 1,
                            editor: {
                                xtype: 'textfield'
                            }
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('ArchiveTemplateGrid').getSelectionModel().deselectAll();
                                        Ext.getCmp('ArchiveTemplateTabPnl').disable();

                                        Ext.getStore('ArchiveTemplates').insert(0, {editable: true});
                                        Ext.getCmp('ArchiveTemplateGrid').editingPlugin.startEdit(0);

                                        Ext.getCmp('ArchiveTemplateTabPnl').disable();
                                    },
                                    icon: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.RowEditing', {
                            listeners: {
                                edit: {
                                    fn: me.onRowEditingEdit,
                                    scope: me
                                }
                            }
                        })
                    ],
                    listeners: {
                        itemclick: {
                            fn: me.onArchiveTemplateGridItemClick,
                            scope: me
                        }
                    }
                },
                {
                    xtype: 'container',
                    flex: 1,
                    region: 'center',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'tabpanel',
                            flex: 1,
                            disabled: true,
                            id: 'ArchiveTemplateTabPnl',
                            itemId: 'ArchiveTemplateTabPnl',
                            activeTab: 0,
                            items: [
                                {
                                    xtype: 'panel',
                                    layout: 'fit',
                                    title: 'Protocollo',
                                    items: [
                                        {
                                            xtype: 'form',
                                            id: 'ArchiveTemplateProtocolFrm',
                                            itemId: 'ArchiveTemplateProtocolFrm',
                                            bodyPadding: 10,
                                            title: '',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    id: 'ProtocolNewType1',
                                                    itemId: 'ProtocolNewType1',
                                                    fieldLabel: 'Voce Titolario',
                                                    name: 'type',
                                                    anyMatch: true,
                                                    displayField: 'full_denomination',
                                                    queryMode: 'local',
                                                    store: 'ProtocolTypesLeaf',
                                                    typeAhead: true,
                                                    valueField: 'id'
                                                },
                                                {
                                                    xtype: 'textareafield',
                                                    fieldLabel: 'Oggetto',
                                                    name: 'object'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'panel',
                                    title: 'Albo',
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'form',
                                            flex: 1,
                                            id: 'ArchiveTemplateAlboFrm',
                                            itemId: 'ArchiveTemplateAlboFrm',
                                            bodyPadding: 10,
                                            title: '',
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'textareafield',
                                                    fieldLabel: 'Titolo',
                                                    name: 'title'
                                                },
                                                {
                                                    xtype: 'numberfield',
                                                    fieldLabel: 'Fra giorni',
                                                    name: 'start',
                                                    hideTrigger: true
                                                },
                                                {
                                                    xtype: 'numberfield',
                                                    fieldLabel: 'Per giorni',
                                                    name: 'total_days',
                                                    hideTrigger: true
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    fieldLabel: 'Categoria',
                                                    name: 'category',
                                                    displayField: 'name',
                                                    queryMode: 'local',
                                                    store: 'AlboCategories',
                                                    valueField: 'id'
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    fieldLabel: 'Area',
                                                    name: 'area',
                                                    displayField: 'name',
                                                    queryMode: 'local',
                                                    store: 'AlboAreas',
                                                    valueField: 'id'
                                                },
                                                {
                                                    xtype: 'combobox',
                                                    fieldLabel: 'Ente',
                                                    name: 'ente',
                                                    displayField: 'name',
                                                    queryMode: 'local',
                                                    store: 'AlboEntities',
                                                    valueField: 'id'
                                                }
                                            ]
                                        }
                                    ]
                                },
                                {
                                    xtype: 'panel',
                                    autoScroll: true,
                                    title: 'Trasparenza',
                                    items: [
                                        {
                                            xtype: 'treepanel',
                                            border: false,
                                            id: 'ArchiveTemplateTrasparenzaFrm',
                                            itemId: 'ArchiveTemplateTrasparenzaFrm',
                                            store: 'TrasparenzaVoicesPickerTree',
                                            rootVisible: false,
                                            viewConfig: {

                                            },
                                            columns: [
                                                {
                                                    xtype: 'treecolumn',
                                                    dataIndex: 'title',
                                                    text: 'Titolo',
                                                    flex: 1
                                                }
                                            ],
                                            selModel: Ext.create('Ext.selection.CheckboxModel', {
                                                mode: 'SIMPLE',
                                                checkOnly: true
                                            })
                                        }
                                    ]
                                }
                            ]
                        },
                        {
                            xtype: 'form',
                            bodyPadding: 10,
                            title: '',
                            layout: {
                                type: 'vbox',
                                align: 'center',
                                pack: 'center'
                            },
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var template = {},
                                            voices = [],
                                            rec;

                                        Ext.getCmp('ArchiveTemplateTabPnl').setLoading();
                                        template.protocol = Ext.getCmp('ArchiveTemplateProtocolFrm').getValues();
                                        template.albo = Ext.getCmp('ArchiveTemplateAlboFrm').getValues();

                                        Ext.each(Ext.getCmp('ArchiveTemplateTrasparenzaFrm').getSelectionModel().getSelection(), function(v, k){
                                            voices.push(v.get('id'));
                                        });
                                        template.trasparenza = {voices: voices};


                                        rec = Ext.getCmp('ArchiveTemplateGrid').getSelectionModel().getSelection()[0];
                                        rec.set('template', Ext.encode(template));
                                        Ext.getStore('ArchiveTemplates').save(rec);
                                        setTimeout(function(){
                                            Ext.getCmp('ArchiveTemplateTabPnl').setLoading(false);
                                        }, 500);

                                    },
                                    text: 'Salva'
                                }
                            ]
                        }
                    ]
                }
            ],
            listeners: {
                show: {
                    fn: me.onArchiveTemplateWinShow,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onRowEditingEdit: function(editor, context, eOpts) {
        Ext.getStore('ArchiveTemplates').save();
        Ext.getCmp('ArchiveTemplateTabPnl').enable();
    },

    onArchiveTemplateGridItemClick: function(dataview, record, item, index, e, eOpts) {
        var template = Ext.decode(record.get('template')),
            root;
        Ext.getCmp('ArchiveTemplateWin').clean();

        if(template){
            if(template.protocol) Ext.getCmp('ArchiveTemplateProtocolFrm').getForm().setValues(template.protocol);

            if(template.albo) Ext.getCmp('ArchiveTemplateAlboFrm').getForm().setValues(template.albo);

            if(template.trasparenza) {

                var setChildrenCheckedStatus = function (current) {
                    Ext.each(template.trasparenza.voices, function(voiceId){
                        if(voiceId == current.get('id')) Ext.getCmp('ArchiveTemplateTrasparenzaFrm').getSelectionModel().select(current, true);
                    });

                    if (current.hasChildNodes()) {
                        current.eachChild(arguments.callee);
                    }
                };
                /*Ext.each(template.trasparenza.voices, function(voiceId){
                    Ext.getCmp('ArchiveTemplateTrasparenzaFrm').getSelectionModel().select(voiceId, true);
                });*/
                root = Ext.getCmp('ArchiveTemplateTrasparenzaFrm').getRootNode();

                if (root.hasChildNodes()) {
                    setChildrenCheckedStatus(root);
                }
            }
        }

        Ext.getCmp('ArchiveTemplateTabPnl').enable();
    },

    onArchiveTemplateWinShow: function(component, eOpts) {
        Ext.getStore('ArchiveTemplates').load();

        var tt = Ext.getCmp('ArchiveTemplateTabPnl');
        tt.setActiveTab(2);
        tt.setActiveTab(0);

        Ext.getStore('ProtocolTypesLeaf').load();
        Ext.getStore('AlboCategories').load();
        Ext.getStore('AlboEntities').load();
        Ext.getStore('AlboAreas').load();
    },

    clean: function() {
        Ext.getCmp('ArchiveTemplateAlboFrm').getForm().reset();
        Ext.getCmp('ArchiveTemplateProtocolFrm').getForm().reset();

        Ext.getCmp('ArchiveTemplateTrasparenzaFrm').getSelectionModel().deselectAll();

    }

});