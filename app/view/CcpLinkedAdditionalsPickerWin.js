/*
 * File: app/view/CcpLinkedAdditionalsPickerWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpLinkedAdditionalsPickerWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpLinkedAdditionalsPickerWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Action',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.selection.CheckboxModel'
    ],

    permissible: true,
    id: 'CcpLinkedAdditionalsPickerWin',
    minHeight: 200,
    width: 400,
    resizable: false,
    title: '<PERSON><PERSON><PERSON>',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    flex: 1,
                    border: false,
                    id: 'CcpLinkedAdditionalsPickerGrid',
                    header: false,
                    emptyText: 'Nessuna Addizionale presente.',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'CcpAdditionalsForm',
                    columns: [
                        {
                            xtype: 'actioncolumn',
                            width: 20,
                            items: [
                                {
                                    getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('positive')) {
                                            return 'icon-control_add';
                                        } else {
                                            return 'icon-control_remove';
                                        }
                                    },
                                    getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                        if (r.get('positive')) {
                                            return 'Addizionale positiva';
                                        } else {
                                            return 'Addizionale negativa';
                                        }
                                    }
                                }
                            ]
                        },
                        {
                            xtype: 'gridcolumn',
                            draggable: false,
                            resizable: false,
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if (value) {
                                    return '%';
                                }
                            },
                            width: 20,
                            dataIndex: 'percentual'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var selection = Ext.getCmp('CcpLinkedAdditionalsPickerGrid').getSelectionModel().getSelection(),
                                            models = Array();

                                        Ext.each(selection, function (s) {
                                            models.push({
                                                additional_id: s.get('id'),
                                                item_id: 0,
                                                name: s.get('name'),
                                                positive: s.get('positive'),
                                                percentual: s.get('percentual'),
                                                amount: 0.00
                                            });
                                        });

                                        Ext.getStore('CcpLinkedAdditionalsForm').removeAll();
                                        Ext.getStore('CcpLinkedAdditionalsForm').add(models);

                                        Ext.getCmp('CcpLinkedAdditionalsPickerWin').close();
                                    },
                                    iconCls: 'icon-link',
                                    text: 'Seleziona'
                                }
                            ]
                        }
                    ],
                    selModel: Ext.create('Ext.selection.CheckboxModel', {
                        checkOnly: true,
                        showHeaderCheckbox: false
                    })
                }
            ]
        });

        me.callParent(arguments);
    }

});