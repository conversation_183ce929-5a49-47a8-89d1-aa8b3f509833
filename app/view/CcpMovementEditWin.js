/*
 * File: app/view/CcpMovementEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpMovementEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpMovementEditWin',

    requires: [
        'Ext.menu.Menu',
        'Ext.menu.Item',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.toolbar.Spacer',
        'Ext.form.field.ComboBox',
        'Ext.form.Panel',
        'Ext.form.field.Hidden',
        'Ext.form.field.Number',
        'Ext.form.field.Date',
        'Ext.grid.Panel',
        'Ext.grid.column.Date',
        'Ext.grid.column.Number',
        'Ext.grid.View',
        'Ext.grid.plugin.RowEditing',
        'Ext.grid.column.Action',
        'Ext.selection.CheckboxModel',
        'Ext.form.Label',
        'Ext.form.field.TextArea',
        'Ext.tree.Panel',
        'Ext.tree.View',
        'Ext.tree.Column',
        'Ext.form.field.Checkbox'
    ],

    height: '90%',
    id: 'CcpMovementEditWin',
    itemId: 'CcpMovementEditWin',
    width: '90%',
    layout: 'fit',
    title: 'Movimento',
    maximizable: true,
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            listeners: {
                show: {
                    fn: me.onCcpMovementEditWinShow,
                    scope: me
                },
                close: {
                    fn: me.onCcpMovementEditWinClose,
                    scope: me
                }
            },
            items: [
                {
                    xtype: 'menu',
                    id: 'CcpMovementEditAdditionalEditMn',
                    width: 150,
                    items: [
                        {
                            xtype: 'menuitem',
                            handler: function(item, e) {
                                var g = Ext.getCmp('CcpMovementEditLinkedAdditionalsGrid'),
                                    r = g.getSelectionModel().getSelection()[0],
                                    sAdds = Ext.getStore('CcpLinkedAdditionalsForm');

                                g.getPlugin().blocked = false;
                                g.getPlugin().startEdit(r);
                                g.getPlugin().blocked = true;
                            },
                            id: 'contextCcpMovementEditAdditionalEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica Importo'
                        }
                    ]
                },
                {
                    xtype: 'container',
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    items: [
                        {
                            xtype: 'toolbar',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.getCmp('CcpMovementEditWin').saveMovement();
                                    },
                                    id: 'CcpSaveMovementIdBtn',
                                    itemId: 'CcpSaveMovementIdBtn',
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        // Save movement and go to payment window
                                        Ext.getCmp('CcpMovementEditWin').saveMovement(true, false);
                                    },
                                    id: 'CcpSaveGoPayMovementIdBtn',
                                    itemId: 'CcpSaveGoPayMovementIdBtn',
                                    text: 'Salva e vai al pagamento'
                                },
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        // Save movement and go to payment window
                                        Ext.getCmp('CcpMovementEditWin').saveMovement(false, true);
                                    },
                                    id: 'CcpSaveAndPayBtn',
                                    itemId: 'CcpSaveAndPayBtn',
                                    text: 'Salva e paga'
                                }
                            ]
                        },
                        {
                            xtype: 'toolbar',
                            items: [
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                },
                                {
                                    xtype: 'combobox',
                                    flex: 1,
                                    id: 'CcpEditMovementSubjectYearCmb',
                                    padding: 5,
                                    fieldLabel: 'Anno scolastico',
                                    displayField: 'name',
                                    forceSelection: true,
                                    queryMode: 'local',
                                    store: 'McDbs',
                                    valueField: 'name',
                                    listeners: {
                                        select: {
                                            fn: me.onComboboxSelect,
                                            scope: me
                                        },
                                        afterrender: {
                                            fn: me.onCcpEditMovementSubjectYearCmbAfterRender,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'tbspacer',
                                    flex: 1
                                }
                            ]
                        },
                        {
                            xtype: 'container',
                            flex: 1,
                            layout: {
                                type: 'hbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'form',
                                    flex: 1,
                                    border: false,
                                    id: 'CcpMovementEditForm',
                                    itemId: 'CcpMovementEditForm',
                                    width: 150,
                                    bodyCls: 'bck-content',
                                    bodyPadding: 10,
                                    header: false,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementId',
                                            name: 'id'
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch',
                                                padding: '0 0 5 0'
                                            },
                                            items: [
                                                {
                                                    xtype: 'numberfield',
                                                    flex: 1,
                                                    id: 'CcpMovementNumber',
                                                    fieldLabel: 'Numero',
                                                    name: 'number',
                                                    fieldStyle: 'text-align:right;',
                                                    hideTrigger: true,
                                                    allowDecimals: false,
                                                    allowExponential: false,
                                                    minValue: 1
                                                },
                                                {
                                                    xtype: 'datefield',
                                                    flex: 2,
                                                    id: 'CcpMovementCreationDate',
                                                    padding: '0 0 0 5',
                                                    fieldLabel: 'Data inserimento',
                                                    name: 'creation_date',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    editable: false,
                                                    format: 'd/m/Y',
                                                    startDay: 1,
                                                    submitFormat: 'c'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch',
                                                padding: '0 0 5 0'
                                            },
                                            items: [
                                                me.processCcpMovementTypeId({
                                                    xtype: 'combobox',
                                                    flex: 4,
                                                    disabled: true,
                                                    id: 'CcpMovementTypeId',
                                                    fieldLabel: 'Tipo*',
                                                    labelWidth: 50,
                                                    name: 'type_id',
                                                    allowBlank: false,
                                                    allowOnlyWhitespace: false,
                                                    displayField: 'name_school_year',
                                                    forceSelection: true,
                                                    queryMode: 'local',
                                                    store: 'CcpTypes',
                                                    valueField: 'id',
                                                    listeners: {
                                                        select: {
                                                            fn: me.onCcpMovementTypeIdSelect,
                                                            scope: me
                                                        },
                                                        change: {
                                                            fn: me.onCcpMovementTypeIdChange,
                                                            buffer: 100,
                                                            scope: me
                                                        }
                                                    }
                                                }),
                                                {
                                                    xtype: 'textfield',
                                                    flex: 2,
                                                    id: 'CcpMovementInvoiceCode',
                                                    itemId: 'CcpMovementInvoiceCode',
                                                    fieldLabel: 'Cod. fattura',
                                                    labelAlign: 'right',
                                                    labelWidth: 70,
                                                    name: 'invoice_code'
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            minHeight: 300,
                                            layout: {
                                                type: 'vbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'panel',
                                                    flex: 3,
                                                    id: 'CcpRatePnl',
                                                    itemId: 'CcpRatePnl',
                                                    padding: 5,
                                                    title: 'Riepilogo',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'gridpanel',
                                                            setTotal: function() {
                                                                var tot = 0;
                                                                Ext.each(Ext.getStore('CcpTypeSteps').getRange(), function(r) {
                                                                    tot+=r.get('value');
                                                                });

                                                                Ext.getCmp('CcpMovementTotal').setValue(tot);
                                                                Ext.getCmp('CcpExpiratonDateField').setValue(Ext.getStore('CcpTypeSteps').getRange()[0].get('expiration_date'));
                                                                Ext.getCmp('CcpAmountField').setValue(Ext.getStore('CcpTypeSteps').getRange()[0].get('value'));


                                                            },
                                                            flex: 1,
                                                            disabled: true,
                                                            id: 'CcpRateGrid',
                                                            itemId: 'CcpRateGrid',
                                                            margin: 5,
                                                            titleAlign: 'center',
                                                            store: 'CcpTypeSteps',
                                                            columns: [
                                                                {
                                                                    xtype: 'datecolumn',
                                                                    width: 80,
                                                                    dataIndex: 'expiration_date',
                                                                    text: 'Data',
                                                                    format: 'd/m/Y',
                                                                    editor: {
                                                                        xtype: 'datefield',
                                                                        format: 'd/m/Y',
                                                                        submitFormat: 'Y-m-d'
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    dataIndex: 'description',
                                                                    text: 'Descrizione',
                                                                    flex: 1,
                                                                    editor: {
                                                                        xtype: 'textfield',
                                                                        name: 'description'
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    dataIndex: 'conto_risconti',
                                                                    text: 'Conto risconti',
                                                                    listeners: {
                                                                        afterrender: function (component) {
                                                                            if (mc2ui.app.settings.easyContiRicaviRisconti !== true) {
                                                                                component.hide();
                                                                            }
                                                                        }
                                                                    },
                                                                    editor: {
                                                                        xtype: 'textfield',
                                                                        name: 'conto_risconti'
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'datecolumn',
                                                                    width: 70,
                                                                    dataIndex: 'da_ratei',
                                                                    text: 'Inizio ratei',
                                                                    format: 'd/m/Y',
                                                                    editor: {
                                                                        xtype: 'datefield',
                                                                        name: 'da_ratei',
                                                                        format: 'd/m/Y',
                                                                        submitFormat: 'Y-m-d'
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    dataIndex: 'conto_crediti',
                                                                    text: 'Conto crediti',
                                                                    editor: {
                                                                        xtype: 'textfield',
                                                                        name: 'conto_crediti'
                                                                    },
                                                                    listeners: {
                                                                        afterrender: function (component) {
                                                                            if (mc2ui.app.settings.easyContiRicaviRisconti !== true) {
                                                                                component.hide();
                                                                            }
                                                                        }
                                                                    },
                                                                },
                                                                {
                                                                    xtype: 'datecolumn',
                                                                    width: 70,
                                                                    dataIndex: 'a_ratei',
                                                                    text: 'Fine ratei',
                                                                    format: 'd/m/Y',
                                                                    editor: {
                                                                        xtype: 'datefield',
                                                                        name: 'a_ratei',
                                                                        format: 'd/m/Y',
                                                                        submitFormat: 'Y-m-d'
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    dataIndex: 'conto_ricavi',
                                                                    text: 'Conto ricavi',
                                                                    editor: {
                                                                        xtype: 'textfield'
                                                                    },
                                                                    listeners: {
                                                                        afterrender: function (component) {
                                                                            if (mc2ui.app.settings.easyContiRicaviRisconti !== true) {
                                                                                component.hide();
                                                                            }
                                                                        }
                                                                    },
                                                                },
                                                                {
                                                                    xtype: 'numbercolumn',
                                                                    width: 70,
                                                                    dataIndex: 'gross',
                                                                    text: 'Imponibile',
                                                                    editor: {
                                                                        xtype: 'numberfield'
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'numbercolumn',
                                                                    width: 45,
                                                                    dataIndex: 'vat',
                                                                    text: 'IVA %'
                                                                },
                                                                {
                                                                    xtype: 'numbercolumn',
                                                                    width: 70,
                                                                    dataIndex: 'value',
                                                                    text: 'Totale'
                                                                },
                                                                {
                                                                    xtype: 'actioncolumn',
                                                                    width: 25,
                                                                    items: [
                                                                        {
                                                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                                if(Ext.getStore('CcpTypeSteps').count() > 1) {
                                                                                    Ext.getStore('CcpTypeSteps').remove(record);
                                                                                    Ext.getCmp('CcpRateGrid').setTotal();
                                                                                }
                                                                            },
                                                                            iconCls: 'icon-delete'
                                                                        }
                                                                    ]
                                                                }
                                                            ],
                                                            plugins: [
                                                                Ext.create('Ext.grid.plugin.RowEditing', {
                                                                    listeners: {
                                                                        edit: {
                                                                            fn: me.onRowEditingEdit1,
                                                                            scope: me
                                                                        },
                                                                        beforeedit: {
                                                                            fn: me.onRowEditingBeforeEdit,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                })
                                                            ],
                                                            dockedItems: [
                                                                {
                                                                    xtype: 'toolbar',
                                                                    dock: 'top',
                                                                    items: [
                                                                        {
                                                                            xtype: 'tbspacer',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {

                                                                                var data = {expiration_date: new Date(), value: 0};

                                                                                Ext.getStore('CcpTypeSteps').add(data);

                                                                                Ext.getCmp('CcpRateGrid').setTotal();
                                                                            },
                                                                            id: 'CcpAddStepMovementBtn',
                                                                            itemId: 'CcpAddStepMovementBtn',
                                                                            iconCls: 'icon-add',
                                                                            text: 'Aggiungi'
                                                                        }
                                                                    ]
                                                                }
                                                            ],
                                                            selModel: Ext.create('Ext.selection.CheckboxModel', {
                                                                checkOnly: true
                                                            })
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            flex: 0,
                                                            disabled: true,
                                                            id: 'CcpRateTotalCnt',
                                                            itemId: 'CcpRateTotalCnt',
                                                            margin: 5,
                                                            layout: {
                                                                type: 'hbox',
                                                                align: 'middle'
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'tbspacer',
                                                                    flex: 1
                                                                },
                                                                {
                                                                    xtype: 'numberfield',
                                                                    id: 'CcpMovementTotal',
                                                                    width: 250,
                                                                    fieldLabel: 'Totale parziale',
                                                                    labelAlign: 'right',
                                                                    fieldStyle: 'text-align:right;',
                                                                    readOnly: true,
                                                                    allowExponential: false
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'panel',
                                                    flex: 2,
                                                    id: 'CcpEditMovDiscountPnl',
                                                    padding: 5,
                                                    autoScroll: true,
                                                    layout: 'fit',
                                                    title: '',
                                                    items: [
                                                        {
                                                            xtype: 'gridpanel',
                                                            id: 'CcpMovementEditLinkedAdditionalsGrid',
                                                            margin: '0 0 5 0',
                                                            minHeight: 150,
                                                            title: '',
                                                            titleAlign: 'center',
                                                            emptyText: 'Nessuna Addizionale abbinata.',
                                                            enableColumnHide: false,
                                                            enableColumnMove: false,
                                                            sortableColumns: false,
                                                            store: 'CcpLinkedAdditionalsForm',
                                                            columns: [
                                                                {
                                                                    xtype: 'actioncolumn',
                                                                    width: 20,
                                                                    resizable: false,
                                                                    hideable: false,
                                                                    items: [
                                                                        {
                                                                            getClass: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                                if (r.get('positive')) {
                                                                                    return 'icon-control_add';
                                                                                } else {
                                                                                    return 'icon-control_remove';
                                                                                }
                                                                            },
                                                                            getTip: function(v, metadata, r, rowIndex, colIndex, store) {
                                                                                if (r.get('positive')) {
                                                                                    return 'Addizionale positiva';
                                                                                } else {
                                                                                    return 'Addizionale negativa';
                                                                                }
                                                                            }
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                                        return record.get('name');
                                                                    },
                                                                    resizable: false,
                                                                    dataIndex: 'additional_id',
                                                                    hideable: false,
                                                                    text: 'Nome',
                                                                    flex: 1,
                                                                    editor: {
                                                                        xtype: 'combobox',
                                                                        displayField: 'name_full',
                                                                        forceSelection: true,
                                                                        queryMode: 'local',
                                                                        store: 'CcpAdditionals',
                                                                        valueField: 'id',
                                                                        listeners: {
                                                                            render: {
                                                                                fn: me.onComboboxRender,
                                                                                scope: me
                                                                            },
                                                                            select: {
                                                                                fn: me.onComboboxSelect1,
                                                                                scope: me
                                                                            }
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'numbercolumn',
                                                                    resizable: false,
                                                                    align: 'right',
                                                                    dataIndex: 'amount',
                                                                    text: 'Importo',
                                                                    editor: {
                                                                        xtype: 'numberfield',
                                                                        allowBlank: false,
                                                                        allowOnlyWhitespace: false,
                                                                        hideTrigger: true,
                                                                        allowExponential: false,
                                                                        minValue: 0
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                                        if (value) {
                                                                            return '%';
                                                                        } else {
                                                                            return '';
                                                                        }
                                                                    },
                                                                    width: 20,
                                                                    resizable: false,
                                                                    align: 'center',
                                                                    dataIndex: 'percentual',
                                                                    hideable: false
                                                                },
                                                                {
                                                                    xtype: 'actioncolumn',
                                                                    width: 36,
                                                                    align: 'center',
                                                                    items: [
                                                                        {
                                                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                                Ext.getStore('CcpLinkedAdditionalsForm').remove(record);
                                                                            },
                                                                            iconCls: 'icon-delete'
                                                                        }
                                                                    ]
                                                                },
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    hidden: true,
                                                                    dataIndex: 'id',
                                                                    text: 'MyColumn'
                                                                }
                                                            ],
                                                            plugins: [
                                                                Ext.create('Ext.grid.plugin.RowEditing', {
                                                                    pluginId: 'CcpMovementDiscountEditPlg',
                                                                    listeners: {
                                                                        edit: {
                                                                            fn: me.onRowEditingEdit,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                })
                                                            ],
                                                            listeners: {
                                                                itemcontextmenu: {
                                                                    fn: me.onCcpMovementEditLinkedAdditionalsGridItemContextMenu,
                                                                    scope: me
                                                                }
                                                            },
                                                            dockedItems: [
                                                                {
                                                                    xtype: 'toolbar',
                                                                    dock: 'top',
                                                                    items: [
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {
                                                                                Ext.getStore('CcpLinkedAdditionalsForm').add({amount:0});
                                                                            },
                                                                            iconCls: 'icon-add',
                                                                            text: 'Aggiungi sconto'
                                                                        },
                                                                        {
                                                                            xtype: 'tbspacer',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'label',
                                                                            text: 'Lo sconto viene applicato in fase di salvataggio'
                                                                        },
                                                                        {
                                                                            xtype: 'tbspacer',
                                                                            flex: 1
                                                                        },
                                                                        {
                                                                            xtype: 'button',
                                                                            handler: function(button, e) {
                                                                                var subjectId = parseInt(Ext.getCmp('CcpMovementSubjectId').getValue());
                                                                                Ext.Ajax.request({
                                                                                    method:'GET',
                                                                                    url:'/mc2-api/ccp/get_student_discount',
                                                                                    params :{
                                                                                        student_id: subjectId
                                                                                    },
                                                                                    success: function(res) {
                                                                                        var r = Ext.decode(res.responseText);
                                                                                        if(r.success === true) {
                                                                                            Ext.each(r.results, function (v) {
                                                                                                var data = {
                                                                                                    name: v.additional.name,
                                                                                                    additional_id: v.additional.id,
                                                                                                    amount: v.amount,
                                                                                                    subject_id: v.subject_id,
                                                                                                    subject_type: v.subject_type
                                                                                                };
                                                                                                Ext.getStore('CcpLinkedAdditionalsForm').add(data);
                                                                                            });
                                                                                        }
                                                                                    }
                                                                                });
                                                                            },
                                                                            iconCls: 'icon-disk_upload',
                                                                            text: 'Carica sconti da studente'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'datefield',
                                            hidden: true,
                                            id: 'CcpExpiratonDateField',
                                            itemId: 'CcpExpiratonDateField',
                                            fieldLabel: 'Scadenza',
                                            name: 'expiration_date',
                                            format: 'd/m/Y',
                                            submitFormat: 'c'
                                        },
                                        {
                                            xtype: 'numberfield',
                                            hidden: true,
                                            id: 'CcpAmountField',
                                            itemId: 'CcpAmountField',
                                            fieldLabel: 'Importo',
                                            name: 'amount',
                                            hideTrigger: true
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementSubjectId',
                                            name: 'subject_id'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementSubjectType',
                                            name: 'subject_type',
                                            value: 'O'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementSubjectData',
                                            name: 'subject_data'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementSubjectSeat',
                                            name: 'subject_seat'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementSubjectClass',
                                            name: 'subject_class'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementSubjectSchoolAddressCode',
                                            name: 'school_address_code'
                                        },
                                        {
                                            xtype: 'hiddenfield',
                                            id: 'CcpMovementSubjectSchoolAddress',
                                            name: 'school_address'
                                        },
                                        {
                                            xtype: 'textareafield',
                                            id: 'CcpMovementNote',
                                            margin: 5,
                                            fieldLabel: 'Note',
                                            name: 'note'
                                        },
                                        {
                                            xtype: 'combobox',
                                            hidden: true,
                                            id: 'CcpMatthaeusYearCmb',
                                            fieldLabel: 'Esercizio',
                                            name: 'matthaeus_year',
                                            displayField: 'nome',
                                            queryMode: 'local',
                                            store: 'MagisterEsercizi',
                                            valueField: 'id',
                                            listeners: {
                                                afterrender: {
                                                    fn: me.onComboboxAfterRender,
                                                    scope: me
                                                }
                                            }
                                        },
                                        {
                                            xtype: 'combobox',
                                            flex: 1,
                                            hidden: true,
                                            fieldLabel: 'Intestatario fattura'
                                        }
                                    ]
                                },
                                {
                                    xtype: 'container',
                                    flex: 1,
                                    layout: {
                                        type: 'vbox',
                                        align: 'stretch'
                                    },
                                    items: [
                                        {
                                            xtype: 'toolbar',
                                            items: [
                                                {
                                                    xtype: 'combobox',
                                                    flex: 1,
                                                    id: 'CcpStudentStatusFilterCmb',
                                                    padding: 5,
                                                    fieldLabel: 'Stato studenti',
                                                    displayField: 'descrizione',
                                                    forceSelection: true,
                                                    store: 'StudentStates',
                                                    valueField: 'id_stato_studente_personalizzato',
                                                    listeners: {
                                                        afterrender: {
                                                            fn: me.onComboboxAfterRender1,
                                                            scope: me
                                                        },
                                                        select: {
                                                            fn: me.onCcpStudentStatusFilterCmbSelect,
                                                            scope: me
                                                        }
                                                    }
                                                }
                                            ]
                                        },
                                        {
                                            xtype: 'container',
                                            flex: 1,
                                            layout: {
                                                type: 'hbox',
                                                align: 'stretch'
                                            },
                                            items: [
                                                {
                                                    xtype: 'panel',
                                                    flex: 1,
                                                    title: 'Ricerca e selezione multipla',
                                                    titleAlign: 'center',
                                                    layout: {
                                                        type: 'vbox',
                                                        align: 'stretch'
                                                    },
                                                    items: [
                                                        {
                                                            xtype: 'container',
                                                            padding: 5,
                                                            layout: 'fit'
                                                        },
                                                        {
                                                            xtype: 'container',
                                                            layout: {
                                                                type: 'hbox',
                                                                padding: 5
                                                            },
                                                            items: [
                                                                {
                                                                    xtype: 'combobox',
                                                                    flex: 1,
                                                                    id: 'CcpMovementSubject',
                                                                    fieldLabel: 'Soggetto',
                                                                    name: 'subject',
                                                                    emptyText: 'Studente ...',
                                                                    hideTrigger: true,
                                                                    displayField: 'display',
                                                                    forceSelection: true,
                                                                    queryMode: 'local',
                                                                    store: 'CcpSubjects',
                                                                    typeAhead: true,
                                                                    valueField: 'id',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onCcpMovementSubjectChange,
                                                                            delay: 300,
                                                                            buffer: 300,
                                                                            scope: me
                                                                        },
                                                                        select: {
                                                                            fn: me.onccpMovementSubjectSelect,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                },
                                                                {
                                                                    xtype: 'button',
                                                                    handler: function(button, e) {
                                                                        /*Ext.getCmp('CcpMovementSubject').setValue();
                                                                        //Ext.getCmp('CcpMovementSubject').select();

                                                                        //Ext.getCmp('CcpMovementSubjectClear').setDisabled(true);

                                                                        if (Ext.getCmp('CcpMovementSubjectId').getValue() > 0) {
                                                                            Ext.getCmp('CcpMovementSubjectData').setValue();
                                                                        }
                                                                        Ext.getCmp('CcpMovementSubjectId').setValue();
                                                                        Ext.getCmp('CcpMovementSubjectType').setValue('O');
                                                                        Ext.getCmp('CcpMovementSubjectSeat').setValue();
                                                                        Ext.getCmp('CcpMovementSubjectClass').setValue();

                                                                        Ext.getCmp('CcpMovementSubject').setDisabled(false);
                                                                        Ext.getCmp('CcpMovementSubjectOther').setDisabled(false);
                                                                        Ext.getCmp('CcpMovementCopyStudents').setDisabled(false);*/
                                                                    },
                                                                    hidden: true,
                                                                    id: 'CcpMovementSubjectClear',
                                                                    iconCls: 'icon-delete'
                                                                }
                                                            ]
                                                        },
                                                        {
                                                            xtype: 'textfield',
                                                            id: 'CcpMovementSubjectOther',
                                                            padding: 5,
                                                            fieldLabel: 'Altro',
                                                            hideEmptyLabel: false,
                                                            listeners: {
                                                                change: {
                                                                    fn: me.onCcpMovementSubjectOtherChange,
                                                                    scope: me
                                                                }
                                                            }
                                                        },
                                                        {
                                                            xtype: 'gridpanel',
                                                            flex: 1,
                                                            id: 'CcpAddMovementStudentSelect',
                                                            title: 'Selezione',
                                                            hideHeaders: true,
                                                            store: 'CcpAddMovementStudents',
                                                            columns: [
                                                                {
                                                                    xtype: 'gridcolumn',
                                                                    dataIndex: 'subject_data',
                                                                    text: 'Nome',
                                                                    flex: 1
                                                                },
                                                                {
                                                                    xtype: 'actioncolumn',
                                                                    width: 30,
                                                                    items: [
                                                                        {
                                                                            handler: function(view, rowIndex, colIndex, item, e, record, row) {
                                                                                Ext.getStore('CcpAddMovementStudents').remove(record);
                                                                            },
                                                                            iconCls: 'icon-delete'
                                                                        }
                                                                    ]
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                },
                                                {
                                                    xtype: 'treepanel',
                                                    flex: 1,
                                                    border: false,
                                                    height: 350,
                                                    id: 'CcpMovementCopyStudents',
                                                    autoScroll: true,
                                                    title: 'Studenti per classi/indirizzo',
                                                    titleAlign: 'center',
                                                    emptyText: 'Nessuno studente trovato.',
                                                    enableColumnHide: false,
                                                    enableColumnMove: false,
                                                    enableColumnResize: false,
                                                    store: 'CcpStudentsTree',
                                                    lines: false,
                                                    useArrows: true,
                                                    viewConfig: {
                                                        autoScroll: true
                                                    },
                                                    columns: [
                                                        {
                                                            xtype: 'treecolumn',
                                                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                                                if (rowIndex > 0) {
                                                                    return value + ' (' + record.get('movements') + ')';
                                                                }
                                                                return value;
                                                            },
                                                            dataIndex: 'text',
                                                            text: 'Nominativo',
                                                            flex: 1
                                                        }
                                                    ],
                                                    listeners: {
                                                        checkchange: {
                                                            fn: me.onCcpMovementCopyStudentsCheckChange,
                                                            scope: me
                                                        }
                                                    },
                                                    dockedItems: [
                                                        {
                                                            xtype: 'toolbar',
                                                            dock: 'top',
                                                            items: [
                                                                {
                                                                    xtype: 'checkboxfield',
                                                                    id: 'CcpFastTreeCheck',
                                                                    margin: '0 0 0 5',
                                                                    fieldLabel: 'Visualizza movimenti inseriti',
                                                                    labelWidth: 150,
                                                                    boxLabel: '',
                                                                    listeners: {
                                                                        change: {
                                                                            fn: me.onCheckboxfieldChange,
                                                                            scope: me
                                                                        }
                                                                    }
                                                                }
                                                            ]
                                                        }
                                                    ]
                                                }
                                            ]
                                        }
                                    ]
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    processCcpMovementTypeId: function(config) {
        config.tpl = Ext.create("Ext.XTemplate",
                                '<tpl for="."><div class="x-boundlist-item">({category_text}) - {name} <tpl if="school_year == \'TUTTI\'"> (TUTTI)</tpl></div></tpl>');
        // config.displayTpl = Ext.create("Ext.XTemplate", '<tpl for=".">({category_text}) - {name}</tpl>');
        return config;
    },

    onCcpMovementEditWinShow: function(component, eOpts) {
        Ext.getStore('CcpSubjects').clearFilter();
        if (Ext.getCmp('CcpMovementSubject').getValue()) {
            Ext.getCmp('CcpMovementSubjectClear').setDisabled(false);
        }

        Ext.getStore('CcpTypeSteps').removeAll();

        //Ext.getStore('CcpStudentsTree').load();

        Ext.getCmp('CcpMovementCopyStudents').getRootNode().cascadeBy(function() {
            this.set('checked', false);
        });

        Ext.getStore('CcpAddMovementStudents').removeAll();

        setTimeout(function() {
            Ext.getCmp('CcpRateGrid').getSelectionModel().selectAll();
        }, 1000);





    },

    onCcpMovementEditWinClose: function(panel, eOpts) {
        Ext.getStore('CcpTypes').clearFilter();
        Ext.getStore('CcpLinkedAdditionalsForm').removeAll();


    },

    onComboboxSelect: function(combo, records, eOpts) {
        var type_id = Ext.getCmp('CcpMovementTypeId').getValue(),
            studentStatus = parseInt(Ext.getCmp('CcpStudentStatusFilterCmb').getValue()),
            show_mov_count = Ext.getCmp('CcpFastTreeCheck').getValue();
        Ext.getStore('CcpSubjects').load({params: {subject_school_year: records[0].get('name'), student_status:studentStatus}});

        Ext.getCmp('CcpMovementEditWin').loadSubjectsTree();


        Ext.getCmp('CcpMovementTypeId').enable();
        Ext.getCmp('CcpMovementTypeId').setValue();
        Ext.getStore('CcpTypes').load({params: {school_year: records[0].get('name')}});



    },

    onCcpEditMovementSubjectYearCmbAfterRender: function(component, eOpts) {
        Ext.getStore('McDbs').filterBy(function(v){
            return v.get('current')===true;
        });
        var year = Ext.getStore('McDbs').getRange()[0];
        Ext.getStore('McDbs').clearFilter();
        if(year) {
            component.select(year);

            var type_id = Ext.getCmp('CcpMovementTypeId').getValue();


            Ext.getStore('CcpStudentsTree').load({params: {type_id: type_id, subject_school_year: year.get('name')}});
            Ext.getStore('CcpSubjects').load({params: {subject_school_year: year.get('name')}});


            Ext.getCmp('CcpMovementTypeId').enable();
            Ext.getCmp('CcpMovementTypeId').setValue();

            Ext.getStore('CcpTypes').load({params: {school_year: year.get('name')}});

        }


    },

    onCcpMovementTypeIdSelect: function(combo, records, eOpts) {
        var record = records[0],
            id = record.get('id');

        /*if(!record.get('easy_code').trim() && mc2ui.app.settings.magisterEnabled === true ){
            Ext.Msg.alert('ATTENZIONE', 'Il tipo di movimento selezionato non può essere utilizzato in quanto non è stato inserito il codice easy (piano dei conti)');
            combo.select();
            return;
        }*/

        if (!Ext.getCmp('CcpMovementEditWin').editMode) {
            var amount = records[0].get('amount'),
                additionals = records[0].get('linked_additionals'),
                invoice_code = records[0].get('invoice_code'),
                vat = records[0].get('vat'),
                sAdds = Ext.getStore('CcpLinkedAdditionalsForm');


            Ext.getCmp('CcpMovementInvoiceCode').setValue(invoice_code);
            // Ext.getCmp('CcpMovementAmount').setValue(amount);
            sAdds.removeAll();
            sAdds.load({
                params: {
                    type: 'T',
                    item: id
                },
                callback: function() {
                    //Ext.getCmp('CcpMovementEditWin').calculateTotal();
                }
            });
            /*if (incoming) {
                Ext.getCmp('').enable();
            } else {
                Ext.getCmp('CcpMovementSubject').disable();
                Ext.getCmp('CcpMovementSubject').setValue();
            }*/
            //Ext.getCmp('CcpMovementEditWin').calculateTotal();
            Ext.getStore('CcpTypeSteps').load({
                params: {'ccp_type': id},
                callback: function(res) {
                    var today = new Date(),
                        currentYear = today.getFullYear(),
                        typeSchoolYears,
                        dm, dateValue;

                    if (Ext.getStore('CcpTypeSteps').count() < 1) {
                        Ext.getStore('CcpTypeSteps').add({expiration_date: new Date(), value: 0, vat: vat, gross:0});
                    } else {
                        Ext.each(res, function(v){
                            if(v.get('expiration')) {
                                dm = v.get('expiration').split('/');
                                if(records[0].get('school_year') == 'TUTTI') {

                                    dateValue = new Date(currentYear + '-' + dm[1] + '-' + dm[0]);

                                    // La data è già passata
                                    if(dm[2]) {
                                        dateValue = new Date(dm[2] + '-' + dm[1] + '-' + dm[0]);
                                    } else if (dateValue < today) {
                                        dateValue.setYear(dateValue.getFullYear()+1);

                                    }
                                } else {
                                    typeSchoolYears = records[0].get('school_year').split('/');

                                    if(dm[2]) {
                                        dateValue = new Date(dm[2] + '-' + dm[1] + '-' + dm[0]);
                                    } else if (dm[1] >= 9) {
                                        dateValue = new Date(typeSchoolYears[0] + '-' + dm[1] + '-' + dm[0]);
                                    } else {
                                        dateValue = new Date(typeSchoolYears[1] + '-' + dm[1] + '-' + dm[0]);
                                    }


                                }
                                v.set('expiration_date', dateValue);
                            } else {
                                v.set('expiration_date', Ext.getCmp('CcpMovementCreationDate').getValue());
                            }
                            v.set('vat', vat);
                            v.set('gross', v.get('value'));

                            var gross = v.get('value'),
                                vatPerc = vat,
                                vatAbs = Math.round(gross/100*vatPerc*100)/100,
                                total = gross+vatAbs,
                                grossReverse = Math.round(total/(vatPerc/100+1)*100)/100;


                            v.set('value', total);

                            v.commit();
                        });
                    }
                    Ext.getCmp('CcpRateGrid').setTotal();
                    Ext.getCmp('CcpRateGrid').enable();
                    Ext.getCmp('CcpRateTotalCnt').enable();
                    Ext.getCmp('CcpRateGrid').getSelectionModel().selectAll();
                }
            });

        }
        setTimeout(function(){Ext.getStore('CcpTypes').clearFilter();}, 300);

        var subject_school_year = Ext.getCmp('CcpEditMovementSubjectYearCmb').getValue(),
            studentStatus = parseInt(Ext.getCmp('CcpStudentStatusFilterCmb').getValue()),
            show_mov_count = Ext.getCmp('CcpFastTreeCheck').getValue();


        Ext.getCmp('CcpMovementEditWin').loadSubjectsTree();
        // Ext.getStore('CcpStudentsTree').load({params: {type_id: id, subject_school_year: subject_school_year}});
        //Ext.getStore('CcpStudentsTree').load({params: {subject_school_year: subject_school_year,student_status:studentStatus,show_mov_count:show_mov_count}});
    },

    onCcpMovementTypeIdChange: function(field, newValue, oldValue, eOpts) {
        var store = Ext.getStore('CcpTypes');

        store.clearFilter();

        if (newValue) {
            store.filter({
                property: 'name',
                anyMatch: true,
                value   : newValue
            });
        }

        Ext.getCmp('CcpFastTreeCheck').setValue();
    },

    onRowEditingEdit1: function(editor, context, eOpts) {


        var gross = context.record.get('gross'),
            vatPerc = context.record.get('vat'),
            vat = Math.round(gross/100*vatPerc*100)/100,
            total = gross+vat,
            grossReverse = Math.round(total/(vatPerc/100+1)*100)/100;

        if( grossReverse !== gross && vatPerc > 0 ) {
            Ext.Msg.confirm('ATTENZIONE', 'La combinazione di imponibile/IVA inseriti non rende scorporabile il risultato. L\'imponibile verrà arrotondato al valore scorporabile oppure è possibile cambiare a mano l\'imponibile stesso. Procedere con l\'arrotondamento?', function(btn){

                if(btn !== 'yes') {
                    context.record.set('gross', grossReverse);
                    total = grossReverse + vat;
                } else {
                    context.record.set('gross', 0);
                    total=0;
                }
                context.record.set('value', total);
                context.record.commit();
                Ext.getCmp('CcpRateGrid').setTotal();
            });
        } else {
            context.record.set('value', total);
            context.record.commit();
            Ext.getCmp('CcpRateGrid').setTotal();
        }

    },

    onRowEditingBeforeEdit: function(editor, context, eOpts) {
        // TO FIX BUG ??? We save selection to reestablish after saving
        var grid = Ext.getCmp('CcpRateGrid');
        grid.selection = grid.getSelectionModel().getSelection();

        setTimeout(function(){
            var grid = Ext.getCmp('CcpRateGrid');
            grid.getSelectionModel().select(grid.selection);
        }, 300);
    },

    onComboboxRender: function(component, eOpts) {
        Ext.getStore('CcpAdditionals').load({params: {type:'D'}});
    },

    onComboboxSelect1: function(combo, records, eOpts) {
        var grid = Ext.getCmp('CcpMovementEditLinkedAdditionalsGrid'),
            plugin = grid.getPlugin('CcpMovementDiscountEditPlg'),
            r;

        plugin.editor.completeEdit();
        r = grid.getSelectionModel().getSelection()[0];
        r.set('amount', records[0].get('amount'));
        plugin.startEdit(r);

    },

    onRowEditingEdit: function(editor, context, eOpts) {
        context.record.set('name', Ext.getStore('CcpAdditionals').getById(context.newValues.additional_id).get('name'));

    },

    onCcpMovementEditLinkedAdditionalsGridItemContextMenu: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('CcpMovementEditAdditionalEditMn').showAt([newX,newY]);
    },

    onComboboxAfterRender: function(component, eOpts) {
        /*if(!mc2ui.app.settings.magisterEnabled) {
            component.hide();
            return;
        }

        var esercizi = Ext.getStore('MagisterEsercizi').getRange(),
            def;

        Ext.each(esercizi, function(esercizio){
            if(esercizio.get('esercizio_corrente') === true) {
                def = esercizio.get('id');
                return;
            }
        });

        component.setValue(def);*/
    },

    onComboboxAfterRender1: function(component, eOpts) {
        component.setValue(0);
    },

    onCcpStudentStatusFilterCmbSelect: function(combo, records, eOpts) {
        var type_id = Ext.getCmp('CcpMovementTypeId').getValue(),
            studentStatus = parseInt(Ext.getCmp('CcpStudentStatusFilterCmb').getValue()),
            show_mov_count = Ext.getCmp('CcpFastTreeCheck').getValue();

        Ext.getStore('CcpSubjects').load({params: {subject_school_year: records[0].get('name'), student_status:studentStatus}});
        Ext.getCmp('CcpMovementEditWin').loadSubjectsTree();
        //Ext.getStore('CcpStudentsTree').load({params: {type_id: type_id, subject_school_year: records[0].get('name'), student_status:studentStatus, show_mov_count:show_mov_count}});
    },

    onCcpMovementSubjectChange: function(field, newValue, oldValue, eOpts) {
        var store = field.store;

        /*if (newValue !== '') {
            Ext.getCmp('CcpMovementSubjectOther').setDisabled(true);
            Ext.getCmp('CcpMovementCopyStudents').setDisabled(true);
        } else {
            Ext.getCmp('CcpMovementSubjectOther').setDisabled(false);
            Ext.getCmp('CcpMovementCopyStudents').setDisabled(false);
        }*/

        store.clearFilter();
        store.filter({
            property: 'display',
            anyMatch: true,
            value   : field.getValue(),
            caseSensitive: false
        });
    },

    onccpMovementSubjectSelect: function(combo, records, eOpts) {
        var s = records[0],
            t = s.get('type') === 'S';

        /*Ext.getCmp('CcpMovementSubjectOther').setDisabled(true);
        Ext.getCmp('CcpMovementSubjectOther').setValue();
        Ext.getCmp('CcpMovementCopyStudents').setDisabled(true);
        mc2ui.app.treePropagateChange(Ext.getCmp('CcpMovementCopyStudents').getRootNode());*/

        /*Ext.getCmp('CcpMovementSubjectId').setValue(s.get('db_id'));
        Ext.getCmp('CcpMovementSubjectType').setValue(s.get('type'));
        Ext.getCmp('CcpMovementSubjectData').setValue(s.get('surname') + ' ' + s.get('name'));
        Ext.getCmp('CcpMovementSubjectSeat').setValue(t ? s.get('seat_id') : null);
        Ext.getCmp('CcpMovementSubjectClass').setValue(t ? s.get('class') + s.get('section') : '');
        Ext.getCmp('CcpMovementSubjectSchoolAddress').setValue(s.get('school_address'));
        Ext.getCmp('CcpMovementSubjectSchoolAddressCode').setValue(s.get('school_address_code'));
        */
        var found=false;
        Ext.each(Ext.getStore('CcpAddMovementStudents').getRange(), function(v) {
            if(v.get('subject_id')===s.get('db_id')){
                found=true;
                return;
            }
        });
        if (!found) {
            var student = {
                subject_id:s.get('db_id'),
                subject_type: s.get('type'),
                subject_data: s.get('surname') + ' ' + s.get('name'),
                subject_seat: t ? s.get('seat_id') : null,
                subject_class: t ? s.get('class') + s.get('section') : '',
                school_address: s.get('school_address'),
                school_address_code: s.get('school_address_code')
            };

            Ext.getStore('CcpAddMovementStudents').add(student);
        }
        combo.setValue();
    },

    onCcpMovementSubjectOtherChange: function(field, newValue, oldValue, eOpts) {
        if (newValue.length > 0) {
            Ext.getCmp('CcpMovementSubjectClear').handler();
            Ext.getCmp('CcpMovementSubject').setDisabled(true);
            Ext.getCmp('CcpMovementCopyStudents').setDisabled(true);
            mc2ui.app.treePropagateChange(Ext.getCmp('CcpMovementCopyStudents').getRootNode());
            //Ext.getCmp('CcpMovementSubjectClear').setDisabled(true);
            Ext.getCmp('CcpMovementSubjectData').setValue(newValue);
            Ext.getCmp('CcpMovementSubjectType').setValue('O');
        } else {
            Ext.getCmp('CcpMovementSubject').setDisabled(false);
            Ext.getCmp('CcpMovementCopyStudents').setDisabled(false);
            Ext.getCmp('CcpMovementSubjectData').setValue();
        }
    },

    onCcpMovementCopyStudentsCheckChange: function(node, checked, eOpts) {
        mc2ui.app.treePropagateChange(node);

        var students = Ext.getCmp('CcpMovementCopyStudents').getChecked();
            //movement = Ext.getCmp('CcpMovementCopyWin').movement;

        if (students.length > 0 /*&& movement*/) {
            // Ext.getCmp('CcpMovementCopyCopyBtn').setDisabled(false);
            Ext.getCmp('CcpMovementSubject').setDisabled(true);
            Ext.getCmp('CcpMovementSubjectOther').setDisabled(true);
            Ext.getCmp('CcpEditMovementSubjectYearCmb').disable();
        } else {
            Ext.getCmp('CcpEditMovementSubjectYearCmb').enable();
            Ext.getCmp('CcpMovementSubject').setDisabled(false);
            Ext.getCmp('CcpMovementSubjectOther').setDisabled(false);
            // Ext.getCmp('CcpMovementCopyCopyBtn').setDisabled(true);
        }
    },

    onCheckboxfieldChange: function(field, newValue, oldValue, eOpts) {
        var params = {
            subject_school_year: Ext.getCmp('CcpEditMovementSubjectYearCmb').getValue()
        };

        if (newValue === true) {
            params.type_id = Ext.getCmp('CcpMovementTypeId').getValue();
        } else {
            params.type_id = null;
        }
        //Ext.getStore('CcpStudentsTree').load({params: params});
        Ext.getCmp('CcpMovementEditWin').loadSubjectsTree();
    },

    saveMovement: function(go_payment, exec_payment) {
        var data = Ext.getCmp('CcpMovementEditForm').getForm().getValues(),
            adds = Ext.getStore('CcpLinkedAdditionalsForm').getRange(),
            //exps = Ext.getStore('CcpTypeSteps').getRange(),
            exps = Ext.getCmp('CcpRateGrid').getSelectionModel().getSelection(),
            sMovs = Ext.getStore('CcpMovements'),
            sAdds = Ext.getStore('CcpAdditionals'),
            sTypes = Ext.getStore('CcpTypes'),
            a = 'aggiunto',
            newMovement = data.id === "",
            lc = [],
            externalGrid = Ext.getCmp('CcpMovementsGrid');

        if(Ext.getCmp('CcpMovementEditWin').section === 'movement_student') {
            externalGrid = Ext.getCmp('CcpMovementsStudentGrid');
            sMovs = Ext.getStore('CcpStudentMovements');
        }

        Ext.getCmp('CcpMovementEditWin').setLoading(true);

        var error=false;
        Ext.each(Ext.getStore('CcpTypeSteps').getRange(), function(v, i){
            if(v.get('da_ratei') > v.get('a_ratei')) {
                error=true;
            }
        });
        if(error) {
            Ext.Msg.alert('ERRORE', 'Controllare che le date dei ratei siano corrette e che data inizio sia maggiore di data fine rateo');
            Ext.getCmp('CcpMovementEditWin').setLoading(false);
            return;
        }

        data.linked_additionals = [];
        adds.forEach(function(a) {
            data.linked_additionals.push([
                a.get('id'),
                a.get('amount'),
                a.get('additional_id'),
                a.get('subject_id'),
                a.get('subject_type')
            ]);
        });

        data.expirations = [];
        exps.forEach(function(a) {
            var e = {
                expiration: a.get('expiration_date'),
                description: a.get('description'),
                da_ratei: a.get('da_ratei'),
                a_ratei: a.get('a_ratei'),
                value: a.get('value'),
                conto_crediti: a.get('conto_crediti'),
                conto_ricavi: a.get('conto_ricavi'),
                conto_risconti: a.get('conto_risconti'),
            };
            data.expirations.push(e);
        });

        data.subjects = [];
        var s = {};
        if (!Ext.getCmp('CcpMovementSubjectOther').disabled) { // ALTRO
            s = {
                type: 'O',
                data: Ext.getCmp('CcpMovementSubjectOther').getValue()
            };
            if (s.data) data.subjects.push(s);

        } else if (!Ext.getCmp('CcpMovementSubject').disabled) { // STUDENTE SINGOLO
            Ext.each(Ext.getStore('CcpAddMovementStudents').getRange(), function(v){
                s = {
                    type: v.get('subject_type'),
                    data: v.get('subject_data'),
                    class: v.get('subject_class'),
                    id: v.get('subject_id'),
                    seat: v.get('subject_seat'),
                    school_address_code: v.get('school_address_code'),
                    school_address: v.get('school_address')
                };
                data.subjects.push(s);
            });

            /*s = {
                type: data.subject_type,
                data: data.subject_data,
                class: data.subject_class,
                id: data.subject_id,
                seat: data.subject_seat,
                school_address_code: data.school_address_code,
                school_address: data.school_address
            };
            data.subjects.push(s);*/
        } else {	// STUDENTE MULTIPLO
            var students = Ext.getCmp('CcpMovementCopyStudents').getChecked();
            Ext.each(students, function(stu) {
                if (stu.get('leaf') === true) {
                    s = {
                        type: 'S',
                        data: stu.get('text'),
                        class: stu.get('class')+stu.get('section'),
                        id: stu.get('db_id'),
                        seat: stu.get('seat_id'),
                        school_address_code: stu.get('school_address_code'),
                        school_address: stu.get('school_address')
                    };
                    data.subjects.push(s);
                }
            });

        }

        data.subject_school_year = Ext.getCmp('CcpEditMovementSubjectYearCmb').getValue();
        data.school_year = data.subject_school_year;

        // Saves the data
        if (!newMovement) {
            a = 'aggiornato';
            var r = externalGrid.getSelectionModel().getSelection()[0];
            r = sMovs.getById(r.get('id'));
            r.set('type_id', data.type_id);
            r.set('miscellaneous', data.miscellaneous);
            r.set('number', data.number);
            r.set('note', data.note);
            r.set('school_year', data.school_year);
            r.set('subject_id', data.subject_id);
            r.set('subject_type', data.subject_type);
            r.set('subject_data', data.subject_data);
            r.set('subject_seat', data.subject_seat);
            r.set('subject_class', data.subject_class);
            r.set('school_address_code', data.school_address_code);
            r.set('school_address', data.school_address);
            r.set('subject_school_year', data.subject_school_year);
            // r.set('subjects', data.subjects);
            // r.set('expirations', data.expirations);
            r.set('description', data.expirations[0].description);
            r.set('da_ratei', data.expirations[0].da_ratei);
            r.set('a_ratei', data.expirations[0].a_ratei);
            r.set('amount', data.amount);
            r.set('creation_date', data.creation_date);
            r.set('expiration_date', data.expiration_date);
            r.set('linked_additionals', data.linked_additionals);
            r.set('invoice_code', data.invoice_code);
            r.set('matthaeus_year', data.matthaeus_year);
            r.set('conto_crediti', data.expirations[0].conto_crediti);
            r.set('conto_ricavi', data.expirations[0].conto_ricavi);
            r.set('conto_risconti', data.expirations[0].conto_risconti);
        } else {
            sMovs.add(data);
        }

        if(exec_payment === true){
            sMovs.getProxy().setExtraParam('exec_payment', true);
        }

        // Syncs the record
        sMovs.sync({
            /*callback: function(batch) {
                var mid = batch.proxy.getReader().jsonData.results.id;

                Ext.getCmp('CcpMovementsGrid').getSelectionModel().deselectAll();

                sMovs.load({
                    callback: function(){
                        var r = Ext.getStore('CcpMovements').getById(mid),
                            g = Ext.getCmp('CcpMovementsGrid').getSelectionModel();

                        if(r){
                            g.select(r);
                        }
                        if(go_payment===true) {
                            Ext.getCmp('CcpMovementPnl').openPaymentWin();
                        }
                        Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                        mc2ui.app.showNotifySave();
                    }
                });
                sAdds.load();
                sTypes.load();
                Ext.getCmp('CcpMovementEditWin').close();

                        sMovs.load({
                    callback: function(){
                        var r = Ext.getStore('CcpMovements').getById(mid),
                            g = Ext.getCmp('CcpMovementsGrid').getSelectionModel();
                        g.select(r);
                    }
                });
                sAdds.load();
                sTypes.load();
            },*/
            success: function(store, b,c) {

                var res = store.proxy.getReader().jsonData.results;
                var mid;
                if (res.length > 0) {
                    mid = res[0].id;
                } else {
                    mid = res.id;
                }


                externalGrid.getSelectionModel().deselectAll();

                if(Ext.getCmp('CcpMovementEditWin').section === 'movement_student') {
                    Ext.getCmp('CcpStudentsGrd').filterByStudent(data.subject_id);
                } else {
                    sMovs.load({
                        callback: function(){
                            var r = Ext.getStore('CcpMovements').getById(mid),
                                g = externalGrid.getSelectionModel();

                            if(r){
                                g.select(r);
                            }
                            if(go_payment===true) {
                                Ext.getCmp('CcpMovementPnl').openPaymentWin();
                            }
                            Ext.getCmp('CcpMovementsFilterForm').loadByFilter();
                            mc2ui.app.showNotifySave();
                        }
                    });
                }
                sAdds.load();
                sTypes.load();
                Ext.getCmp('CcpMovementEditWin').close();

            },
            failure: function(store, action) {
                var res = store.proxy.getReader().jsonData;

                if (res.message) {
                    Ext.Msg.alert('Attenzione', res.message);
                } else {
                    Ext.Msg.alert('Attenzione', 'Movimento NON ' + a);
                }
                Ext.getCmp('CcpMovementEditWin').setLoading(false);
                if(Ext.getCmp('CcpMovementEditWin').section === 'movement_student') {
                    Ext.getCmp('CcpStudentsGrd').filterByStudent(data.subject_id);
                } else {
                    sMovs.load();
                }
            }
        });

        sMovs.getProxy().setExtraParam('exec_payment', false);
    },

    filterTypes: function(field, value, reset) {
        Ext.getCmp('CcpMovementTypeId').enable();
        if (reset === null) {
            Ext.getCmp('CcpMovementTypeId').setValue();
        }

        Ext.getStore('CcpTypes').clearFilter();

        Ext.getStore('CcpTypes').filterBy(function(record) {
            if (record.get(field) === value || record.get(field) === 'TUTTI') {
                return record;
            }
        });


    },

    loadSubjectsTree: function() {
        var type_id = Ext.getCmp('CcpMovementTypeId').getValue(),
            studentStatus = parseInt(Ext.getCmp('CcpStudentStatusFilterCmb').getValue()),
            show_mov_count = Ext.getCmp('CcpFastTreeCheck').getValue(),
            school_year= Ext.getCmp('CcpEditMovementSubjectYearCmb').getValue();

        Ext.getStore('CcpStudentsTree').load({
            params: {
                type_id: show_mov_count === true ? type_id : null,
                subject_school_year: school_year,
                student_status:studentStatus,
                show_mov_count:show_mov_count
            }
        });
    },

    calculateTotal: function() {
        var adds = Ext.getStore('CcpLinkedAdditionalsForm').getRange(),
            total = Ext.getCmp('CcpMovementAmount').getValue();

        Ext.each(adds, function(a) {
            var value = a.get('amount'),
                s = a.get('positive'),
                p = a.get('percentual');

            if (!p) {
                if (s) {
                    total += value;
                } else {
                    total -= value;
                }
            }
        });

        total = total < 0 ? 0 : total;

        Ext.each(adds, function(a) {
            var value = a.get('amount'),
                s = a.get('positive'),
                p = a.get('percentual');

            if (p) {
                if (s) {
                    total += ((total / 100) * value);
                } else {
                    total -= ((total / 100) * value);
                }
            }
        });

        Ext.getCmp('CcpMovementTotal').setValue(total < 0 ? 0 : total);
    },

    validateFields: function() {
        Ext.getCmp('CcpMovementSubject').validate();
        Ext.getCmp('CcpMovementTypeId').validate();
        Ext.getCmp('CcpMovementSubjectData').validate();
        Ext.getCmp('CcpMovementSubjectId').validate();
        Ext.getCmp('CcpMovementSubjectType').validate();
        Ext.getCmp('CcpMovementSubjectSeat').validate();
        Ext.getCmp('CcpMovementSubjectClass').validate();

        Ext.getCmp('CcpMovementEditForm').getForm().isValid();
    }

});