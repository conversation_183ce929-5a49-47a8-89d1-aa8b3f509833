/*
 * File: app/view/InstitutesWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.InstitutesWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.InstitutesWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Column',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.menu.Menu',
        'Ext.menu.Item'
    ],

    height: 400,
    id: 'InstitutesWin',
    itemId: 'InstitutesWin',
    width: 800,
    title: '<PERSON>ri Istituti',
    modal: true,

    layout: {
        type: 'vbox',
        align: 'stretch'
    },

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    permissible: true,
                    flex: 1,
                    border: false,
                    id: 'SettingsInstitutesGrid',
                    itemId: 'SettingsInstitutesGrid',
                    store: 'Institutes',
                    viewConfig: {
                        listeners: {
                            itemcontextmenu: {
                                fn: me.onGridviewItemContextMenu3,
                                scope: me
                            }
                        }
                    },
                    columns: [
                        {
                            xtype: 'gridcolumn',
                            width: 400,
                            dataIndex: 'name',
                            hideable: false,
                            text: 'Nome',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 133,
                            resizable: false,
                            dataIndex: 'mechan_code',
                            hideable: false,
                            text: 'Meccanografico'
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 150,
                            dataIndex: 'city',
                            hideable: false,
                            text: 'Città'
                        },
                        {
                            xtype: 'gridcolumn',
                            dataIndex: 'address',
                            hideable: false,
                            text: 'Indirizzo',
                            flex: 1
                        },
                        {
                            xtype: 'gridcolumn',
                            width: 100,
                            resizable: false,
                            dataIndex: 'fiscal_code',
                            hideable: false,
                            text: 'P. IVA'
                        }
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        Ext.widget('InstituteEditWin').show();
                                    },
                                    id: 'InstituteAddBtn',
                                    itemId: 'InstituteAddBtn',
                                    iconCls: 'icon-add',
                                    text: 'Nuovo'
                                }
                            ]
                        }
                    ]
                },
                {
                    xtype: 'menu',
                    permissible: true,
                    hidden: true,
                    id: 'InstituteEditMn',
                    itemId: 'InstituteEditMn',
                    items: [
                        {
                            xtype: 'menuitem',
                            id: 'contextInstituteEdit',
                            itemId: 'contextInstituteEdit',
                            iconCls: 'icon-pencil',
                            text: 'Modifica',
                            listeners: {
                                click: {
                                    fn: me.onMenuitemClick91,
                                    scope: me
                                }
                            }
                        },
                        {
                            xtype: 'menuitem',
                            id: 'contextInstituteDelete',
                            itemId: 'contextInstituteDelete',
                            iconCls: 'icon-cancel',
                            text: 'Cancella',
                            listeners: {
                                click: {
                                    fn: me.onMenuitemClick911,
                                    scope: me
                                }
                            }
                        }
                    ]
                }
            ],
            listeners: {
                boxready: {
                    fn: me.onInstitutesWinBoxReady,
                    scope: me
                }
            }
        });

        me.callParent(arguments);
    },

    onGridviewItemContextMenu3: function(dataview, record, item, index, e, eOpts) {
        e.stopEvent();
        var newX = e.xy[0];
        var newY = e.xy[1];
        Ext.getCmp('InstituteEditMn').showAt([newX,newY]);
    },

    onMenuitemClick91: function(item, e, eOpts) {
        var record = Ext.getCmp('SettingsInstitutesGrid').getSelectionModel().getSelection()[0];

        Ext.Ajax.request({
            method: 'GET',
            url : '/mc2/applications/core/institutes/read.php',
            params: {
                institute_id : record.get('institute_id')
            },
            success: function(res){
                var r = Ext.decode(res.responseText);
                if (r.success === true) {
                    Ext.widget('InstituteEditWin').show();
                    var mi = new mc2ui.model.Institute(r.results[0]);
                    Ext.getCmp('InstituteEditFrm').getForm().loadRecord(mi);
                }
            }
        });
    },

    onMenuitemClick911: function(item, e, eOpts) {
        var record = Ext.getCmp('SettingsInstitutesGrid').getSelectionModel().getSelection()[0],
            store = Ext.getStore('Institutes');

        Ext.Msg.show({
            title: record.get('name'),
            msg: 'Sei sicuro di voler eliminare questo istituto?',
            buttons: Ext.Msg.YESNO,
            fn: function(r){
                if (r == 'yes') {
                    store.remove(record);
                    store.sync();
                }
            }
        });


    },

    onInstitutesWinBoxReady: function(component, width, height, eOpts) {
        Ext.getStore('Institutes').load();
    }

});