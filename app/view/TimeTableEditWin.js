/*
 * File: app/view/TimeTableEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.TimeTableEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.TimeTableEditWin',

    requires: [
        'Ext.grid.Panel',
        'Ext.grid.View',
        'Ext.grid.column.Date',
        'Ext.form.field.Time',
        'Ext.grid.column.Number',
        'Ext.grid.plugin.CellEditing',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.Label'
    ],

    height: 128,
    id: 'TimeTableEditWin',
    itemId: 'TimeTableEditWin',
    width: 400,
    layout: 'fit',
    title: 'Orario',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'gridpanel',
                    border: false,
                    id: 'TimeTableEditGrid',
                    itemId: 'TimeTableEditGrid',
                    title: '',
                    enableColumnHide: false,
                    enableColumnMove: false,
                    enableColumnResize: false,
                    sortableColumns: false,
                    store: 'TimeTables',
                    columns: [
                        {
                            xtype: 'datecolumn',
                            hidden: true,
                            id: 'TimeTableDay',
                            itemId: 'TimeTableDay',
                            dataIndex: 'date_day',
                            text: 'MyDateColumn',
                            format: 'd/m/Y'
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if(value!=null){
                                    return Ext.Date.format(new Date(value),'H:i');
                                }
                            },
                            width: 69,
                            dataIndex: 'date_s',
                            text: 'Ora inizio',
                            flex: 1,
                            editor: {
                                xtype: 'timefield',
                                emptyText: '08:00',
                                format: 'H:i'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if(value!=null){
                                    return Ext.Date.format(new Date(value),'H:i');
                                }
                            },
                            width: 69,
                            dataIndex: 'date_s_p',
                            text: 'Ora inizio pausa',
                            flex: 1,
                            editor: {
                                xtype: 'timefield',
                                emptyText: '00:00',
                                format: 'H:i'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if(value!=null){
                                    return Ext.Date.format(new Date(value),'H:i');
                                }
                            },
                            width: 69,
                            dataIndex: 'date_e_p',
                            text: 'Ora fine pausa',
                            flex: 1,
                            editor: {
                                xtype: 'timefield',
                                emptyText: '00:00',
                                format: 'H:i'
                            }
                        },
                        {
                            xtype: 'gridcolumn',
                            renderer: function(value, metaData, record, rowIndex, colIndex, store, view) {
                                if(value!=null){
                                    return Ext.Date.format(new Date(value),'H:i');
                                }

                            },
                            width: 64,
                            dataIndex: 'date_e',
                            text: 'Ora fine',
                            flex: 1,
                            editor: {
                                xtype: 'timefield',
                                emptyText: '13:30',
                                format: 'H:i'
                            }
                        },
                        {
                            xtype: 'numbercolumn',
                            hidden: true,
                            id: 'TimeTableEmployeeId',
                            itemId: 'TimeTableEmployeeId',
                            dataIndex: 'employee_id',
                            text: 'TimeTableEmployee'
                        }
                    ],
                    plugins: [
                        Ext.create('Ext.grid.plugin.CellEditing', {
                            clicksToEdit: 1
                        })
                    ],
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    iconCls: 'icon-disk',
                                    text: 'Salva',
                                    listeners: {
                                        click: {
                                            fn: me.onButtonClick,
                                            scope: me
                                        }
                                    }
                                },
                                {
                                    xtype: 'label',
                                    id: 'PeriodTimetableEditLbl',
                                    itemId: 'PeriodTimetableEditLbl',
                                    style: 'font-weight:bold;',
                                    text: 'Data: '
                                }
                            ]
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    },

    onButtonClick: function(button, e, eOpts) {
        Ext.getCmp('TimeTableEditGrid').getStore().update({
            callback: function(){
                Ext.getCmp('TimeTableEditWin').close();
                Ext.getCmp('TimeTableTabPnl').loadData();
            }
        });



    }

});