/*
 * File: app/view/ArchiveDocumentEditWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.ArchiveDocumentEditWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.ArchiveDocumentEditWin',

    requires: [
        'Ext.form.Panel',
        'Ext.toolbar.Toolbar',
        'Ext.button.Button',
        'Ext.form.field.ComboBox',
        'Ext.form.field.TextArea',
        'Ext.form.FieldSet',
        'Ext.form.field.Checkbox',
        'Ext.form.field.Date',
        'Ext.form.field.Hidden'
    ],

    id: 'ArchiveDocumentEditWin',
    itemId: 'ArchiveDocumentEditWin',
    width: 500,
    resizable: false,
    layout: 'fit',
    title: 'Modifica documento da esaminare',
    modal: true,

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'form',
                    border: false,
                    id: 'ArchiveDocumentEditForm',
                    itemId: 'ArchiveDocumentEditForm',
                    bodyCls: 'bck-content',
                    bodyPadding: 10,
                    header: false,
                    layout: {
                        type: 'vbox',
                        align: 'stretch'
                    },
                    dockedItems: [
                        {
                            xtype: 'toolbar',
                            flex: 1,
                            dock: 'top',
                            items: [
                                {
                                    xtype: 'button',
                                    handler: function(button, e) {
                                        var shortDescription = Ext.getCmp('ArchiveDocumentEditShortDescription'),
                                            description = Ext.getCmp('ArchiveDocumentEditDescription'),
                                            model = Ext.getCmp('ArchiveDocumentEditModel'),
                                            expiration = Ext.getCmp('ArchiveDocumentEditExpirationDate'),
                                            win = Ext.getCmp('ArchiveDocumentEditWin'),
                                            record = win.record;


                                        record.set('short_description', shortDescription.getValue());
                                        record.set('description', description.getValue());
                                        record.set('model', model.getValue());
                                        record.set('expiration_date', expiration.getValue());

                                        record.store.save({
                                            callback: function() {
                                                record.store.load();
                                                win.close();
                                            },
                                            success: function(form, action) {
                                                mc2ui.app.showNotifySave();
                                            },
                                            failure: function(form, action) {
                                                Ext.Msg.alert('Attenzione', 'Documento NON modificato');
                                            }
                                        });
                                    },
                                    formBind: true,
                                    iconCls: 'icon-disk',
                                    text: 'Salva'
                                }
                            ]
                        }
                    ],
                    items: [
                        {
                            xtype: 'combobox',
                            id: 'ArchiveDocumentEditModel',
                            itemId: 'ArchiveDocumentEditModel',
                            fieldLabel: 'Modello',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'model',
                            emptyText: 'Abbina modello ...',
                            displayField: 'name',
                            forceSelection: true,
                            queryMode: 'local',
                            store: 'ArchiveTemplates',
                            typeAhead: true,
                            valueField: 'id'
                        },
                        {
                            xtype: 'textfield',
                            id: 'ArchiveDocumentEditShortDescription',
                            itemId: 'ArchiveDocumentEditShortDescription',
                            margin: '10 0 0 0',
                            fieldLabel: 'Breve descrizione',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'short_description'
                        },
                        {
                            xtype: 'textareafield',
                            flex: 1,
                            id: 'ArchiveDocumentEditDescription',
                            itemId: 'ArchiveDocumentEditDescription',
                            margin: '10 0 0 0',
                            fieldLabel: 'Note',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'description'
                        },
                        {
                            xtype: 'fieldset',
                            hidden: true,
                            title: 'Operazioni',
                            layout: {
                                type: 'vbox',
                                align: 'stretch'
                            },
                            items: [
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    id: 'ArchiveDocumentEditActionProtocol',
                                    itemId: 'ArchiveDocumentEditActionProtocol',
                                    name: 'action_protocol',
                                    boxLabel: 'Da protocollare',
                                    uncheckedValue: 'off'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    id: 'ArchiveDocumentEditActionAlbo',
                                    itemId: 'ArchiveDocumentEditActionAlbo',
                                    name: 'action_albo',
                                    boxLabel: 'Da pubblicare su Albo',
                                    uncheckedValue: 'off'
                                },
                                {
                                    xtype: 'checkboxfield',
                                    flex: 1,
                                    id: 'ArchiveDocumentEditActionTrasparenza',
                                    itemId: 'ArchiveDocumentEditActionTrasparenza',
                                    name: 'action_trasparenza',
                                    boxLabel: 'Da allegare a Trasparenza',
                                    uncheckedValue: 'off'
                                }
                            ]
                        },
                        {
                            xtype: 'datefield',
                            flex: 1,
                            id: 'ArchiveDocumentEditExpirationDate',
                            itemId: 'ArchiveDocumentEditExpirationDate',
                            fieldLabel: 'Scadenza',
                            labelAlign: 'right',
                            labelWidth: 120,
                            name: 'expiration_date'
                        },
                        {
                            xtype: 'hiddenfield',
                            id: 'ArchiveDocumentEditId',
                            itemId: 'ArchiveDocumentEditId',
                            name: 'id'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});