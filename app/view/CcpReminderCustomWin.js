/*
 * File: app/view/CcpReminderCustomWin.js
 *
 * This file was generated by Sencha Architect version 3.2.0.
 * http://www.sencha.com/products/architect/
 *
 * This file requires use of the Ext JS 4.2.x library, under independent license.
 * License of Sencha Architect does not include license for Ext JS 4.2.x. For more
 * details see http://www.sencha.com/license <NAME_EMAIL>.
 *
 * This file will be auto-generated each and everytime you save your project.
 *
 * Do NOT hand edit this file.
 */

Ext.define('mc2ui.view.CcpReminderCustomWin', {
    extend: 'Ext.window.Window',
    alias: 'widget.CcpReminderCustomWin',

    requires: [
        'Ext.form.field.HtmlEditor',
        'Ext.toolbar.Toolbar',
        'Ext.toolbar.Spacer',
        'Ext.button.Button'
    ],

    isWarning: false,
    height: 435,
    id: 'CcpReminderCustomWin',
    width: 688,
    layout: 'fit',
    title: 'Personalizza il messaggio da inviare',

    initComponent: function() {
        var me = this;

        Ext.applyIf(me, {
            items: [
                {
                    xtype: 'htmleditor',
                    height: 150,
                    id: 'CcpCustomReminderHtml',
                    fieldLabel: ''
                }
            ],
            dockedItems: [
                {
                    xtype: 'toolbar',
                    dock: 'bottom',
                    items: [
                        {
                            xtype: 'tbspacer',
                            flex: 1
                        },
                        {
                            xtype: 'button',
                            handler: function(button, e) {
                                var html = Ext.getCmp('CcpCustomReminderHtml').getValue(),
                                    messageType = Ext.getCmp('CcpReminderCustomWin').isWarning === true ? 'WARNING_CUSTOMIZED'  : 'INFO_CUSTOMIZED';
                                Ext.getCmp('CcpReminderMn').generateReminder(messageType, html);
                                Ext.getCmp('CcpReminderCustomWin').close();
                            },
                            iconCls: 'icon-add',
                            text: 'Crea'
                        }
                    ]
                }
            ]
        });

        me.callParent(arguments);
    }

});