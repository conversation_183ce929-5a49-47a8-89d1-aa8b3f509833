/**
 * This file is generated by Sencha Cmd and should NOT be edited.  It is
 * provided to support globbing requires, custom xtypes, and other
 * metadata-driven class system features
 */
/**
 * This file is generated by Sencha Cmd and should NOT be edited.  It is
 * provided to support globbing requires, custom xtypes, and other
 * metadata-driven class system features
 */
Ext.Loader.addClassPathMappings({
  "Ext": "ext/src",
  "Ext.Boot": ".sencha/app/Boot.js",
  "Ext.DomHelper": "ext/src/dom/Helper.js",
  "Ext.DomQuery": "ext/src/dom/Query.js",
  "Ext.EventObjectImpl": "ext/src/EventObject.js",
  "Ext.Msg": "ext/src/window/MessageBox.js",
  "Ext.Supports": "ext/src/Support.js",
  "Ext.chart.theme.Residuals": "app/view/CcpPnl.js",
  "Ext.core.DomHelper": "ext/src/dom/Helper.js",
  "Ext.core.DomQuery": "ext/src/dom/Query.js",
  "mc2ui": "app"
});

Ext.ClassManager.addNameAlternateMappings({
  "Ext.AbstractComponent": [],
  "Ext.AbstractManager": [],
  "Ext.AbstractPlugin": [],
  "Ext.Action": [],
  "Ext.Ajax": [],
  "Ext.Component": [],
  "Ext.ComponentLoader": [],
  "Ext.ComponentManager": [
    "Ext.ComponentMgr"
  ],
  "Ext.ComponentQuery": [],
  "Ext.Editor": [],
  "Ext.ElementLoader": [],
  "Ext.FocusManager": [
    "Ext.FocusMgr"
  ],
  "Ext.Img": [],
  "Ext.LoadMask": [],
  "Ext.ModelManager": [
    "Ext.ModelMgr"
  ],
  "Ext.PluginManager": [
    "Ext.PluginMgr"
  ],
  "Ext.ProgressBar": [],
  "Ext.Queryable": [],
  "Ext.Shadow": [],
  "Ext.ShadowPool": [],
  "Ext.ZIndexManager": [
    "Ext.WindowGroup"
  ],
  "Ext.app.Application": [],
  "Ext.app.Controller": [],
  "Ext.app.EventBus": [],
  "Ext.app.EventDomain": [],
  "Ext.app.domain.Component": [],
  "Ext.app.domain.Controller": [],
  "Ext.app.domain.Direct": [],
  "Ext.app.domain.Global": [],
  "Ext.app.domain.Store": [],
  "Ext.button.Button": [
    "Ext.Button"
  ],
  "Ext.button.Cycle": [
    "Ext.CycleButton"
  ],
  "Ext.button.Manager": [
    "Ext.ButtonToggleManager"
  ],
  "Ext.button.Split": [
    "Ext.SplitButton"
  ],
  "Ext.chart.Callout": [],
  "Ext.chart.Chart": [],
  "Ext.chart.Highlight": [],
  "Ext.chart.Label": [],
  "Ext.chart.Legend": [],
  "Ext.chart.LegendItem": [],
  "Ext.chart.Mask": [],
  "Ext.chart.MaskLayer": [],
  "Ext.chart.Navigation": [],
  "Ext.chart.Shape": [],
  "Ext.chart.Tip": [],
  "Ext.chart.TipSurface": [],
  "Ext.chart.axis.Abstract": [],
  "Ext.chart.axis.Axis": [
    "Ext.chart.Axis"
  ],
  "Ext.chart.axis.Category": [
    "Ext.chart.CategoryAxis"
  ],
  "Ext.chart.axis.Gauge": [],
  "Ext.chart.axis.Numeric": [
    "Ext.chart.NumericAxis"
  ],
  "Ext.chart.axis.Radial": [],
  "Ext.chart.axis.Time": [
    "Ext.chart.TimeAxis"
  ],
  "Ext.chart.series.Area": [],
  "Ext.chart.series.Bar": [
    "Ext.chart.BarSeries",
    "Ext.chart.BarChart",
    "Ext.chart.StackedBarChart"
  ],
  "Ext.chart.series.Cartesian": [
    "Ext.chart.CartesianSeries",
    "Ext.chart.CartesianChart"
  ],
  "Ext.chart.series.Column": [
    "Ext.chart.ColumnSeries",
    "Ext.chart.ColumnChart",
    "Ext.chart.StackedColumnChart"
  ],
  "Ext.chart.series.Gauge": [],
  "Ext.chart.series.Line": [
    "Ext.chart.LineSeries",
    "Ext.chart.LineChart"
  ],
  "Ext.chart.series.Pie": [
    "Ext.chart.PieSeries",
    "Ext.chart.PieChart"
  ],
  "Ext.chart.series.Radar": [],
  "Ext.chart.series.Scatter": [],
  "Ext.chart.series.Series": [],
  "Ext.chart.theme.Base": [],
  "Ext.chart.theme.Residuals": [],
  "Ext.chart.theme.Theme": [],
  "Ext.container.AbstractContainer": [],
  "Ext.container.ButtonGroup": [
    "Ext.ButtonGroup"
  ],
  "Ext.container.Container": [
    "Ext.Container"
  ],
  "Ext.container.DockingContainer": [],
  "Ext.container.Monitor": [],
  "Ext.container.Viewport": [
    "Ext.Viewport"
  ],
  "Ext.data.AbstractStore": [],
  "Ext.data.ArrayStore": [],
  "Ext.data.Batch": [],
  "Ext.data.BufferStore": [],
  "Ext.data.Connection": [],
  "Ext.data.DirectStore": [],
  "Ext.data.Errors": [],
  "Ext.data.Field": [],
  "Ext.data.Group": [],
  "Ext.data.IdGenerator": [],
  "Ext.data.JsonP": [],
  "Ext.data.JsonPStore": [],
  "Ext.data.JsonStore": [],
  "Ext.data.Model": [
    "Ext.data.Record"
  ],
  "Ext.data.NodeInterface": [],
  "Ext.data.NodeStore": [],
  "Ext.data.Operation": [],
  "Ext.data.PageMap": [],
  "Ext.data.Request": [],
  "Ext.data.ResultSet": [],
  "Ext.data.SequentialIdGenerator": [],
  "Ext.data.SortTypes": [],
  "Ext.data.Store": [],
  "Ext.data.StoreManager": [
    "Ext.StoreMgr",
    "Ext.data.StoreMgr",
    "Ext.StoreManager"
  ],
  "Ext.data.Tree": [],
  "Ext.data.TreeModel": [],
  "Ext.data.TreeStore": [],
  "Ext.data.Types": [],
  "Ext.data.UuidGenerator": [],
  "Ext.data.XmlStore": [],
  "Ext.data.association.Association": [
    "Ext.data.Association"
  ],
  "Ext.data.association.BelongsTo": [
    "Ext.data.BelongsToAssociation"
  ],
  "Ext.data.association.HasMany": [
    "Ext.data.HasManyAssociation"
  ],
  "Ext.data.association.HasOne": [
    "Ext.data.HasOneAssociation"
  ],
  "Ext.data.flash.BinaryXhr": [],
  "Ext.data.proxy.Ajax": [
    "Ext.data.HttpProxy",
    "Ext.data.AjaxProxy"
  ],
  "Ext.data.proxy.Client": [
    "Ext.data.ClientProxy"
  ],
  "Ext.data.proxy.Direct": [
    "Ext.data.DirectProxy"
  ],
  "Ext.data.proxy.JsonP": [
    "Ext.data.ScriptTagProxy"
  ],
  "Ext.data.proxy.LocalStorage": [
    "Ext.data.LocalStorageProxy"
  ],
  "Ext.data.proxy.Memory": [
    "Ext.data.MemoryProxy"
  ],
  "Ext.data.proxy.Proxy": [
    "Ext.data.DataProxy",
    "Ext.data.Proxy"
  ],
  "Ext.data.proxy.Rest": [
    "Ext.data.RestProxy"
  ],
  "Ext.data.proxy.Server": [
    "Ext.data.ServerProxy"
  ],
  "Ext.data.proxy.SessionStorage": [
    "Ext.data.SessionStorageProxy"
  ],
  "Ext.data.proxy.WebStorage": [
    "Ext.data.WebStorageProxy"
  ],
  "Ext.data.reader.Array": [
    "Ext.data.ArrayReader"
  ],
  "Ext.data.reader.Json": [
    "Ext.data.JsonReader"
  ],
  "Ext.data.reader.Reader": [
    "Ext.data.Reader",
    "Ext.data.DataReader"
  ],
  "Ext.data.reader.Xml": [
    "Ext.data.XmlReader"
  ],
  "Ext.data.validations": [],
  "Ext.data.writer.Json": [
    "Ext.data.JsonWriter"
  ],
  "Ext.data.writer.Writer": [
    "Ext.data.DataWriter",
    "Ext.data.Writer"
  ],
  "Ext.data.writer.Xml": [
    "Ext.data.XmlWriter"
  ],
  "Ext.dd.DD": [],
  "Ext.dd.DDProxy": [],
  "Ext.dd.DDTarget": [],
  "Ext.dd.DragDrop": [],
  "Ext.dd.DragDropManager": [
    "Ext.dd.DragDropMgr",
    "Ext.dd.DDM"
  ],
  "Ext.dd.DragSource": [],
  "Ext.dd.DragTracker": [],
  "Ext.dd.DragZone": [],
  "Ext.dd.DropTarget": [],
  "Ext.dd.DropZone": [],
  "Ext.dd.Registry": [],
  "Ext.dd.ScrollManager": [],
  "Ext.dd.StatusProxy": [],
  "Ext.diag.layout.Context": [],
  "Ext.diag.layout.ContextItem": [],
  "Ext.direct.Event": [],
  "Ext.direct.ExceptionEvent": [],
  "Ext.direct.JsonProvider": [],
  "Ext.direct.Manager": [],
  "Ext.direct.PollingProvider": [],
  "Ext.direct.Provider": [],
  "Ext.direct.RemotingEvent": [],
  "Ext.direct.RemotingMethod": [],
  "Ext.direct.RemotingProvider": [],
  "Ext.direct.Transaction": [
    "Ext.Direct.Transaction"
  ],
  "Ext.dom.Layer": [
    "Ext.Layer"
  ],
  "Ext.draw.Color": [],
  "Ext.draw.Component": [],
  "Ext.draw.CompositeSprite": [],
  "Ext.draw.Draw": [],
  "Ext.draw.Matrix": [],
  "Ext.draw.Sprite": [],
  "Ext.draw.SpriteDD": [],
  "Ext.draw.Surface": [],
  "Ext.draw.Text": [],
  "Ext.draw.engine.ImageExporter": [],
  "Ext.draw.engine.Svg": [],
  "Ext.draw.engine.SvgExporter": [],
  "Ext.draw.engine.Vml": [],
  "Ext.flash.Component": [
    "Ext.FlashComponent"
  ],
  "Ext.form.Basic": [
    "Ext.form.BasicForm"
  ],
  "Ext.form.CheckboxGroup": [],
  "Ext.form.CheckboxManager": [],
  "Ext.form.FieldAncestor": [],
  "Ext.form.FieldContainer": [],
  "Ext.form.FieldSet": [],
  "Ext.form.Label": [],
  "Ext.form.Labelable": [],
  "Ext.form.Panel": [
    "Ext.FormPanel",
    "Ext.form.FormPanel"
  ],
  "Ext.form.RadioGroup": [],
  "Ext.form.RadioManager": [],
  "Ext.form.action.Action": [
    "Ext.form.Action"
  ],
  "Ext.form.action.DirectLoad": [
    "Ext.form.Action.DirectLoad"
  ],
  "Ext.form.action.DirectSubmit": [
    "Ext.form.Action.DirectSubmit"
  ],
  "Ext.form.action.Load": [
    "Ext.form.Action.Load"
  ],
  "Ext.form.action.StandardSubmit": [],
  "Ext.form.action.Submit": [
    "Ext.form.Action.Submit"
  ],
  "Ext.form.field.Base": [
    "Ext.form.Field",
    "Ext.form.BaseField"
  ],
  "Ext.form.field.Checkbox": [
    "Ext.form.Checkbox"
  ],
  "Ext.form.field.ComboBox": [
    "Ext.form.ComboBox"
  ],
  "Ext.form.field.Date": [
    "Ext.form.DateField",
    "Ext.form.Date"
  ],
  "Ext.form.field.Display": [
    "Ext.form.DisplayField",
    "Ext.form.Display"
  ],
  "Ext.form.field.Field": [],
  "Ext.form.field.File": [
    "Ext.form.FileUploadField",
    "Ext.ux.form.FileUploadField",
    "Ext.form.File"
  ],
  "Ext.form.field.FileButton": [],
  "Ext.form.field.Hidden": [
    "Ext.form.Hidden"
  ],
  "Ext.form.field.HtmlEditor": [
    "Ext.form.HtmlEditor"
  ],
  "Ext.form.field.Number": [
    "Ext.form.NumberField",
    "Ext.form.Number"
  ],
  "Ext.form.field.Picker": [
    "Ext.form.Picker"
  ],
  "Ext.form.field.Radio": [
    "Ext.form.Radio"
  ],
  "Ext.form.field.Spinner": [
    "Ext.form.Spinner"
  ],
  "Ext.form.field.Text": [
    "Ext.form.TextField",
    "Ext.form.Text"
  ],
  "Ext.form.field.TextArea": [
    "Ext.form.TextArea"
  ],
  "Ext.form.field.Time": [
    "Ext.form.TimeField",
    "Ext.form.Time"
  ],
  "Ext.form.field.Trigger": [
    "Ext.form.TriggerField",
    "Ext.form.TwinTriggerField",
    "Ext.form.Trigger"
  ],
  "Ext.form.field.VTypes": [
    "Ext.form.VTypes"
  ],
  "Ext.fx.Anim": [],
  "Ext.fx.Animator": [],
  "Ext.fx.CubicBezier": [],
  "Ext.fx.Easing": [],
  "Ext.fx.Manager": [],
  "Ext.fx.PropertyHandler": [],
  "Ext.fx.Queue": [],
  "Ext.fx.target.Component": [],
  "Ext.fx.target.CompositeElement": [],
  "Ext.fx.target.CompositeElementCSS": [],
  "Ext.fx.target.CompositeSprite": [],
  "Ext.fx.target.Element": [],
  "Ext.fx.target.ElementCSS": [],
  "Ext.fx.target.Sprite": [],
  "Ext.fx.target.Target": [],
  "Ext.grid.CellContext": [],
  "Ext.grid.CellEditor": [],
  "Ext.grid.ColumnComponentLayout": [],
  "Ext.grid.ColumnLayout": [],
  "Ext.grid.ColumnManager": [
    "Ext.grid.ColumnModel"
  ],
  "Ext.grid.Panel": [
    "Ext.list.ListView",
    "Ext.ListView",
    "Ext.grid.GridPanel"
  ],
  "Ext.grid.RowEditor": [],
  "Ext.grid.RowEditorButtons": [],
  "Ext.grid.Scroller": [],
  "Ext.grid.View": [],
  "Ext.grid.ViewDropZone": [],
  "Ext.grid.column.Action": [
    "Ext.grid.ActionColumn"
  ],
  "Ext.grid.column.Boolean": [
    "Ext.grid.BooleanColumn"
  ],
  "Ext.grid.column.Check": [
    "Ext.ux.CheckColumn",
    "Ext.grid.column.CheckColumn"
  ],
  "Ext.grid.column.Column": [
    "Ext.grid.Column"
  ],
  "Ext.grid.column.Date": [
    "Ext.grid.DateColumn"
  ],
  "Ext.grid.column.Number": [
    "Ext.grid.NumberColumn"
  ],
  "Ext.grid.column.RowNumberer": [
    "Ext.grid.RowNumberer"
  ],
  "Ext.grid.column.Template": [
    "Ext.grid.TemplateColumn"
  ],
  "Ext.grid.feature.AbstractSummary": [],
  "Ext.grid.feature.Feature": [],
  "Ext.grid.feature.GroupStore": [],
  "Ext.grid.feature.Grouping": [],
  "Ext.grid.feature.GroupingSummary": [],
  "Ext.grid.feature.RowBody": [],
  "Ext.grid.feature.RowWrap": [],
  "Ext.grid.feature.Summary": [],
  "Ext.grid.header.Container": [],
  "Ext.grid.header.DragZone": [],
  "Ext.grid.header.DropZone": [],
  "Ext.grid.locking.HeaderContainer": [],
  "Ext.grid.locking.Lockable": [
    "Ext.grid.Lockable"
  ],
  "Ext.grid.locking.View": [
    "Ext.grid.LockingView"
  ],
  "Ext.grid.plugin.BufferedRenderer": [],
  "Ext.grid.plugin.BufferedRendererTableView": [],
  "Ext.grid.plugin.BufferedRendererTreeView": [],
  "Ext.grid.plugin.CellEditing": [],
  "Ext.grid.plugin.DivRenderer": [],
  "Ext.grid.plugin.DragDrop": [],
  "Ext.grid.plugin.Editing": [],
  "Ext.grid.plugin.HeaderReorderer": [],
  "Ext.grid.plugin.HeaderResizer": [],
  "Ext.grid.plugin.RowEditing": [],
  "Ext.grid.plugin.RowExpander": [],
  "Ext.grid.property.Grid": [
    "Ext.grid.PropertyGrid"
  ],
  "Ext.grid.property.HeaderContainer": [
    "Ext.grid.PropertyColumnModel"
  ],
  "Ext.grid.property.Property": [
    "Ext.PropGridProperty"
  ],
  "Ext.grid.property.Store": [
    "Ext.grid.PropertyStore"
  ],
  "Ext.layout.ClassList": [],
  "Ext.layout.Context": [],
  "Ext.layout.ContextItem": [],
  "Ext.layout.Layout": [],
  "Ext.layout.SizeModel": [],
  "Ext.layout.component.Auto": [],
  "Ext.layout.component.Body": [],
  "Ext.layout.component.BoundList": [],
  "Ext.layout.component.Button": [],
  "Ext.layout.component.Component": [],
  "Ext.layout.component.Dock": [
    "Ext.layout.component.AbstractDock"
  ],
  "Ext.layout.component.Draw": [],
  "Ext.layout.component.FieldSet": [],
  "Ext.layout.component.ProgressBar": [],
  "Ext.layout.component.field.ComboBox": [],
  "Ext.layout.component.field.Field": [],
  "Ext.layout.component.field.FieldContainer": [],
  "Ext.layout.component.field.HtmlEditor": [],
  "Ext.layout.component.field.Slider": [],
  "Ext.layout.component.field.Text": [],
  "Ext.layout.component.field.TextArea": [],
  "Ext.layout.component.field.Trigger": [],
  "Ext.layout.container.Absolute": [
    "Ext.layout.AbsoluteLayout"
  ],
  "Ext.layout.container.Accordion": [
    "Ext.layout.AccordionLayout"
  ],
  "Ext.layout.container.Anchor": [
    "Ext.layout.AnchorLayout"
  ],
  "Ext.layout.container.Auto": [],
  "Ext.layout.container.Border": [
    "Ext.layout.BorderLayout"
  ],
  "Ext.layout.container.Box": [
    "Ext.layout.BoxLayout"
  ],
  "Ext.layout.container.Card": [
    "Ext.layout.CardLayout"
  ],
  "Ext.layout.container.CheckboxGroup": [],
  "Ext.layout.container.Column": [
    "Ext.layout.ColumnLayout"
  ],
  "Ext.layout.container.Container": [
    "Ext.layout.ContainerLayout"
  ],
  "Ext.layout.container.Editor": [],
  "Ext.layout.container.Fit": [
    "Ext.layout.FitLayout"
  ],
  "Ext.layout.container.Form": [
    "Ext.layout.FormLayout"
  ],
  "Ext.layout.container.HBox": [
    "Ext.layout.HBoxLayout"
  ],
  "Ext.layout.container.Table": [
    "Ext.layout.TableLayout"
  ],
  "Ext.layout.container.VBox": [
    "Ext.layout.VBoxLayout"
  ],
  "Ext.layout.container.border.Region": [],
  "Ext.layout.container.boxOverflow.Menu": [
    "Ext.layout.boxOverflow.Menu"
  ],
  "Ext.layout.container.boxOverflow.None": [
    "Ext.layout.boxOverflow.None"
  ],
  "Ext.layout.container.boxOverflow.Scroller": [
    "Ext.layout.boxOverflow.Scroller"
  ],
  "Ext.menu.CheckItem": [],
  "Ext.menu.ColorPicker": [],
  "Ext.menu.DatePicker": [],
  "Ext.menu.Item": [
    "Ext.menu.TextItem"
  ],
  "Ext.menu.KeyNav": [],
  "Ext.menu.Manager": [
    "Ext.menu.MenuMgr"
  ],
  "Ext.menu.Menu": [],
  "Ext.menu.Separator": [],
  "Ext.panel.AbstractPanel": [],
  "Ext.panel.DD": [],
  "Ext.panel.Header": [],
  "Ext.panel.Panel": [
    "Ext.Panel"
  ],
  "Ext.panel.Proxy": [
    "Ext.dd.PanelProxy"
  ],
  "Ext.panel.Table": [],
  "Ext.panel.Tool": [],
  "Ext.picker.Color": [
    "Ext.ColorPalette"
  ],
  "Ext.picker.Date": [
    "Ext.DatePicker"
  ],
  "Ext.picker.Month": [
    "Ext.MonthPicker"
  ],
  "Ext.picker.Time": [],
  "Ext.resizer.BorderSplitter": [],
  "Ext.resizer.BorderSplitterTracker": [],
  "Ext.resizer.Handle": [],
  "Ext.resizer.ResizeTracker": [],
  "Ext.resizer.Resizer": [
    "Ext.Resizable"
  ],
  "Ext.resizer.Splitter": [],
  "Ext.resizer.SplitterTracker": [],
  "Ext.rtl.AbstractComponent": [],
  "Ext.rtl.EventObjectImpl": [],
  "Ext.rtl.button.Button": [],
  "Ext.rtl.chart.Chart": [],
  "Ext.rtl.chart.Legend": [],
  "Ext.rtl.chart.LegendItem": [],
  "Ext.rtl.chart.axis.Axis": [],
  "Ext.rtl.chart.axis.Gauge": [],
  "Ext.rtl.chart.series.Cartesian": [],
  "Ext.rtl.chart.series.Gauge": [],
  "Ext.rtl.dd.DD": [],
  "Ext.rtl.dom.Element_anim": [],
  "Ext.rtl.dom.Element_insertion": [],
  "Ext.rtl.dom.Element_position": [],
  "Ext.rtl.dom.Element_scroll": [],
  "Ext.rtl.dom.Element_static": [],
  "Ext.rtl.dom.Layer": [],
  "Ext.rtl.draw.Component": [],
  "Ext.rtl.draw.Sprite": [],
  "Ext.rtl.form.field.Checkbox": [],
  "Ext.rtl.form.field.File": [],
  "Ext.rtl.form.field.Spinner": [],
  "Ext.rtl.form.field.Trigger": [],
  "Ext.rtl.grid.CellEditor": [],
  "Ext.rtl.grid.RowEditor": [],
  "Ext.rtl.grid.column.Column": [],
  "Ext.rtl.grid.feature.Summary": [],
  "Ext.rtl.grid.plugin.HeaderResizer": [],
  "Ext.rtl.grid.plugin.RowEditing": [],
  "Ext.rtl.layout.ContextItem": [],
  "Ext.rtl.layout.component.Dock": [],
  "Ext.rtl.layout.component.field.Text": [],
  "Ext.rtl.layout.component.field.Trigger": [],
  "Ext.rtl.layout.container.Absolute": [],
  "Ext.rtl.layout.container.Border": [],
  "Ext.rtl.layout.container.Box": [],
  "Ext.rtl.layout.container.CheckboxGroup": [],
  "Ext.rtl.layout.container.Column": [],
  "Ext.rtl.layout.container.HBox": [],
  "Ext.rtl.layout.container.VBox": [],
  "Ext.rtl.layout.container.boxOverflow.Menu": [],
  "Ext.rtl.layout.container.boxOverflow.Scroller": [],
  "Ext.rtl.panel.Header": [],
  "Ext.rtl.panel.Panel": [],
  "Ext.rtl.resizer.BorderSplitterTracker": [],
  "Ext.rtl.resizer.ResizeTracker": [],
  "Ext.rtl.resizer.SplitterTracker": [],
  "Ext.rtl.selection.CellModel": [],
  "Ext.rtl.selection.TreeModel": [],
  "Ext.rtl.slider.Multi": [],
  "Ext.rtl.tab.Bar": [],
  "Ext.rtl.tip.QuickTipManager": [],
  "Ext.rtl.tree.Column": [],
  "Ext.rtl.util.Floating": [],
  "Ext.rtl.util.Renderable": [],
  "Ext.rtl.view.Table": [],
  "Ext.selection.CellModel": [],
  "Ext.selection.CheckboxModel": [],
  "Ext.selection.DataViewModel": [],
  "Ext.selection.Model": [
    "Ext.AbstractSelectionModel"
  ],
  "Ext.selection.RowModel": [],
  "Ext.selection.TreeModel": [],
  "Ext.slider.Multi": [
    "Ext.slider.MultiSlider"
  ],
  "Ext.slider.Single": [
    "Ext.Slider",
    "Ext.form.SliderField",
    "Ext.slider.SingleSlider",
    "Ext.slider.Slider"
  ],
  "Ext.slider.Thumb": [],
  "Ext.slider.Tip": [],
  "Ext.state.CookieProvider": [],
  "Ext.state.LocalStorageProvider": [],
  "Ext.state.Manager": [],
  "Ext.state.Provider": [],
  "Ext.state.Stateful": [],
  "Ext.tab.Bar": [],
  "Ext.tab.Panel": [
    "Ext.TabPanel"
  ],
  "Ext.tab.Tab": [],
  "Ext.tip.QuickTip": [
    "Ext.QuickTip"
  ],
  "Ext.tip.QuickTipManager": [
    "Ext.QuickTips"
  ],
  "Ext.tip.Tip": [
    "Ext.Tip"
  ],
  "Ext.tip.ToolTip": [
    "Ext.ToolTip"
  ],
  "Ext.toolbar.Fill": [
    "Ext.Toolbar.Fill"
  ],
  "Ext.toolbar.Item": [
    "Ext.Toolbar.Item"
  ],
  "Ext.toolbar.Paging": [
    "Ext.PagingToolbar"
  ],
  "Ext.toolbar.Separator": [
    "Ext.Toolbar.Separator"
  ],
  "Ext.toolbar.Spacer": [
    "Ext.Toolbar.Spacer"
  ],
  "Ext.toolbar.TextItem": [
    "Ext.Toolbar.TextItem"
  ],
  "Ext.toolbar.Toolbar": [
    "Ext.Toolbar"
  ],
  "Ext.tree.Column": [],
  "Ext.tree.Panel": [
    "Ext.tree.TreePanel",
    "Ext.TreePanel"
  ],
  "Ext.tree.View": [],
  "Ext.tree.ViewDragZone": [],
  "Ext.tree.ViewDropZone": [],
  "Ext.tree.plugin.TreeViewDragDrop": [],
  "Ext.util.AbstractMixedCollection": [],
  "Ext.util.Animate": [],
  "Ext.util.Bindable": [],
  "Ext.util.CSS": [],
  "Ext.util.ClickRepeater": [],
  "Ext.util.ComponentDragger": [],
  "Ext.util.Cookies": [],
  "Ext.util.ElementContainer": [],
  "Ext.util.Filter": [],
  "Ext.util.Floating": [],
  "Ext.util.Grouper": [],
  "Ext.util.HashMap": [],
  "Ext.util.History": [
    "Ext.History"
  ],
  "Ext.util.Inflector": [],
  "Ext.util.KeyMap": [
    "Ext.KeyMap"
  ],
  "Ext.util.KeyNav": [
    "Ext.KeyNav"
  ],
  "Ext.util.LocalStorage": [],
  "Ext.util.LruCache": [],
  "Ext.util.Memento": [],
  "Ext.util.MixedCollection": [],
  "Ext.util.Offset": [],
  "Ext.util.Point": [],
  "Ext.util.ProtoElement": [],
  "Ext.util.Queue": [],
  "Ext.util.Region": [],
  "Ext.util.Renderable": [],
  "Ext.util.Sortable": [],
  "Ext.util.Sorter": [],
  "Ext.util.TextMetrics": [],
  "Ext.ux.BoxReorderer": [],
  "Ext.ux.CellDragDrop": [],
  "Ext.ux.DataTip": [],
  "Ext.ux.DataView.Animated": [],
  "Ext.ux.DataView.DragSelector": [],
  "Ext.ux.DataView.Draggable": [],
  "Ext.ux.DataView.LabelEditor": [],
  "Ext.ux.FieldReplicator": [],
  "Ext.ux.GMapPanel": [],
  "Ext.ux.GroupTabPanel": [],
  "Ext.ux.GroupTabRenderer": [],
  "Ext.ux.IFrame": [],
  "Ext.ux.LiveSearchGridPanel": [],
  "Ext.ux.PreviewPlugin": [],
  "Ext.ux.ProgressBarPager": [],
  "Ext.ux.RowExpander": [],
  "Ext.ux.SlidingPager": [],
  "Ext.ux.Spotlight": [],
  "Ext.ux.TabCloseMenu": [],
  "Ext.ux.TabReorderer": [],
  "Ext.ux.TabScrollerMenu": [],
  "Ext.ux.ToolbarDroppable": [],
  "Ext.ux.TreePicker": [],
  "Ext.ux.ajax.DataSimlet": [],
  "Ext.ux.ajax.JsonSimlet": [],
  "Ext.ux.ajax.SimManager": [],
  "Ext.ux.ajax.SimXhr": [],
  "Ext.ux.ajax.Simlet": [],
  "Ext.ux.ajax.XmlSimlet": [],
  "Ext.ux.data.PagingMemoryProxy": [
    "Ext.data.PagingMemoryProxy"
  ],
  "Ext.ux.dd.CellFieldDropZone": [],
  "Ext.ux.dd.PanelFieldDragZone": [],
  "Ext.ux.event.Driver": [],
  "Ext.ux.event.Maker": [],
  "Ext.ux.event.Player": [],
  "Ext.ux.event.Recorder": [],
  "Ext.ux.event.RecorderManager": [],
  "Ext.ux.form.ItemSelector": [
    "Ext.ux.ItemSelector"
  ],
  "Ext.ux.form.MultiSelect": [
    "Ext.ux.Multiselect"
  ],
  "Ext.ux.form.SearchField": [],
  "Ext.ux.grid.FiltersFeature": [],
  "Ext.ux.grid.TransformGrid": [],
  "Ext.ux.grid.filter.BooleanFilter": [],
  "Ext.ux.grid.filter.DateFilter": [],
  "Ext.ux.grid.filter.DateTimeFilter": [],
  "Ext.ux.grid.filter.Filter": [],
  "Ext.ux.grid.filter.ListFilter": [],
  "Ext.ux.grid.filter.NumericFilter": [],
  "Ext.ux.grid.filter.StringFilter": [],
  "Ext.ux.grid.menu.ListMenu": [],
  "Ext.ux.grid.menu.RangeMenu": [],
  "Ext.ux.layout.Center": [],
  "Ext.ux.statusbar.StatusBar": [
    "Ext.ux.StatusBar"
  ],
  "Ext.ux.statusbar.ValidationStatus": [],
  "Ext.view.AbstractView": [],
  "Ext.view.BoundList": [
    "Ext.BoundList"
  ],
  "Ext.view.BoundListKeyNav": [],
  "Ext.view.DragZone": [],
  "Ext.view.DropZone": [],
  "Ext.view.NodeCache": [],
  "Ext.view.Table": [],
  "Ext.view.TableLayout": [],
  "Ext.view.View": [
    "Ext.DataView"
  ],
  "Ext.window.MessageBox": [],
  "Ext.window.Window": [
    "Ext.Window"
  ],
  "mc2ui.controller.PermissionController": [],
  "mc2ui.model.Absence": [],
  "mc2ui.model.AbsenceKind": [],
  "mc2ui.model.AbsenceStack": [],
  "mc2ui.model.AlboArea": [],
  "mc2ui.model.AlboCategory": [],
  "mc2ui.model.AlboEntity": [],
  "mc2ui.model.AlboHistory": [],
  "mc2ui.model.AlboPublication": [],
  "mc2ui.model.ArchiveClass": [],
  "mc2ui.model.ArchiveClassStep": [],
  "mc2ui.model.ArchiveDocument": [],
  "mc2ui.model.ArchiveDocumentFile": [],
  "mc2ui.model.ArchiveDossier": [],
  "mc2ui.model.ArchiveMail": [],
  "mc2ui.model.ArchiveMailAccount": [],
  "mc2ui.model.ArchiveMailAttachment": [],
  "mc2ui.model.ArchiveMetadata": [],
  "mc2ui.model.ArchiveOffice": [],
  "mc2ui.model.ArchiveOrigin": [],
  "mc2ui.model.ArchiveTemplate": [],
  "mc2ui.model.Assignee": [],
  "mc2ui.model.BudgetActivities": [],
  "mc2ui.model.CcpAdditional": [],
  "mc2ui.model.CcpAdditionalLinked": [],
  "mc2ui.model.CcpBollettino": [],
  "mc2ui.model.CcpCategory": [],
  "mc2ui.model.CcpDeposit": [],
  "mc2ui.model.CcpDepositSlip": [],
  "mc2ui.model.CcpInvoice": [],
  "mc2ui.model.CcpInvoiceDepositSlip": [],
  "mc2ui.model.CcpInvoiceTransmission": [],
  "mc2ui.model.CcpMovement": [],
  "mc2ui.model.CcpParent": [],
  "mc2ui.model.CcpPayer": [],
  "mc2ui.model.CcpPayment": [],
  "mc2ui.model.CcpPaymentMethod": [],
  "mc2ui.model.CcpReceipt": [],
  "mc2ui.model.CcpReminder": [],
  "mc2ui.model.CcpReport": [],
  "mc2ui.model.CcpResidual": [],
  "mc2ui.model.CcpSchoolYear": [],
  "mc2ui.model.CcpServizi": [],
  "mc2ui.model.CcpStudent": [],
  "mc2ui.model.CcpStudentMarket": [],
  "mc2ui.model.CcpSubject": [],
  "mc2ui.model.CcpType": [],
  "mc2ui.model.CcpTypeStep": [],
  "mc2ui.model.CcpVatCode": [],
  "mc2ui.model.Classe": [],
  "mc2ui.model.Contact": [],
  "mc2ui.model.ContactGroup": [],
  "mc2ui.model.CoreBankAccount": [],
  "mc2ui.model.CoreCity": [],
  "mc2ui.model.CoreCountry": [],
  "mc2ui.model.CoreParameter": [],
  "mc2ui.model.CorePrintSpool": [],
  "mc2ui.model.Corrispettivo": [],
  "mc2ui.model.Credit": [],
  "mc2ui.model.CreditType": [],
  "mc2ui.model.DataLookupResult": [],
  "mc2ui.model.DayTimeTable": [],
  "mc2ui.model.Decreto": [],
  "mc2ui.model.Discount": [],
  "mc2ui.model.DocumentFlow": [],
  "mc2ui.model.DocumentStep": [],
  "mc2ui.model.Dossier": [],
  "mc2ui.model.Employee": [],
  "mc2ui.model.EmployeeParameters": [],
  "mc2ui.model.EmployeeTree": [],
  "mc2ui.model.EventError": [],
  "mc2ui.model.ExtraordinaryAbsStack": [],
  "mc2ui.model.ExtraordinaryStored": [],
  "mc2ui.model.GenericSearch": [],
  "mc2ui.model.HomeInfo": [],
  "mc2ui.model.HomeReleaseNote": [],
  "mc2ui.model.Indirizzo": [],
  "mc2ui.model.Institute": [],
  "mc2ui.model.InstituteType": [],
  "mc2ui.model.Invoice": [],
  "mc2ui.model.MC2Parameter": [],
  "mc2ui.model.MC2Table": [],
  "mc2ui.model.MagisterEsercizio": [],
  "mc2ui.model.MyModel": [],
  "mc2ui.model.Permission": [],
  "mc2ui.model.PersonnelHourType": [],
  "mc2ui.model.PersonnelHourTypeLinked": [],
  "mc2ui.model.PersonnelProject": [],
  "mc2ui.model.PersonnelProjectLinked": [],
  "mc2ui.model.Presence": [],
  "mc2ui.model.ProtocolAction": [],
  "mc2ui.model.ProtocolCorrespondent": [],
  "mc2ui.model.ProtocolCorrespondentOrigin": [],
  "mc2ui.model.ProtocolHistory": [],
  "mc2ui.model.ProtocolProtocol": [],
  "mc2ui.model.ProtocolSendMethod": [],
  "mc2ui.model.ProtocolSubjectKind": [],
  "mc2ui.model.ProtocolType": [],
  "mc2ui.model.Raccolte": [],
  "mc2ui.model.Register": [],
  "mc2ui.model.RemoteClass": [],
  "mc2ui.model.SettingsGroup": [],
  "mc2ui.model.SettingsUser": [],
  "mc2ui.model.SocialPosition": [],
  "mc2ui.model.StackPersonnelLink": [],
  "mc2ui.model.StudentBalance": [],
  "mc2ui.model.Supplier": [],
  "mc2ui.model.TimeTable": [],
  "mc2ui.model.TrasparenzaVoice": [],
  "mc2ui.model.UserLog": [],
  "mc2ui.model.WeekBorder": [],
  "mc2ui.store.AbsenceKindGrouped": [],
  "mc2ui.store.AbsenceKinds": [],
  "mc2ui.store.AbsenceKindsLinked": [],
  "mc2ui.store.AbsenceStackUnits": [],
  "mc2ui.store.AbsenceStacks": [],
  "mc2ui.store.AbsenceStacksAndExtraordinary": [],
  "mc2ui.store.Absences": [],
  "mc2ui.store.AddressBook": [],
  "mc2ui.store.AlboAreas": [],
  "mc2ui.store.AlboAreasFilter": [],
  "mc2ui.store.AlboCategories": [],
  "mc2ui.store.AlboCategoriesFilter": [],
  "mc2ui.store.AlboCategoryDurations": [],
  "mc2ui.store.AlboDocumentsForm": [],
  "mc2ui.store.AlboEntities": [],
  "mc2ui.store.AlboEntitiesFilter": [],
  "mc2ui.store.AlboExpirationsFilter": [],
  "mc2ui.store.AlboHistories": [],
  "mc2ui.store.AlboLinkedDocuments": [],
  "mc2ui.store.AlboLinkedDocumentsForm": [],
  "mc2ui.store.AlboPublications": [],
  "mc2ui.store.AlboPublishedFilter": [],
  "mc2ui.store.AlboStatusesFilter": [],
  "mc2ui.store.ArchiveActionAlboFilter": [],
  "mc2ui.store.ArchiveActionProtocolFilter": [],
  "mc2ui.store.ArchiveActionTrasparenzaFilter": [],
  "mc2ui.store.ArchiveCheckedFilter": [],
  "mc2ui.store.ArchiveClassMetadata": [],
  "mc2ui.store.ArchiveClassSteps": [],
  "mc2ui.store.ArchiveClasses": [],
  "mc2ui.store.ArchiveClassesFilter": [],
  "mc2ui.store.ArchiveCompletedFilter": [],
  "mc2ui.store.ArchiveDashboard": [],
  "mc2ui.store.ArchiveDocumentDossier": [],
  "mc2ui.store.ArchiveDocumentDossierLinked": [],
  "mc2ui.store.ArchiveDocumentDossierMc": [],
  "mc2ui.store.ArchiveDocumentFiles": [],
  "mc2ui.store.ArchiveDocumentMails": [],
  "mc2ui.store.ArchiveDocumentsArchived": [],
  "mc2ui.store.ArchiveDocumentsOffice": [],
  "mc2ui.store.ArchiveDocumentsUser": [],
  "mc2ui.store.ArchiveDossiers": [],
  "mc2ui.store.ArchiveMailAccounts": [],
  "mc2ui.store.ArchiveMailAttachments": [],
  "mc2ui.store.ArchiveMailProtocols": [],
  "mc2ui.store.ArchiveMailSecurity": [],
  "mc2ui.store.ArchiveMailSendContact": [],
  "mc2ui.store.ArchiveMails": [],
  "mc2ui.store.ArchiveMailsOffice": [],
  "mc2ui.store.ArchiveMailsUser": [],
  "mc2ui.store.ArchiveMetadata": [],
  "mc2ui.store.ArchiveMetadataFiles": [],
  "mc2ui.store.ArchiveOfficeUsers": [],
  "mc2ui.store.ArchiveOffices": [],
  "mc2ui.store.ArchiveOrigins": [],
  "mc2ui.store.ArchiveOriginsFilter": [],
  "mc2ui.store.ArchiveServices": [],
  "mc2ui.store.ArchiveSignTypes": [],
  "mc2ui.store.ArchiveStores": [],
  "mc2ui.store.ArchiveTemplates": [],
  "mc2ui.store.Assignees": [],
  "mc2ui.store.BolTaxesType": [],
  "mc2ui.store.BudgetActivities": [],
  "mc2ui.store.CalendarHoliday": [],
  "mc2ui.store.CalendarTimeTables": [],
  "mc2ui.store.CcpAddMovementStudents": [],
  "mc2ui.store.CcpAdditionals": [],
  "mc2ui.store.CcpAdditionalsForm": [],
  "mc2ui.store.CcpAeCategories": [],
  "mc2ui.store.CcpBollettini": [],
  "mc2ui.store.CcpCategories": [],
  "mc2ui.store.CcpCategoriesFilter": [],
  "mc2ui.store.CcpCategoriesFilter1": [],
  "mc2ui.store.CcpConsolidations": [],
  "mc2ui.store.CcpDateTypeFilter": [],
  "mc2ui.store.CcpDepositSlips": [],
  "mc2ui.store.CcpDeposits": [],
  "mc2ui.store.CcpDirectionsFilter": [],
  "mc2ui.store.CcpFamilies": [],
  "mc2ui.store.CcpInvoiceAccountHolders": [],
  "mc2ui.store.CcpInvoiceDepositSlips": [],
  "mc2ui.store.CcpInvoiceMovements": [],
  "mc2ui.store.CcpInvoiceTransmissions": [],
  "mc2ui.store.CcpInvoices": [],
  "mc2ui.store.CcpInvoicesToDepositSlip": [],
  "mc2ui.store.CcpLinkedAdditionals": [],
  "mc2ui.store.CcpLinkedAdditionalsForm": [],
  "mc2ui.store.CcpLinkedPayments": [],
  "mc2ui.store.CcpMcPrintTemplate": [],
  "mc2ui.store.CcpMovements": [],
  "mc2ui.store.CcpMovementsCover": [],
  "mc2ui.store.CcpParents": [],
  "mc2ui.store.CcpPastiConsumati": [],
  "mc2ui.store.CcpPastiRim": [],
  "mc2ui.store.CcpPayerTypes": [],
  "mc2ui.store.CcpPayerTypesFilter": [],
  "mc2ui.store.CcpPayers": [],
  "mc2ui.store.CcpPaymentDestinations": [],
  "mc2ui.store.CcpPaymentDestinationsFilter": [],
  "mc2ui.store.CcpPaymentMethods": [],
  "mc2ui.store.CcpPaymentMethodsFilter": [],
  "mc2ui.store.CcpPayments": [],
  "mc2ui.store.CcpPaymentsUnreceipted": [],
  "mc2ui.store.CcpPrintCategories": [],
  "mc2ui.store.CcpPrintCategoryMovementTypes": [],
  "mc2ui.store.CcpPrintGroupings": [],
  "mc2ui.store.CcpPrintOfCategories": [],
  "mc2ui.store.CcpPrintStudentCols": [],
  "mc2ui.store.CcpPrintTypes": [],
  "mc2ui.store.CcpPrintWhat": [],
  "mc2ui.store.CcpReceiptAddressCode": [],
  "mc2ui.store.CcpReceipts": [],
  "mc2ui.store.CcpReminderDetails": [],
  "mc2ui.store.CcpReminders": [],
  "mc2ui.store.CcpReportStudentsParents": [],
  "mc2ui.store.CcpReports": [],
  "mc2ui.store.CcpResiduals": [],
  "mc2ui.store.CcpResidualsGroupings": [],
  "mc2ui.store.CcpResidualsTypes": [],
  "mc2ui.store.CcpResidualsYears": [],
  "mc2ui.store.CcpSchoolYearsFilter": [],
  "mc2ui.store.CcpServiceGrouping": [],
  "mc2ui.store.CcpServizi": [],
  "mc2ui.store.CcpStudentMarkets": [],
  "mc2ui.store.CcpStudentMovements": [],
  "mc2ui.store.CcpStudentMovementsMensa": [],
  "mc2ui.store.CcpStudentReceipts": [],
  "mc2ui.store.CcpStudents": [],
  "mc2ui.store.CcpStudentsDiscount": [],
  "mc2ui.store.CcpStudentsPayments": [],
  "mc2ui.store.CcpStudentsTree": [],
  "mc2ui.store.CcpSubjectTypes": [],
  "mc2ui.store.CcpSubjectTypesFilter": [],
  "mc2ui.store.CcpSubjects": [],
  "mc2ui.store.CcpTypeSchoolYears": [],
  "mc2ui.store.CcpTypeSteps": [],
  "mc2ui.store.CcpTypes": [],
  "mc2ui.store.CcpTypesFilter": [],
  "mc2ui.store.CcpTypesFilter1": [],
  "mc2ui.store.CcpVatCodes": [],
  "mc2ui.store.Classi": [],
  "mc2ui.store.Classi1": [],
  "mc2ui.store.ContactGroupLinked": [],
  "mc2ui.store.ContactGroups": [],
  "mc2ui.store.Contacts": [],
  "mc2ui.store.CoreBankAccountTypes": [],
  "mc2ui.store.CoreBankAccounts": [],
  "mc2ui.store.CoreCities": [],
  "mc2ui.store.CoreCountries": [],
  "mc2ui.store.CoreParameter": [],
  "mc2ui.store.CorePrintSpool": [],
  "mc2ui.store.Corrispettivi": [],
  "mc2ui.store.Credits": [],
  "mc2ui.store.CreditsType": [],
  "mc2ui.store.CreditsTypePaymentFilter": [],
  "mc2ui.store.DataLookupResults": [],
  "mc2ui.store.Decreti": [],
  "mc2ui.store.Discounts": [],
  "mc2ui.store.DocumentFlows": [],
  "mc2ui.store.EmpUnitRecoverHours": [],
  "mc2ui.store.EmployeeParameters": [],
  "mc2ui.store.Employees": [],
  "mc2ui.store.EmployeesAll": [],
  "mc2ui.store.EmployeesTree": [],
  "mc2ui.store.EmployeesTreeActive": [],
  "mc2ui.store.EventErrors": [],
  "mc2ui.store.ExtraordinaryAbsStack": [],
  "mc2ui.store.ExtraordinaryStored": [],
  "mc2ui.store.Gender": [],
  "mc2ui.store.GenericSearches": [],
  "mc2ui.store.HomeInfos": [],
  "mc2ui.store.HomeReleaseNotes": [],
  "mc2ui.store.InOut": [],
  "mc2ui.store.InOutType": [],
  "mc2ui.store.Indirizzi": [],
  "mc2ui.store.Indirizzi1": [],
  "mc2ui.store.Institutes": [],
  "mc2ui.store.InvoiceExpirations": [],
  "mc2ui.store.Invoices": [],
  "mc2ui.store.ListaFratelli": [],
  "mc2ui.store.MC2Parameters": [],
  "mc2ui.store.MC2Tables": [],
  "mc2ui.store.MagisterEsercizi": [],
  "mc2ui.store.MastertrainingContacts": [],
  "mc2ui.store.McDbs": [],
  "mc2ui.store.Months": [],
  "mc2ui.store.MyModels": [],
  "mc2ui.store.OLDCcpDepositSlipRows": [],
  "mc2ui.store.Permissions": [],
  "mc2ui.store.PersonnelHourTypes": [],
  "mc2ui.store.PersonnelPersonnelProjects": [],
  "mc2ui.store.PersonnelProjectHourTypes": [],
  "mc2ui.store.PersonnelProjects": [],
  "mc2ui.store.Presences": [],
  "mc2ui.store.PrintFormats": [],
  "mc2ui.store.ProtocolActions": [],
  "mc2ui.store.ProtocolBarcodes": [],
  "mc2ui.store.ProtocolCorrespondents": [],
  "mc2ui.store.ProtocolCorrespondentsFilter": [],
  "mc2ui.store.ProtocolCorrespondentsForm": [],
  "mc2ui.store.ProtocolCorrespondentsOrigins": [],
  "mc2ui.store.ProtocolCorrespondentsOriginsForm": [],
  "mc2ui.store.ProtocolDirectionsFilter": [],
  "mc2ui.store.ProtocolDocumentsForm": [],
  "mc2ui.store.ProtocolHistories": [],
  "mc2ui.store.ProtocolLinkedCorrespondents": [],
  "mc2ui.store.ProtocolLinkedCorrespondentsForm": [],
  "mc2ui.store.ProtocolLinkedDocuments": [],
  "mc2ui.store.ProtocolLinkedDocumentsForm": [],
  "mc2ui.store.ProtocolLinkedProtocols": [],
  "mc2ui.store.ProtocolLinkedProtocolsForm": [],
  "mc2ui.store.ProtocolProtocols": [],
  "mc2ui.store.ProtocolProtocolsForm": [],
  "mc2ui.store.ProtocolSendMethods": [],
  "mc2ui.store.ProtocolSendMethodsFilter": [],
  "mc2ui.store.ProtocolSignPositions": [],
  "mc2ui.store.ProtocolSubjectKinds": [],
  "mc2ui.store.ProtocolSubjectKindsFilter": [],
  "mc2ui.store.ProtocolTypes": [],
  "mc2ui.store.ProtocolTypesFilter": [],
  "mc2ui.store.ProtocolTypesLeaf": [],
  "mc2ui.store.ProtocolTypesTree": [],
  "mc2ui.store.Raccoltes": [],
  "mc2ui.store.Register": [],
  "mc2ui.store.ReminderTypes": [],
  "mc2ui.store.RemoteClass": [],
  "mc2ui.store.SchoolTypes": [],
  "mc2ui.store.SettingsGroups": [],
  "mc2ui.store.SettingsUsers": [],
  "mc2ui.store.SocialPosition": [],
  "mc2ui.store.StackResetTypes": [],
  "mc2ui.store.StacksPersonnelLinks": [],
  "mc2ui.store.StudentBalances": [],
  "mc2ui.store.StudentStates": [],
  "mc2ui.store.Students": [],
  "mc2ui.store.Suppliers": [],
  "mc2ui.store.TimeTables": [],
  "mc2ui.store.TrasparenzaDocumentsForm": [],
  "mc2ui.store.TrasparenzaLinkedDocuments": [],
  "mc2ui.store.TrasparenzaLinkedDocumentsForm": [],
  "mc2ui.store.TrasparenzaVoicesPickerTree": [],
  "mc2ui.store.TrasparenzaVoicesTree": [],
  "mc2ui.store.UserLogs": [],
  "mc2ui.store.WeekBorders": [],
  "mc2ui.store.Years": [],
  "mc2ui.view.AbsenceDecretoWin": [],
  "mc2ui.view.AbsenceNewWin": [],
  "mc2ui.view.AdminDataLookupWin": [],
  "mc2ui.view.AdminParametersWin": [],
  "mc2ui.view.AlboAreaEditWin": [],
  "mc2ui.view.AlboAreasWin": [],
  "mc2ui.view.AlboCategoriesWin": [],
  "mc2ui.view.AlboCategoryEditWin": [],
  "mc2ui.view.AlboEntitiesWin": [],
  "mc2ui.view.AlboEntityEditWin": [],
  "mc2ui.view.AlboPnl": [],
  "mc2ui.view.AlboPublicationEditWin": [],
  "mc2ui.view.AlboPublicationExtensionWin": [],
  "mc2ui.view.AlboPublicationHistoryWin": [],
  "mc2ui.view.AlboPublicationLinkedDocumentsPickerWin": [],
  "mc2ui.view.AlboPublicationLinkedDocumentsWin": [],
  "mc2ui.view.ArchiveClassWin": [],
  "mc2ui.view.ArchiveDashboardWin": [],
  "mc2ui.view.ArchiveDocumentArchiveWin": [],
  "mc2ui.view.ArchiveDocumentCheckWin": [],
  "mc2ui.view.ArchiveDocumentEditWin": [],
  "mc2ui.view.ArchiveDocumentMailWin": [],
  "mc2ui.view.ArchiveDocumentUploadWin": [],
  "mc2ui.view.ArchiveDocumentViewWin": [],
  "mc2ui.view.ArchiveDossierNewWin": [],
  "mc2ui.view.ArchiveDossierWin": [],
  "mc2ui.view.ArchiveMailSendWin": [],
  "mc2ui.view.ArchiveMailViewWin": [],
  "mc2ui.view.ArchiveManagementWin": [],
  "mc2ui.view.ArchiveMassiveSignWin": [],
  "mc2ui.view.ArchiveMetadataFileWin": [],
  "mc2ui.view.ArchiveOfficeWin": [],
  "mc2ui.view.ArchivePnl": [],
  "mc2ui.view.ArchiveSignRemoteLogin": [],
  "mc2ui.view.ArchiveSignRemoteOtpWin": [],
  "mc2ui.view.ArchiveSignRemoteWin": [],
  "mc2ui.view.ArchiveSignWin": [],
  "mc2ui.view.ArchiveTemplateWin": [],
  "mc2ui.view.CcpAdditionalEditWin": [],
  "mc2ui.view.CcpAdditionalTemplateWin": [],
  "mc2ui.view.CcpAdditionalsWin": [],
  "mc2ui.view.CcpAdePrintFromFileWin": [],
  "mc2ui.view.CcpAeExportWin": [],
  "mc2ui.view.CcpAttestazionePrintWin": [],
  "mc2ui.view.CcpBollettiniPrintWin": [],
  "mc2ui.view.CcpByCCPrintWin": [],
  "mc2ui.view.CcpCashJournalPrintWin": [],
  "mc2ui.view.CcpCategoriesWin": [],
  "mc2ui.view.CcpCategoryEditWin": [],
  "mc2ui.view.CcpCorrispettiviPrintWin": [],
  "mc2ui.view.CcpCreditDistributionWin": [],
  "mc2ui.view.CcpCreditTypeWin": [],
  "mc2ui.view.CcpCreditUploadWin": [],
  "mc2ui.view.CcpDepositSlipNewWin": [],
  "mc2ui.view.CcpDepositSlipRowWin": [],
  "mc2ui.view.CcpDepositWin": [],
  "mc2ui.view.CcpDichiarazionePrintWin": [],
  "mc2ui.view.CcpDiscountWin": [],
  "mc2ui.view.CcpExcelMovementExportWin": [],
  "mc2ui.view.CcpExportResidualsWin": [],
  "mc2ui.view.CcpExtraTimeWin": [],
  "mc2ui.view.CcpInvoiceAccountHolderWin": [],
  "mc2ui.view.CcpInvoiceEditWin": [],
  "mc2ui.view.CcpInvoiceNewWin": [],
  "mc2ui.view.CcpInvoiceTransmissionWin": [],
  "mc2ui.view.CcpLinkedAdditionalsPickerWin": [],
  "mc2ui.view.CcpLinkedAdditionalsWin": [],
  "mc2ui.view.CcpLinkedPaymentsWin": [],
  "mc2ui.view.CcpMovementCopyWin": [],
  "mc2ui.view.CcpMovementEditWin": [],
  "mc2ui.view.CcpPaymentEditWin": [],
  "mc2ui.view.CcpPaymentMassiveEditWin": [],
  "mc2ui.view.CcpPnl": [],
  "mc2ui.view.CcpPrintCategoriesWin": [],
  "mc2ui.view.CcpPrintCategoryMovementTypeWin": [],
  "mc2ui.view.CcpPrintTypeWin": [],
  "mc2ui.view.CcpPvrUploadWin": [],
  "mc2ui.view.CcpReceiptEditMultiWin": [],
  "mc2ui.view.CcpReceiptEditWin": [],
  "mc2ui.view.CcpReminderCustomWin": [],
  "mc2ui.view.CcpResidualsWin": [],
  "mc2ui.view.CcpServiziWin": [],
  "mc2ui.view.CcpSignLoginWin": [],
  "mc2ui.view.CcpStudentsMissing": [],
  "mc2ui.view.CcpTypeEditWin": [],
  "mc2ui.view.CcpTypesWin": [],
  "mc2ui.view.CcpYearsToSaveWin": [],
  "mc2ui.view.ContactAddWin": [],
  "mc2ui.view.DispatcherView": [],
  "mc2ui.view.EasyImportWin": [],
  "mc2ui.view.EmployeeAbsenceCopyWin": [],
  "mc2ui.view.EmployeeAbsenceStackEditWin": [],
  "mc2ui.view.EmployeeAbsenceStackWin": [],
  "mc2ui.view.EmployeeAbsencesPrintWin": [],
  "mc2ui.view.EmployeeDayPrintWin": [],
  "mc2ui.view.EmployeeEntriesExitsPrint_Win": [],
  "mc2ui.view.EmployeeHolidayCalendarWin": [],
  "mc2ui.view.EmployeeLastLockedMonthPrintWin": [],
  "mc2ui.view.EmployeeParametersCopyWin": [],
  "mc2ui.view.EmployeePnl": [],
  "mc2ui.view.EmployeePresencesPrintWin": [],
  "mc2ui.view.EmployeeProjectsEditWin": [],
  "mc2ui.view.EmployeeProjectsHourTypeEditWin": [],
  "mc2ui.view.EmployeeProjectsHourTypesWin": [],
  "mc2ui.view.EmployeeProjectsProjectEditWin": [],
  "mc2ui.view.EmployeeResidualsPrint_Win": [],
  "mc2ui.view.EmployeeStacksCopyWin": [],
  "mc2ui.view.EmployeeTimetablePrintWin": [],
  "mc2ui.view.ExportEasyWin": [],
  "mc2ui.view.GroupEditWin": [],
  "mc2ui.view.HomePnl": [],
  "mc2ui.view.ImportPaymentsEasyWin": [],
  "mc2ui.view.InstituteEditWin": [],
  "mc2ui.view.InstitutesWin": [],
  "mc2ui.view.InvoiceEditWin": [],
  "mc2ui.view.LoginPasswordExpired": [],
  "mc2ui.view.LoginView": [],
  "mc2ui.view.MailingListWin": [],
  "mc2ui.view.MainView": [],
  "mc2ui.view.MpExtraTimeEditWin": [],
  "mc2ui.view.MyPanel27": [],
  "mc2ui.view.MyPanel29": [],
  "mc2ui.view.MyPanel3": [],
  "mc2ui.view.MyPanel9": [],
  "mc2ui.view.PaswdEditWin": [],
  "mc2ui.view.PresencesNewWin": [],
  "mc2ui.view.ProtocolActionsWin": [],
  "mc2ui.view.ProtocolCorrespondentEditOriginPickerWin": [],
  "mc2ui.view.ProtocolCorrespondentEditWin": [],
  "mc2ui.view.ProtocolCorrespondentsWin": [],
  "mc2ui.view.ProtocolLinkedCorrespondentsWin": [],
  "mc2ui.view.ProtocolLinkedDocumentsWin": [],
  "mc2ui.view.ProtocolLinkedProtocolsWin": [],
  "mc2ui.view.ProtocolPnl": [],
  "mc2ui.view.ProtocolPrintsBarcodeWin": [],
  "mc2ui.view.ProtocolProtocolHistoryWin": [],
  "mc2ui.view.ProtocolProtocolNewLinkedCorrespondentsPickerWin": [],
  "mc2ui.view.ProtocolProtocolNewLinkedDocumentsPickerWin": [],
  "mc2ui.view.ProtocolProtocolNewLinkedProtocolsPickerWin": [],
  "mc2ui.view.ProtocolProtocolNewWin": [],
  "mc2ui.view.ProtocolSendMethodEditWin": [],
  "mc2ui.view.ProtocolSendMethodsWin": [],
  "mc2ui.view.ProtocolSubjectKindEditWin": [],
  "mc2ui.view.ProtocolSubjectKindsWin": [],
  "mc2ui.view.ProtocolTypeEditWin": [],
  "mc2ui.view.ProtocolTypesWin": [],
  "mc2ui.view.RaccoltaAssignWin": [],
  "mc2ui.view.RegisterAddWin": [],
  "mc2ui.view.RegisterDetailWin": [],
  "mc2ui.view.RegisterWin": [],
  "mc2ui.view.ReminderDetailWin": [],
  "mc2ui.view.SettingsPanel": [],
  "mc2ui.view.TimeTableCopyWeekWin": [],
  "mc2ui.view.TimeTableDeleteWin": [],
  "mc2ui.view.TimeTableEditWin": [],
  "mc2ui.view.TrasparenzaLinkedDocumentsPickerWin": [],
  "mc2ui.view.TrasparenzaLinkedDocumentsWin": [],
  "mc2ui.view.TrasparenzaPnl": [],
  "mc2ui.view.TrasparenzaVoiceEditWin": [],
  "mc2ui.view.TrasparenzaVoicePickerWin": [],
  "mc2ui.view.UploadDocumentReportWin": [],
  "mc2ui.view.UserEditWin": []
});

Ext.ClassManager.addNameAliasMappings({
  "Ext.AbstractComponent": [],
  "Ext.AbstractManager": [],
  "Ext.AbstractPlugin": [],
  "Ext.Action": [],
  "Ext.Ajax": [],
  "Ext.Component": [
    "widget.box",
    "widget.component"
  ],
  "Ext.ComponentLoader": [],
  "Ext.ComponentManager": [],
  "Ext.ComponentQuery": [],
  "Ext.Editor": [
    "widget.editor"
  ],
  "Ext.ElementLoader": [],
  "Ext.FocusManager": [],
  "Ext.Img": [
    "widget.image",
    "widget.imagecomponent"
  ],
  "Ext.LoadMask": [
    "widget.loadmask"
  ],
  "Ext.ModelManager": [],
  "Ext.PluginManager": [],
  "Ext.ProgressBar": [
    "widget.progressbar"
  ],
  "Ext.Queryable": [],
  "Ext.Shadow": [],
  "Ext.ShadowPool": [],
  "Ext.ZIndexManager": [],
  "Ext.app.Application": [],
  "Ext.app.Controller": [],
  "Ext.app.EventBus": [],
  "Ext.app.EventDomain": [],
  "Ext.app.domain.Component": [],
  "Ext.app.domain.Controller": [],
  "Ext.app.domain.Direct": [],
  "Ext.app.domain.Global": [],
  "Ext.app.domain.Store": [],
  "Ext.button.Button": [
    "widget.button"
  ],
  "Ext.button.Cycle": [
    "widget.cycle"
  ],
  "Ext.button.Manager": [],
  "Ext.button.Split": [
    "widget.splitbutton"
  ],
  "Ext.chart.Callout": [],
  "Ext.chart.Chart": [
    "widget.chart"
  ],
  "Ext.chart.Highlight": [],
  "Ext.chart.Label": [],
  "Ext.chart.Legend": [],
  "Ext.chart.LegendItem": [],
  "Ext.chart.Mask": [],
  "Ext.chart.MaskLayer": [],
  "Ext.chart.Navigation": [],
  "Ext.chart.Shape": [],
  "Ext.chart.Tip": [],
  "Ext.chart.TipSurface": [],
  "Ext.chart.axis.Abstract": [],
  "Ext.chart.axis.Axis": [],
  "Ext.chart.axis.Category": [
    "axis.category"
  ],
  "Ext.chart.axis.Gauge": [
    "axis.gauge"
  ],
  "Ext.chart.axis.Numeric": [
    "axis.numeric"
  ],
  "Ext.chart.axis.Radial": [
    "axis.radial"
  ],
  "Ext.chart.axis.Time": [
    "axis.time"
  ],
  "Ext.chart.series.Area": [
    "series.area"
  ],
  "Ext.chart.series.Bar": [
    "series.bar"
  ],
  "Ext.chart.series.Cartesian": [],
  "Ext.chart.series.Column": [
    "series.column"
  ],
  "Ext.chart.series.Gauge": [
    "series.gauge"
  ],
  "Ext.chart.series.Line": [
    "series.line"
  ],
  "Ext.chart.series.Pie": [
    "series.pie"
  ],
  "Ext.chart.series.Radar": [
    "series.radar"
  ],
  "Ext.chart.series.Scatter": [
    "series.scatter"
  ],
  "Ext.chart.series.Series": [],
  "Ext.chart.theme.Base": [],
  "Ext.chart.theme.Residuals": [],
  "Ext.chart.theme.Theme": [],
  "Ext.container.AbstractContainer": [],
  "Ext.container.ButtonGroup": [
    "widget.buttongroup"
  ],
  "Ext.container.Container": [
    "widget.container"
  ],
  "Ext.container.DockingContainer": [],
  "Ext.container.Monitor": [],
  "Ext.container.Viewport": [
    "widget.viewport"
  ],
  "Ext.data.AbstractStore": [],
  "Ext.data.ArrayStore": [
    "store.array"
  ],
  "Ext.data.Batch": [],
  "Ext.data.BufferStore": [
    "store.buffer"
  ],
  "Ext.data.Connection": [],
  "Ext.data.DirectStore": [
    "store.direct"
  ],
  "Ext.data.Errors": [],
  "Ext.data.Field": [
    "data.field"
  ],
  "Ext.data.Group": [],
  "Ext.data.IdGenerator": [],
  "Ext.data.JsonP": [],
  "Ext.data.JsonPStore": [
    "store.jsonp"
  ],
  "Ext.data.JsonStore": [
    "store.json"
  ],
  "Ext.data.Model": [],
  "Ext.data.NodeInterface": [],
  "Ext.data.NodeStore": [
    "store.node"
  ],
  "Ext.data.Operation": [],
  "Ext.data.PageMap": [],
  "Ext.data.Request": [],
  "Ext.data.ResultSet": [],
  "Ext.data.SequentialIdGenerator": [
    "idgen.sequential"
  ],
  "Ext.data.SortTypes": [],
  "Ext.data.Store": [
    "store.store"
  ],
  "Ext.data.StoreManager": [],
  "Ext.data.Tree": [
    "data.tree"
  ],
  "Ext.data.TreeModel": [],
  "Ext.data.TreeStore": [
    "store.tree"
  ],
  "Ext.data.Types": [],
  "Ext.data.UuidGenerator": [
    "idgen.uuid"
  ],
  "Ext.data.XmlStore": [
    "store.xml"
  ],
  "Ext.data.association.Association": [],
  "Ext.data.association.BelongsTo": [
    "association.belongsto"
  ],
  "Ext.data.association.HasMany": [
    "association.hasmany"
  ],
  "Ext.data.association.HasOne": [
    "association.hasone"
  ],
  "Ext.data.flash.BinaryXhr": [],
  "Ext.data.proxy.Ajax": [
    "proxy.ajax"
  ],
  "Ext.data.proxy.Client": [],
  "Ext.data.proxy.Direct": [
    "proxy.direct"
  ],
  "Ext.data.proxy.JsonP": [
    "proxy.jsonp",
    "proxy.scripttag"
  ],
  "Ext.data.proxy.LocalStorage": [
    "proxy.localstorage"
  ],
  "Ext.data.proxy.Memory": [
    "proxy.memory"
  ],
  "Ext.data.proxy.Proxy": [
    "proxy.proxy"
  ],
  "Ext.data.proxy.Rest": [
    "proxy.rest"
  ],
  "Ext.data.proxy.Server": [
    "proxy.server"
  ],
  "Ext.data.proxy.SessionStorage": [
    "proxy.sessionstorage"
  ],
  "Ext.data.proxy.WebStorage": [],
  "Ext.data.reader.Array": [
    "reader.array"
  ],
  "Ext.data.reader.Json": [
    "reader.json"
  ],
  "Ext.data.reader.Reader": [],
  "Ext.data.reader.Xml": [
    "reader.xml"
  ],
  "Ext.data.validations": [],
  "Ext.data.writer.Json": [
    "writer.json"
  ],
  "Ext.data.writer.Writer": [
    "writer.base"
  ],
  "Ext.data.writer.Xml": [
    "writer.xml"
  ],
  "Ext.dd.DD": [],
  "Ext.dd.DDProxy": [],
  "Ext.dd.DDTarget": [],
  "Ext.dd.DragDrop": [],
  "Ext.dd.DragDropManager": [],
  "Ext.dd.DragSource": [],
  "Ext.dd.DragTracker": [],
  "Ext.dd.DragZone": [],
  "Ext.dd.DropTarget": [],
  "Ext.dd.DropZone": [],
  "Ext.dd.Registry": [],
  "Ext.dd.ScrollManager": [],
  "Ext.dd.StatusProxy": [],
  "Ext.diag.layout.Context": [],
  "Ext.diag.layout.ContextItem": [],
  "Ext.direct.Event": [
    "direct.event"
  ],
  "Ext.direct.ExceptionEvent": [
    "direct.exception"
  ],
  "Ext.direct.JsonProvider": [
    "direct.jsonprovider"
  ],
  "Ext.direct.Manager": [],
  "Ext.direct.PollingProvider": [
    "direct.pollingprovider"
  ],
  "Ext.direct.Provider": [
    "direct.provider"
  ],
  "Ext.direct.RemotingEvent": [
    "direct.rpc"
  ],
  "Ext.direct.RemotingMethod": [],
  "Ext.direct.RemotingProvider": [
    "direct.remotingprovider"
  ],
  "Ext.direct.Transaction": [
    "direct.transaction"
  ],
  "Ext.dom.Layer": [],
  "Ext.draw.Color": [],
  "Ext.draw.Component": [
    "widget.draw"
  ],
  "Ext.draw.CompositeSprite": [],
  "Ext.draw.Draw": [],
  "Ext.draw.Matrix": [],
  "Ext.draw.Sprite": [],
  "Ext.draw.SpriteDD": [],
  "Ext.draw.Surface": [],
  "Ext.draw.Text": [
    "widget.text"
  ],
  "Ext.draw.engine.ImageExporter": [],
  "Ext.draw.engine.Svg": [],
  "Ext.draw.engine.SvgExporter": [],
  "Ext.draw.engine.Vml": [],
  "Ext.flash.Component": [
    "widget.flash"
  ],
  "Ext.form.Basic": [],
  "Ext.form.CheckboxGroup": [
    "widget.checkboxgroup"
  ],
  "Ext.form.CheckboxManager": [],
  "Ext.form.FieldAncestor": [],
  "Ext.form.FieldContainer": [
    "widget.fieldcontainer"
  ],
  "Ext.form.FieldSet": [
    "widget.fieldset"
  ],
  "Ext.form.Label": [
    "widget.label"
  ],
  "Ext.form.Labelable": [],
  "Ext.form.Panel": [
    "widget.form"
  ],
  "Ext.form.RadioGroup": [
    "widget.radiogroup"
  ],
  "Ext.form.RadioManager": [],
  "Ext.form.action.Action": [],
  "Ext.form.action.DirectLoad": [
    "formaction.directload"
  ],
  "Ext.form.action.DirectSubmit": [
    "formaction.directsubmit"
  ],
  "Ext.form.action.Load": [
    "formaction.load"
  ],
  "Ext.form.action.StandardSubmit": [
    "formaction.standardsubmit"
  ],
  "Ext.form.action.Submit": [
    "formaction.submit"
  ],
  "Ext.form.field.Base": [
    "widget.field"
  ],
  "Ext.form.field.Checkbox": [
    "widget.checkbox",
    "widget.checkboxfield"
  ],
  "Ext.form.field.ComboBox": [
    "widget.combo",
    "widget.combobox"
  ],
  "Ext.form.field.Date": [
    "widget.datefield"
  ],
  "Ext.form.field.Display": [
    "widget.displayfield"
  ],
  "Ext.form.field.Field": [],
  "Ext.form.field.File": [
    "widget.filefield",
    "widget.fileuploadfield"
  ],
  "Ext.form.field.FileButton": [
    "widget.filebutton"
  ],
  "Ext.form.field.Hidden": [
    "widget.hidden",
    "widget.hiddenfield"
  ],
  "Ext.form.field.HtmlEditor": [
    "widget.htmleditor"
  ],
  "Ext.form.field.Number": [
    "widget.numberfield"
  ],
  "Ext.form.field.Picker": [
    "widget.pickerfield"
  ],
  "Ext.form.field.Radio": [
    "widget.radio",
    "widget.radiofield"
  ],
  "Ext.form.field.Spinner": [
    "widget.spinnerfield"
  ],
  "Ext.form.field.Text": [
    "widget.textfield"
  ],
  "Ext.form.field.TextArea": [
    "widget.textarea",
    "widget.textareafield"
  ],
  "Ext.form.field.Time": [
    "widget.timefield"
  ],
  "Ext.form.field.Trigger": [
    "widget.trigger",
    "widget.triggerfield"
  ],
  "Ext.form.field.VTypes": [],
  "Ext.fx.Anim": [],
  "Ext.fx.Animator": [],
  "Ext.fx.CubicBezier": [],
  "Ext.fx.Easing": [],
  "Ext.fx.Manager": [],
  "Ext.fx.PropertyHandler": [],
  "Ext.fx.Queue": [],
  "Ext.fx.target.Component": [],
  "Ext.fx.target.CompositeElement": [],
  "Ext.fx.target.CompositeElementCSS": [],
  "Ext.fx.target.CompositeSprite": [],
  "Ext.fx.target.Element": [],
  "Ext.fx.target.ElementCSS": [],
  "Ext.fx.target.Sprite": [],
  "Ext.fx.target.Target": [],
  "Ext.grid.CellContext": [],
  "Ext.grid.CellEditor": [],
  "Ext.grid.ColumnComponentLayout": [
    "layout.columncomponent"
  ],
  "Ext.grid.ColumnLayout": [
    "layout.gridcolumn"
  ],
  "Ext.grid.ColumnManager": [],
  "Ext.grid.Panel": [
    "widget.grid",
    "widget.gridpanel"
  ],
  "Ext.grid.RowEditor": [
    "widget.roweditor"
  ],
  "Ext.grid.RowEditorButtons": [
    "widget.roweditorbuttons"
  ],
  "Ext.grid.Scroller": [],
  "Ext.grid.View": [
    "widget.gridview"
  ],
  "Ext.grid.ViewDropZone": [],
  "Ext.grid.column.Action": [
    "widget.actioncolumn"
  ],
  "Ext.grid.column.Boolean": [
    "widget.booleancolumn"
  ],
  "Ext.grid.column.Check": [
    "widget.checkcolumn"
  ],
  "Ext.grid.column.Column": [
    "widget.gridcolumn"
  ],
  "Ext.grid.column.Date": [
    "widget.datecolumn"
  ],
  "Ext.grid.column.Number": [
    "widget.numbercolumn"
  ],
  "Ext.grid.column.RowNumberer": [
    "widget.rownumberer"
  ],
  "Ext.grid.column.Template": [
    "widget.templatecolumn"
  ],
  "Ext.grid.feature.AbstractSummary": [
    "feature.abstractsummary"
  ],
  "Ext.grid.feature.Feature": [
    "feature.feature"
  ],
  "Ext.grid.feature.GroupStore": [],
  "Ext.grid.feature.Grouping": [
    "feature.grouping"
  ],
  "Ext.grid.feature.GroupingSummary": [
    "feature.groupingsummary"
  ],
  "Ext.grid.feature.RowBody": [
    "feature.rowbody"
  ],
  "Ext.grid.feature.RowWrap": [
    "feature.rowwrap"
  ],
  "Ext.grid.feature.Summary": [
    "feature.summary"
  ],
  "Ext.grid.header.Container": [
    "widget.headercontainer"
  ],
  "Ext.grid.header.DragZone": [],
  "Ext.grid.header.DropZone": [],
  "Ext.grid.locking.HeaderContainer": [],
  "Ext.grid.locking.Lockable": [],
  "Ext.grid.locking.View": [],
  "Ext.grid.plugin.BufferedRenderer": [
    "plugin.bufferedrenderer"
  ],
  "Ext.grid.plugin.BufferedRendererTableView": [],
  "Ext.grid.plugin.BufferedRendererTreeView": [],
  "Ext.grid.plugin.CellEditing": [
    "plugin.cellediting"
  ],
  "Ext.grid.plugin.DivRenderer": [
    "plugin.divrenderer"
  ],
  "Ext.grid.plugin.DragDrop": [
    "plugin.gridviewdragdrop"
  ],
  "Ext.grid.plugin.Editing": [
    "editing.editing"
  ],
  "Ext.grid.plugin.HeaderReorderer": [
    "plugin.gridheaderreorderer"
  ],
  "Ext.grid.plugin.HeaderResizer": [
    "plugin.gridheaderresizer"
  ],
  "Ext.grid.plugin.RowEditing": [
    "plugin.rowediting"
  ],
  "Ext.grid.plugin.RowExpander": [
    "plugin.rowexpander"
  ],
  "Ext.grid.property.Grid": [
    "widget.propertygrid"
  ],
  "Ext.grid.property.HeaderContainer": [],
  "Ext.grid.property.Property": [],
  "Ext.grid.property.Store": [],
  "Ext.layout.ClassList": [],
  "Ext.layout.Context": [],
  "Ext.layout.ContextItem": [],
  "Ext.layout.Layout": [],
  "Ext.layout.SizeModel": [],
  "Ext.layout.component.Auto": [
    "layout.autocomponent"
  ],
  "Ext.layout.component.Body": [
    "layout.body"
  ],
  "Ext.layout.component.BoundList": [
    "layout.boundlist"
  ],
  "Ext.layout.component.Button": [
    "layout.button"
  ],
  "Ext.layout.component.Component": [],
  "Ext.layout.component.Dock": [
    "layout.dock"
  ],
  "Ext.layout.component.Draw": [
    "layout.draw"
  ],
  "Ext.layout.component.FieldSet": [
    "layout.fieldset"
  ],
  "Ext.layout.component.ProgressBar": [
    "layout.progressbar"
  ],
  "Ext.layout.component.field.ComboBox": [
    "layout.combobox"
  ],
  "Ext.layout.component.field.Field": [
    "layout.field"
  ],
  "Ext.layout.component.field.FieldContainer": [
    "layout.fieldcontainer"
  ],
  "Ext.layout.component.field.HtmlEditor": [
    "layout.htmleditor"
  ],
  "Ext.layout.component.field.Slider": [
    "layout.sliderfield"
  ],
  "Ext.layout.component.field.Text": [
    "layout.textfield"
  ],
  "Ext.layout.component.field.TextArea": [
    "layout.textareafield"
  ],
  "Ext.layout.component.field.Trigger": [
    "layout.triggerfield"
  ],
  "Ext.layout.container.Absolute": [
    "layout.absolute"
  ],
  "Ext.layout.container.Accordion": [
    "layout.accordion"
  ],
  "Ext.layout.container.Anchor": [
    "layout.anchor"
  ],
  "Ext.layout.container.Auto": [
    "layout.auto",
    "layout.autocontainer"
  ],
  "Ext.layout.container.Border": [
    "layout.border"
  ],
  "Ext.layout.container.Box": [
    "layout.box"
  ],
  "Ext.layout.container.Card": [
    "layout.card"
  ],
  "Ext.layout.container.CheckboxGroup": [
    "layout.checkboxgroup"
  ],
  "Ext.layout.container.Column": [
    "layout.column"
  ],
  "Ext.layout.container.Container": [
    "layout.container"
  ],
  "Ext.layout.container.Editor": [
    "layout.editor"
  ],
  "Ext.layout.container.Fit": [
    "layout.fit"
  ],
  "Ext.layout.container.Form": [
    "layout.form"
  ],
  "Ext.layout.container.HBox": [
    "layout.hbox"
  ],
  "Ext.layout.container.Table": [
    "layout.table"
  ],
  "Ext.layout.container.VBox": [
    "layout.vbox"
  ],
  "Ext.layout.container.border.Region": [],
  "Ext.layout.container.boxOverflow.Menu": [],
  "Ext.layout.container.boxOverflow.None": [],
  "Ext.layout.container.boxOverflow.Scroller": [],
  "Ext.menu.CheckItem": [
    "widget.menucheckitem"
  ],
  "Ext.menu.ColorPicker": [
    "widget.colormenu"
  ],
  "Ext.menu.DatePicker": [
    "widget.datemenu"
  ],
  "Ext.menu.Item": [
    "widget.menuitem"
  ],
  "Ext.menu.KeyNav": [],
  "Ext.menu.Manager": [],
  "Ext.menu.Menu": [
    "widget.menu"
  ],
  "Ext.menu.Separator": [
    "widget.menuseparator"
  ],
  "Ext.panel.AbstractPanel": [],
  "Ext.panel.DD": [],
  "Ext.panel.Header": [
    "widget.header"
  ],
  "Ext.panel.Panel": [
    "widget.panel"
  ],
  "Ext.panel.Proxy": [],
  "Ext.panel.Table": [
    "widget.tablepanel"
  ],
  "Ext.panel.Tool": [
    "widget.tool"
  ],
  "Ext.picker.Color": [
    "widget.colorpicker"
  ],
  "Ext.picker.Date": [
    "widget.datepicker"
  ],
  "Ext.picker.Month": [
    "widget.monthpicker"
  ],
  "Ext.picker.Time": [
    "widget.timepicker"
  ],
  "Ext.resizer.BorderSplitter": [
    "widget.bordersplitter"
  ],
  "Ext.resizer.BorderSplitterTracker": [],
  "Ext.resizer.Handle": [],
  "Ext.resizer.ResizeTracker": [],
  "Ext.resizer.Resizer": [],
  "Ext.resizer.Splitter": [
    "widget.splitter"
  ],
  "Ext.resizer.SplitterTracker": [],
  "Ext.rtl.AbstractComponent": [],
  "Ext.rtl.EventObjectImpl": [],
  "Ext.rtl.button.Button": [],
  "Ext.rtl.chart.Chart": [],
  "Ext.rtl.chart.Legend": [],
  "Ext.rtl.chart.LegendItem": [],
  "Ext.rtl.chart.axis.Axis": [],
  "Ext.rtl.chart.axis.Gauge": [],
  "Ext.rtl.chart.series.Cartesian": [],
  "Ext.rtl.chart.series.Gauge": [],
  "Ext.rtl.dd.DD": [],
  "Ext.rtl.dom.Element_anim": [],
  "Ext.rtl.dom.Element_insertion": [],
  "Ext.rtl.dom.Element_position": [],
  "Ext.rtl.dom.Element_scroll": [],
  "Ext.rtl.dom.Element_static": [],
  "Ext.rtl.dom.Layer": [],
  "Ext.rtl.draw.Component": [],
  "Ext.rtl.draw.Sprite": [],
  "Ext.rtl.form.field.Checkbox": [],
  "Ext.rtl.form.field.File": [],
  "Ext.rtl.form.field.Spinner": [],
  "Ext.rtl.form.field.Trigger": [],
  "Ext.rtl.grid.CellEditor": [],
  "Ext.rtl.grid.RowEditor": [],
  "Ext.rtl.grid.column.Column": [],
  "Ext.rtl.grid.feature.Summary": [],
  "Ext.rtl.grid.plugin.HeaderResizer": [],
  "Ext.rtl.grid.plugin.RowEditing": [],
  "Ext.rtl.layout.ContextItem": [],
  "Ext.rtl.layout.component.Dock": [],
  "Ext.rtl.layout.component.field.Text": [],
  "Ext.rtl.layout.component.field.Trigger": [],
  "Ext.rtl.layout.container.Absolute": [],
  "Ext.rtl.layout.container.Border": [],
  "Ext.rtl.layout.container.Box": [],
  "Ext.rtl.layout.container.CheckboxGroup": [],
  "Ext.rtl.layout.container.Column": [],
  "Ext.rtl.layout.container.HBox": [],
  "Ext.rtl.layout.container.VBox": [],
  "Ext.rtl.layout.container.boxOverflow.Menu": [],
  "Ext.rtl.layout.container.boxOverflow.Scroller": [],
  "Ext.rtl.panel.Header": [],
  "Ext.rtl.panel.Panel": [],
  "Ext.rtl.resizer.BorderSplitterTracker": [],
  "Ext.rtl.resizer.ResizeTracker": [],
  "Ext.rtl.resizer.SplitterTracker": [],
  "Ext.rtl.selection.CellModel": [],
  "Ext.rtl.selection.TreeModel": [],
  "Ext.rtl.slider.Multi": [],
  "Ext.rtl.tab.Bar": [],
  "Ext.rtl.tip.QuickTipManager": [],
  "Ext.rtl.tree.Column": [],
  "Ext.rtl.util.Floating": [],
  "Ext.rtl.util.Renderable": [],
  "Ext.rtl.view.Table": [],
  "Ext.selection.CellModel": [
    "selection.cellmodel"
  ],
  "Ext.selection.CheckboxModel": [
    "selection.checkboxmodel"
  ],
  "Ext.selection.DataViewModel": [],
  "Ext.selection.Model": [],
  "Ext.selection.RowModel": [
    "selection.rowmodel"
  ],
  "Ext.selection.TreeModel": [
    "selection.treemodel"
  ],
  "Ext.slider.Multi": [
    "widget.multislider"
  ],
  "Ext.slider.Single": [
    "widget.slider",
    "widget.sliderfield"
  ],
  "Ext.slider.Thumb": [],
  "Ext.slider.Tip": [
    "widget.slidertip"
  ],
  "Ext.state.CookieProvider": [],
  "Ext.state.LocalStorageProvider": [
    "state.localstorage"
  ],
  "Ext.state.Manager": [],
  "Ext.state.Provider": [],
  "Ext.state.Stateful": [],
  "Ext.tab.Bar": [
    "widget.tabbar"
  ],
  "Ext.tab.Panel": [
    "widget.tabpanel"
  ],
  "Ext.tab.Tab": [
    "widget.tab"
  ],
  "Ext.tip.QuickTip": [
    "widget.quicktip"
  ],
  "Ext.tip.QuickTipManager": [],
  "Ext.tip.Tip": [],
  "Ext.tip.ToolTip": [
    "widget.tooltip"
  ],
  "Ext.toolbar.Fill": [
    "widget.tbfill"
  ],
  "Ext.toolbar.Item": [
    "widget.tbitem"
  ],
  "Ext.toolbar.Paging": [
    "widget.pagingtoolbar"
  ],
  "Ext.toolbar.Separator": [
    "widget.tbseparator"
  ],
  "Ext.toolbar.Spacer": [
    "widget.tbspacer"
  ],
  "Ext.toolbar.TextItem": [
    "widget.tbtext"
  ],
  "Ext.toolbar.Toolbar": [
    "widget.toolbar"
  ],
  "Ext.tree.Column": [
    "widget.treecolumn"
  ],
  "Ext.tree.Panel": [
    "widget.treepanel"
  ],
  "Ext.tree.View": [
    "widget.treeview"
  ],
  "Ext.tree.ViewDragZone": [],
  "Ext.tree.ViewDropZone": [],
  "Ext.tree.plugin.TreeViewDragDrop": [
    "plugin.treeviewdragdrop"
  ],
  "Ext.util.AbstractMixedCollection": [],
  "Ext.util.Animate": [],
  "Ext.util.Bindable": [],
  "Ext.util.CSS": [],
  "Ext.util.ClickRepeater": [],
  "Ext.util.ComponentDragger": [],
  "Ext.util.Cookies": [],
  "Ext.util.ElementContainer": [],
  "Ext.util.Filter": [],
  "Ext.util.Floating": [],
  "Ext.util.Grouper": [],
  "Ext.util.HashMap": [],
  "Ext.util.History": [],
  "Ext.util.Inflector": [],
  "Ext.util.KeyMap": [],
  "Ext.util.KeyNav": [],
  "Ext.util.LocalStorage": [],
  "Ext.util.LruCache": [],
  "Ext.util.Memento": [],
  "Ext.util.MixedCollection": [],
  "Ext.util.Offset": [],
  "Ext.util.Point": [],
  "Ext.util.ProtoElement": [],
  "Ext.util.Queue": [],
  "Ext.util.Region": [],
  "Ext.util.Renderable": [],
  "Ext.util.Sortable": [],
  "Ext.util.Sorter": [],
  "Ext.util.TextMetrics": [],
  "Ext.ux.BoxReorderer": [],
  "Ext.ux.CellDragDrop": [
    "plugin.celldragdrop"
  ],
  "Ext.ux.DataTip": [
    "plugin.datatip"
  ],
  "Ext.ux.DataView.Animated": [],
  "Ext.ux.DataView.DragSelector": [],
  "Ext.ux.DataView.Draggable": [],
  "Ext.ux.DataView.LabelEditor": [],
  "Ext.ux.FieldReplicator": [],
  "Ext.ux.GMapPanel": [
    "widget.gmappanel"
  ],
  "Ext.ux.GroupTabPanel": [
    "widget.grouptabpanel"
  ],
  "Ext.ux.GroupTabRenderer": [
    "plugin.grouptabrenderer"
  ],
  "Ext.ux.IFrame": [
    "widget.uxiframe"
  ],
  "Ext.ux.LiveSearchGridPanel": [],
  "Ext.ux.PreviewPlugin": [
    "plugin.preview"
  ],
  "Ext.ux.ProgressBarPager": [],
  "Ext.ux.RowExpander": [],
  "Ext.ux.SlidingPager": [],
  "Ext.ux.Spotlight": [],
  "Ext.ux.TabCloseMenu": [
    "plugin.tabclosemenu"
  ],
  "Ext.ux.TabReorderer": [],
  "Ext.ux.TabScrollerMenu": [
    "plugin.tabscrollermenu"
  ],
  "Ext.ux.ToolbarDroppable": [],
  "Ext.ux.TreePicker": [
    "widget.treepicker"
  ],
  "Ext.ux.ajax.DataSimlet": [],
  "Ext.ux.ajax.JsonSimlet": [
    "simlet.json"
  ],
  "Ext.ux.ajax.SimManager": [],
  "Ext.ux.ajax.SimXhr": [],
  "Ext.ux.ajax.Simlet": [
    "simlet.basic"
  ],
  "Ext.ux.ajax.XmlSimlet": [
    "simlet.xml"
  ],
  "Ext.ux.data.PagingMemoryProxy": [
    "proxy.pagingmemory"
  ],
  "Ext.ux.dd.CellFieldDropZone": [],
  "Ext.ux.dd.PanelFieldDragZone": [],
  "Ext.ux.event.Driver": [],
  "Ext.ux.event.Maker": [],
  "Ext.ux.event.Player": [],
  "Ext.ux.event.Recorder": [],
  "Ext.ux.event.RecorderManager": [
    "widget.eventrecordermanager"
  ],
  "Ext.ux.form.ItemSelector": [
    "widget.itemselector",
    "widget.itemselectorfield"
  ],
  "Ext.ux.form.MultiSelect": [
    "widget.multiselect",
    "widget.multiselectfield"
  ],
  "Ext.ux.form.SearchField": [
    "widget.searchfield"
  ],
  "Ext.ux.grid.FiltersFeature": [
    "feature.filters"
  ],
  "Ext.ux.grid.TransformGrid": [],
  "Ext.ux.grid.filter.BooleanFilter": [
    "gridfilter.boolean"
  ],
  "Ext.ux.grid.filter.DateFilter": [
    "gridfilter.date"
  ],
  "Ext.ux.grid.filter.DateTimeFilter": [
    "gridfilter.datetime"
  ],
  "Ext.ux.grid.filter.Filter": [],
  "Ext.ux.grid.filter.ListFilter": [
    "gridfilter.list"
  ],
  "Ext.ux.grid.filter.NumericFilter": [
    "gridfilter.numeric"
  ],
  "Ext.ux.grid.filter.StringFilter": [
    "gridfilter.string"
  ],
  "Ext.ux.grid.menu.ListMenu": [],
  "Ext.ux.grid.menu.RangeMenu": [],
  "Ext.ux.layout.Center": [
    "layout.ux.center"
  ],
  "Ext.ux.statusbar.StatusBar": [
    "widget.statusbar"
  ],
  "Ext.ux.statusbar.ValidationStatus": [],
  "Ext.view.AbstractView": [],
  "Ext.view.BoundList": [
    "widget.boundlist"
  ],
  "Ext.view.BoundListKeyNav": [],
  "Ext.view.DragZone": [],
  "Ext.view.DropZone": [],
  "Ext.view.NodeCache": [],
  "Ext.view.Table": [
    "widget.tableview"
  ],
  "Ext.view.TableLayout": [
    "layout.tableview"
  ],
  "Ext.view.View": [
    "widget.dataview"
  ],
  "Ext.window.MessageBox": [
    "widget.messagebox"
  ],
  "Ext.window.Window": [
    "widget.window"
  ],
  "mc2ui.controller.PermissionController": [],
  "mc2ui.model.Absence": [],
  "mc2ui.model.AbsenceKind": [],
  "mc2ui.model.AbsenceStack": [],
  "mc2ui.model.AlboArea": [],
  "mc2ui.model.AlboCategory": [],
  "mc2ui.model.AlboEntity": [],
  "mc2ui.model.AlboHistory": [],
  "mc2ui.model.AlboPublication": [],
  "mc2ui.model.ArchiveClass": [],
  "mc2ui.model.ArchiveClassStep": [],
  "mc2ui.model.ArchiveDocument": [],
  "mc2ui.model.ArchiveDocumentFile": [],
  "mc2ui.model.ArchiveDossier": [
    "model.archivedossier"
  ],
  "mc2ui.model.ArchiveMail": [],
  "mc2ui.model.ArchiveMailAccount": [],
  "mc2ui.model.ArchiveMailAttachment": [],
  "mc2ui.model.ArchiveMetadata": [],
  "mc2ui.model.ArchiveOffice": [],
  "mc2ui.model.ArchiveOrigin": [],
  "mc2ui.model.ArchiveTemplate": [
    "model.archivetemplate"
  ],
  "mc2ui.model.Assignee": [],
  "mc2ui.model.BudgetActivities": [],
  "mc2ui.model.CcpAdditional": [],
  "mc2ui.model.CcpAdditionalLinked": [],
  "mc2ui.model.CcpBollettino": [],
  "mc2ui.model.CcpCategory": [],
  "mc2ui.model.CcpDeposit": [],
  "mc2ui.model.CcpDepositSlip": [],
  "mc2ui.model.CcpInvoice": [],
  "mc2ui.model.CcpInvoiceDepositSlip": [],
  "mc2ui.model.CcpInvoiceTransmission": [],
  "mc2ui.model.CcpMovement": [],
  "mc2ui.model.CcpParent": [],
  "mc2ui.model.CcpPayer": [],
  "mc2ui.model.CcpPayment": [],
  "mc2ui.model.CcpPaymentMethod": [],
  "mc2ui.model.CcpReceipt": [],
  "mc2ui.model.CcpReminder": [],
  "mc2ui.model.CcpReport": [],
  "mc2ui.model.CcpResidual": [],
  "mc2ui.model.CcpSchoolYear": [],
  "mc2ui.model.CcpServizi": [],
  "mc2ui.model.CcpStudent": [],
  "mc2ui.model.CcpStudentMarket": [],
  "mc2ui.model.CcpSubject": [],
  "mc2ui.model.CcpType": [],
  "mc2ui.model.CcpTypeStep": [],
  "mc2ui.model.CcpVatCode": [],
  "mc2ui.model.Classe": [],
  "mc2ui.model.Contact": [],
  "mc2ui.model.ContactGroup": [],
  "mc2ui.model.CoreBankAccount": [],
  "mc2ui.model.CoreCity": [],
  "mc2ui.model.CoreCountry": [],
  "mc2ui.model.CoreParameter": [],
  "mc2ui.model.CorePrintSpool": [],
  "mc2ui.model.Corrispettivo": [],
  "mc2ui.model.Credit": [],
  "mc2ui.model.CreditType": [],
  "mc2ui.model.DataLookupResult": [],
  "mc2ui.model.DayTimeTable": [],
  "mc2ui.model.Decreto": [],
  "mc2ui.model.Discount": [],
  "mc2ui.model.DocumentFlow": [],
  "mc2ui.model.DocumentStep": [],
  "mc2ui.model.Dossier": [],
  "mc2ui.model.Employee": [],
  "mc2ui.model.EmployeeParameters": [],
  "mc2ui.model.EmployeeTree": [],
  "mc2ui.model.EventError": [],
  "mc2ui.model.ExtraordinaryAbsStack": [],
  "mc2ui.model.ExtraordinaryStored": [],
  "mc2ui.model.GenericSearch": [],
  "mc2ui.model.HomeInfo": [],
  "mc2ui.model.HomeReleaseNote": [],
  "mc2ui.model.Indirizzo": [],
  "mc2ui.model.Institute": [],
  "mc2ui.model.InstituteType": [],
  "mc2ui.model.Invoice": [],
  "mc2ui.model.MC2Parameter": [],
  "mc2ui.model.MC2Table": [],
  "mc2ui.model.MagisterEsercizio": [],
  "mc2ui.model.MyModel": [
    "model.mymodel"
  ],
  "mc2ui.model.Permission": [],
  "mc2ui.model.PersonnelHourType": [],
  "mc2ui.model.PersonnelHourTypeLinked": [],
  "mc2ui.model.PersonnelProject": [],
  "mc2ui.model.PersonnelProjectLinked": [],
  "mc2ui.model.Presence": [],
  "mc2ui.model.ProtocolAction": [],
  "mc2ui.model.ProtocolCorrespondent": [],
  "mc2ui.model.ProtocolCorrespondentOrigin": [],
  "mc2ui.model.ProtocolHistory": [],
  "mc2ui.model.ProtocolProtocol": [],
  "mc2ui.model.ProtocolSendMethod": [],
  "mc2ui.model.ProtocolSubjectKind": [],
  "mc2ui.model.ProtocolType": [],
  "mc2ui.model.Raccolte": [
    "model.raccolte"
  ],
  "mc2ui.model.Register": [],
  "mc2ui.model.RemoteClass": [],
  "mc2ui.model.SettingsGroup": [],
  "mc2ui.model.SettingsUser": [],
  "mc2ui.model.SocialPosition": [],
  "mc2ui.model.StackPersonnelLink": [],
  "mc2ui.model.StudentBalance": [],
  "mc2ui.model.Supplier": [],
  "mc2ui.model.TimeTable": [],
  "mc2ui.model.TrasparenzaVoice": [],
  "mc2ui.model.UserLog": [],
  "mc2ui.model.WeekBorder": [],
  "mc2ui.store.AbsenceKindGrouped": [],
  "mc2ui.store.AbsenceKinds": [],
  "mc2ui.store.AbsenceKindsLinked": [],
  "mc2ui.store.AbsenceStackUnits": [],
  "mc2ui.store.AbsenceStacks": [],
  "mc2ui.store.AbsenceStacksAndExtraordinary": [],
  "mc2ui.store.Absences": [],
  "mc2ui.store.AddressBook": [],
  "mc2ui.store.AlboAreas": [],
  "mc2ui.store.AlboAreasFilter": [],
  "mc2ui.store.AlboCategories": [],
  "mc2ui.store.AlboCategoriesFilter": [],
  "mc2ui.store.AlboCategoryDurations": [],
  "mc2ui.store.AlboDocumentsForm": [],
  "mc2ui.store.AlboEntities": [],
  "mc2ui.store.AlboEntitiesFilter": [],
  "mc2ui.store.AlboExpirationsFilter": [],
  "mc2ui.store.AlboHistories": [],
  "mc2ui.store.AlboLinkedDocuments": [],
  "mc2ui.store.AlboLinkedDocumentsForm": [],
  "mc2ui.store.AlboPublications": [],
  "mc2ui.store.AlboPublishedFilter": [],
  "mc2ui.store.AlboStatusesFilter": [],
  "mc2ui.store.ArchiveActionAlboFilter": [],
  "mc2ui.store.ArchiveActionProtocolFilter": [],
  "mc2ui.store.ArchiveActionTrasparenzaFilter": [],
  "mc2ui.store.ArchiveCheckedFilter": [],
  "mc2ui.store.ArchiveClassMetadata": [],
  "mc2ui.store.ArchiveClassSteps": [],
  "mc2ui.store.ArchiveClasses": [],
  "mc2ui.store.ArchiveClassesFilter": [],
  "mc2ui.store.ArchiveCompletedFilter": [],
  "mc2ui.store.ArchiveDashboard": [],
  "mc2ui.store.ArchiveDocumentDossier": [],
  "mc2ui.store.ArchiveDocumentDossierLinked": [],
  "mc2ui.store.ArchiveDocumentDossierMc": [],
  "mc2ui.store.ArchiveDocumentFiles": [],
  "mc2ui.store.ArchiveDocumentMails": [],
  "mc2ui.store.ArchiveDocumentsArchived": [],
  "mc2ui.store.ArchiveDocumentsOffice": [],
  "mc2ui.store.ArchiveDocumentsUser": [],
  "mc2ui.store.ArchiveDossiers": [
    "store.archivedossiers"
  ],
  "mc2ui.store.ArchiveMailAccounts": [],
  "mc2ui.store.ArchiveMailAttachments": [],
  "mc2ui.store.ArchiveMailProtocols": [],
  "mc2ui.store.ArchiveMailSecurity": [],
  "mc2ui.store.ArchiveMailSendContact": [],
  "mc2ui.store.ArchiveMails": [],
  "mc2ui.store.ArchiveMailsOffice": [],
  "mc2ui.store.ArchiveMailsUser": [],
  "mc2ui.store.ArchiveMetadata": [],
  "mc2ui.store.ArchiveMetadataFiles": [],
  "mc2ui.store.ArchiveOfficeUsers": [],
  "mc2ui.store.ArchiveOffices": [],
  "mc2ui.store.ArchiveOrigins": [],
  "mc2ui.store.ArchiveOriginsFilter": [],
  "mc2ui.store.ArchiveServices": [],
  "mc2ui.store.ArchiveSignTypes": [],
  "mc2ui.store.ArchiveStores": [],
  "mc2ui.store.ArchiveTemplates": [
    "store.archivetemplates"
  ],
  "mc2ui.store.Assignees": [],
  "mc2ui.store.BolTaxesType": [],
  "mc2ui.store.BudgetActivities": [],
  "mc2ui.store.CalendarHoliday": [],
  "mc2ui.store.CalendarTimeTables": [],
  "mc2ui.store.CcpAddMovementStudents": [],
  "mc2ui.store.CcpAdditionals": [],
  "mc2ui.store.CcpAdditionalsForm": [],
  "mc2ui.store.CcpAeCategories": [],
  "mc2ui.store.CcpBollettini": [],
  "mc2ui.store.CcpCategories": [],
  "mc2ui.store.CcpCategoriesFilter": [],
  "mc2ui.store.CcpCategoriesFilter1": [],
  "mc2ui.store.CcpConsolidations": [],
  "mc2ui.store.CcpDateTypeFilter": [],
  "mc2ui.store.CcpDepositSlips": [],
  "mc2ui.store.CcpDeposits": [],
  "mc2ui.store.CcpDirectionsFilter": [],
  "mc2ui.store.CcpFamilies": [],
  "mc2ui.store.CcpInvoiceAccountHolders": [],
  "mc2ui.store.CcpInvoiceDepositSlips": [],
  "mc2ui.store.CcpInvoiceMovements": [],
  "mc2ui.store.CcpInvoiceTransmissions": [],
  "mc2ui.store.CcpInvoices": [],
  "mc2ui.store.CcpInvoicesToDepositSlip": [],
  "mc2ui.store.CcpLinkedAdditionals": [],
  "mc2ui.store.CcpLinkedAdditionalsForm": [],
  "mc2ui.store.CcpLinkedPayments": [],
  "mc2ui.store.CcpMcPrintTemplate": [],
  "mc2ui.store.CcpMovements": [],
  "mc2ui.store.CcpMovementsCover": [],
  "mc2ui.store.CcpParents": [],
  "mc2ui.store.CcpPastiConsumati": [],
  "mc2ui.store.CcpPastiRim": [],
  "mc2ui.store.CcpPayerTypes": [],
  "mc2ui.store.CcpPayerTypesFilter": [],
  "mc2ui.store.CcpPayers": [],
  "mc2ui.store.CcpPaymentDestinations": [],
  "mc2ui.store.CcpPaymentDestinationsFilter": [],
  "mc2ui.store.CcpPaymentMethods": [],
  "mc2ui.store.CcpPaymentMethodsFilter": [],
  "mc2ui.store.CcpPayments": [],
  "mc2ui.store.CcpPaymentsUnreceipted": [],
  "mc2ui.store.CcpPrintCategories": [],
  "mc2ui.store.CcpPrintCategoryMovementTypes": [],
  "mc2ui.store.CcpPrintGroupings": [],
  "mc2ui.store.CcpPrintOfCategories": [],
  "mc2ui.store.CcpPrintStudentCols": [],
  "mc2ui.store.CcpPrintTypes": [],
  "mc2ui.store.CcpPrintWhat": [],
  "mc2ui.store.CcpReceiptAddressCode": [],
  "mc2ui.store.CcpReceipts": [],
  "mc2ui.store.CcpReminderDetails": [],
  "mc2ui.store.CcpReminders": [],
  "mc2ui.store.CcpReportStudentsParents": [],
  "mc2ui.store.CcpReports": [],
  "mc2ui.store.CcpResiduals": [],
  "mc2ui.store.CcpResidualsGroupings": [],
  "mc2ui.store.CcpResidualsTypes": [],
  "mc2ui.store.CcpResidualsYears": [],
  "mc2ui.store.CcpSchoolYearsFilter": [],
  "mc2ui.store.CcpServiceGrouping": [],
  "mc2ui.store.CcpServizi": [],
  "mc2ui.store.CcpStudentMarkets": [],
  "mc2ui.store.CcpStudentMovements": [],
  "mc2ui.store.CcpStudentMovementsMensa": [],
  "mc2ui.store.CcpStudentReceipts": [],
  "mc2ui.store.CcpStudents": [],
  "mc2ui.store.CcpStudentsDiscount": [],
  "mc2ui.store.CcpStudentsPayments": [],
  "mc2ui.store.CcpStudentsTree": [],
  "mc2ui.store.CcpSubjectTypes": [],
  "mc2ui.store.CcpSubjectTypesFilter": [],
  "mc2ui.store.CcpSubjects": [],
  "mc2ui.store.CcpTypeSchoolYears": [],
  "mc2ui.store.CcpTypeSteps": [],
  "mc2ui.store.CcpTypes": [],
  "mc2ui.store.CcpTypesFilter": [],
  "mc2ui.store.CcpTypesFilter1": [],
  "mc2ui.store.CcpVatCodes": [],
  "mc2ui.store.Classi": [],
  "mc2ui.store.Classi1": [],
  "mc2ui.store.ContactGroupLinked": [],
  "mc2ui.store.ContactGroups": [],
  "mc2ui.store.Contacts": [],
  "mc2ui.store.CoreBankAccountTypes": [],
  "mc2ui.store.CoreBankAccounts": [],
  "mc2ui.store.CoreCities": [],
  "mc2ui.store.CoreCountries": [],
  "mc2ui.store.CoreParameter": [],
  "mc2ui.store.CorePrintSpool": [],
  "mc2ui.store.Corrispettivi": [],
  "mc2ui.store.Credits": [],
  "mc2ui.store.CreditsType": [],
  "mc2ui.store.CreditsTypePaymentFilter": [],
  "mc2ui.store.DataLookupResults": [],
  "mc2ui.store.Decreti": [],
  "mc2ui.store.Discounts": [],
  "mc2ui.store.DocumentFlows": [],
  "mc2ui.store.EmpUnitRecoverHours": [],
  "mc2ui.store.EmployeeParameters": [
    "store.EmployeeParameters"
  ],
  "mc2ui.store.Employees": [],
  "mc2ui.store.EmployeesAll": [],
  "mc2ui.store.EmployeesTree": [
    "store.EmployeesTree"
  ],
  "mc2ui.store.EmployeesTreeActive": [
    "store.EmployeesTreeActive"
  ],
  "mc2ui.store.EventErrors": [],
  "mc2ui.store.ExtraordinaryAbsStack": [],
  "mc2ui.store.ExtraordinaryStored": [],
  "mc2ui.store.Gender": [],
  "mc2ui.store.GenericSearches": [],
  "mc2ui.store.HomeInfos": [],
  "mc2ui.store.HomeReleaseNotes": [],
  "mc2ui.store.InOut": [],
  "mc2ui.store.InOutType": [],
  "mc2ui.store.Indirizzi": [],
  "mc2ui.store.Indirizzi1": [],
  "mc2ui.store.Institutes": [],
  "mc2ui.store.InvoiceExpirations": [],
  "mc2ui.store.Invoices": [],
  "mc2ui.store.ListaFratelli": [],
  "mc2ui.store.MC2Parameters": [],
  "mc2ui.store.MC2Tables": [],
  "mc2ui.store.MagisterEsercizi": [],
  "mc2ui.store.MastertrainingContacts": [],
  "mc2ui.store.McDbs": [],
  "mc2ui.store.Months": [],
  "mc2ui.store.MyModels": [
    "store.mymodels"
  ],
  "mc2ui.store.OLDCcpDepositSlipRows": [],
  "mc2ui.store.Permissions": [
    "store.Permissions"
  ],
  "mc2ui.store.PersonnelHourTypes": [],
  "mc2ui.store.PersonnelPersonnelProjects": [],
  "mc2ui.store.PersonnelProjectHourTypes": [],
  "mc2ui.store.PersonnelProjects": [],
  "mc2ui.store.Presences": [],
  "mc2ui.store.PrintFormats": [],
  "mc2ui.store.ProtocolActions": [],
  "mc2ui.store.ProtocolBarcodes": [],
  "mc2ui.store.ProtocolCorrespondents": [],
  "mc2ui.store.ProtocolCorrespondentsFilter": [],
  "mc2ui.store.ProtocolCorrespondentsForm": [],
  "mc2ui.store.ProtocolCorrespondentsOrigins": [],
  "mc2ui.store.ProtocolCorrespondentsOriginsForm": [],
  "mc2ui.store.ProtocolDirectionsFilter": [],
  "mc2ui.store.ProtocolDocumentsForm": [],
  "mc2ui.store.ProtocolHistories": [],
  "mc2ui.store.ProtocolLinkedCorrespondents": [],
  "mc2ui.store.ProtocolLinkedCorrespondentsForm": [],
  "mc2ui.store.ProtocolLinkedDocuments": [],
  "mc2ui.store.ProtocolLinkedDocumentsForm": [],
  "mc2ui.store.ProtocolLinkedProtocols": [],
  "mc2ui.store.ProtocolLinkedProtocolsForm": [],
  "mc2ui.store.ProtocolProtocols": [],
  "mc2ui.store.ProtocolProtocolsForm": [],
  "mc2ui.store.ProtocolSendMethods": [],
  "mc2ui.store.ProtocolSendMethodsFilter": [],
  "mc2ui.store.ProtocolSignPositions": [],
  "mc2ui.store.ProtocolSubjectKinds": [],
  "mc2ui.store.ProtocolSubjectKindsFilter": [],
  "mc2ui.store.ProtocolTypes": [],
  "mc2ui.store.ProtocolTypesFilter": [],
  "mc2ui.store.ProtocolTypesLeaf": [],
  "mc2ui.store.ProtocolTypesTree": [],
  "mc2ui.store.Raccoltes": [
    "store.raccoltes"
  ],
  "mc2ui.store.Register": [],
  "mc2ui.store.ReminderTypes": [],
  "mc2ui.store.RemoteClass": [],
  "mc2ui.store.SchoolTypes": [],
  "mc2ui.store.SettingsGroups": [],
  "mc2ui.store.SettingsUsers": [],
  "mc2ui.store.SocialPosition": [],
  "mc2ui.store.StackResetTypes": [],
  "mc2ui.store.StacksPersonnelLinks": [],
  "mc2ui.store.StudentBalances": [],
  "mc2ui.store.StudentStates": [],
  "mc2ui.store.Students": [],
  "mc2ui.store.Suppliers": [],
  "mc2ui.store.TimeTables": [],
  "mc2ui.store.TrasparenzaDocumentsForm": [],
  "mc2ui.store.TrasparenzaLinkedDocuments": [],
  "mc2ui.store.TrasparenzaLinkedDocumentsForm": [],
  "mc2ui.store.TrasparenzaVoicesPickerTree": [],
  "mc2ui.store.TrasparenzaVoicesTree": [],
  "mc2ui.store.UserLogs": [],
  "mc2ui.store.WeekBorders": [],
  "mc2ui.store.Years": [],
  "mc2ui.view.AbsenceDecretoWin": [
    "widget.AbsenceDecretoWin"
  ],
  "mc2ui.view.AbsenceNewWin": [
    "widget.AbsenceNewWin"
  ],
  "mc2ui.view.AdminDataLookupWin": [
    "widget.AdminDataLookupWin"
  ],
  "mc2ui.view.AdminParametersWin": [
    "widget.AdminParametersWin"
  ],
  "mc2ui.view.AlboAreaEditWin": [
    "widget.AlboAreaEditWin"
  ],
  "mc2ui.view.AlboAreasWin": [
    "widget.AlboAreasWin"
  ],
  "mc2ui.view.AlboCategoriesWin": [
    "widget.AlboCategoriesWin"
  ],
  "mc2ui.view.AlboCategoryEditWin": [
    "widget.AlboCategoryEditWin"
  ],
  "mc2ui.view.AlboEntitiesWin": [
    "widget.AlboEntitiesWin"
  ],
  "mc2ui.view.AlboEntityEditWin": [
    "widget.AlboEntityEditWin"
  ],
  "mc2ui.view.AlboPnl": [
    "widget.AlboPnl"
  ],
  "mc2ui.view.AlboPublicationEditWin": [
    "widget.AlboPublicationEditWin"
  ],
  "mc2ui.view.AlboPublicationExtensionWin": [
    "widget.AlboPublicationExtensionWin"
  ],
  "mc2ui.view.AlboPublicationHistoryWin": [
    "widget.AlboPublicationHistoryWin"
  ],
  "mc2ui.view.AlboPublicationLinkedDocumentsPickerWin": [
    "widget.AlboPublicationLinkedDocumentsPickerWin"
  ],
  "mc2ui.view.AlboPublicationLinkedDocumentsWin": [
    "widget.AlboPublicationLinkedDocumentsWin"
  ],
  "mc2ui.view.ArchiveClassWin": [
    "widget.ArchiveClassWin"
  ],
  "mc2ui.view.ArchiveDashboardWin": [
    "widget.ArchiveDashboardWin"
  ],
  "mc2ui.view.ArchiveDocumentArchiveWin": [
    "widget.ArchiveDocumentArchiveWin"
  ],
  "mc2ui.view.ArchiveDocumentCheckWin": [
    "widget.ArchiveDocumentCheckWin"
  ],
  "mc2ui.view.ArchiveDocumentEditWin": [
    "widget.ArchiveDocumentEditWin"
  ],
  "mc2ui.view.ArchiveDocumentMailWin": [
    "widget.ArchiveDocumentMailWin"
  ],
  "mc2ui.view.ArchiveDocumentUploadWin": [
    "widget.ArchiveDocumentUploadWin"
  ],
  "mc2ui.view.ArchiveDocumentViewWin": [
    "widget.ArchiveDocumentViewWin"
  ],
  "mc2ui.view.ArchiveDossierNewWin": [
    "widget.ArchiveDossierNewWin"
  ],
  "mc2ui.view.ArchiveDossierWin": [
    "widget.ArchiveDossierWin"
  ],
  "mc2ui.view.ArchiveMailSendWin": [
    "widget.ArchiveMailSendWin"
  ],
  "mc2ui.view.ArchiveMailViewWin": [
    "widget.ArchiveMailViewWin"
  ],
  "mc2ui.view.ArchiveManagementWin": [
    "widget.ArchiveManagementWin"
  ],
  "mc2ui.view.ArchiveMassiveSignWin": [
    "widget.ArchiveMassiveSignWin"
  ],
  "mc2ui.view.ArchiveMetadataFileWin": [
    "widget.ArchiveMetadataFileWin"
  ],
  "mc2ui.view.ArchiveOfficeWin": [
    "widget.ArchiveOfficeWin"
  ],
  "mc2ui.view.ArchivePnl": [
    "widget.ArchivePnl"
  ],
  "mc2ui.view.ArchiveSignRemoteLogin": [
    "widget.ArchiveSignRemoteLogin"
  ],
  "mc2ui.view.ArchiveSignRemoteOtpWin": [
    "widget.ArchiveSignRemoteOtpWin"
  ],
  "mc2ui.view.ArchiveSignRemoteWin": [
    "widget.ArchiveSignRemoteWin"
  ],
  "mc2ui.view.ArchiveSignWin": [
    "widget.ArchiveSignWin"
  ],
  "mc2ui.view.ArchiveTemplateWin": [
    "widget.ArchiveTemplateWin"
  ],
  "mc2ui.view.CcpAdditionalEditWin": [
    "widget.CcpAdditionalEditWin"
  ],
  "mc2ui.view.CcpAdditionalTemplateWin": [
    "widget.CcpAdditionalTemplateWin"
  ],
  "mc2ui.view.CcpAdditionalsWin": [
    "widget.CcpAdditionalsWin"
  ],
  "mc2ui.view.CcpAdePrintFromFileWin": [
    "widget.CcpAdePrintFromFileWin"
  ],
  "mc2ui.view.CcpAeExportWin": [
    "widget.CcpAeExportWin"
  ],
  "mc2ui.view.CcpAttestazionePrintWin": [
    "widget.CcpAttestazionePrintWin"
  ],
  "mc2ui.view.CcpBollettiniPrintWin": [
    "widget.CcpBollettiniPrintWin"
  ],
  "mc2ui.view.CcpByCCPrintWin": [
    "widget.CcpByCCPrintWin"
  ],
  "mc2ui.view.CcpCashJournalPrintWin": [
    "widget.CcpCashJournalPrintWin"
  ],
  "mc2ui.view.CcpCategoriesWin": [
    "widget.CcpCategoriesWin"
  ],
  "mc2ui.view.CcpCategoryEditWin": [
    "widget.CcpCategoryEditWin"
  ],
  "mc2ui.view.CcpCorrispettiviPrintWin": [
    "widget.CcpCorrispettiviPrintWin"
  ],
  "mc2ui.view.CcpCreditDistributionWin": [
    "widget.CcpCreditDistributionWin"
  ],
  "mc2ui.view.CcpCreditTypeWin": [
    "widget.CcpCreditTypeWin"
  ],
  "mc2ui.view.CcpCreditUploadWin": [
    "widget.CcpCreditUploadWin"
  ],
  "mc2ui.view.CcpDepositSlipNewWin": [
    "widget.CcpDepositSlipNewWin"
  ],
  "mc2ui.view.CcpDepositSlipRowWin": [
    "widget.CcpDepositSlipRowWin"
  ],
  "mc2ui.view.CcpDepositWin": [
    "widget.CcpDepositWin"
  ],
  "mc2ui.view.CcpDichiarazionePrintWin": [
    "widget.CcpDichiarazionePrintWin"
  ],
  "mc2ui.view.CcpDiscountWin": [
    "widget.CcpDiscountWin"
  ],
  "mc2ui.view.CcpExcelMovementExportWin": [
    "widget.CcpExcelMovementExportWin"
  ],
  "mc2ui.view.CcpExportResidualsWin": [
    "widget.CcpExportResidualsWin"
  ],
  "mc2ui.view.CcpExtraTimeWin": [
    "widget.CcpExtraTimeWin"
  ],
  "mc2ui.view.CcpInvoiceAccountHolderWin": [
    "widget.CcpInvoiceAccountHolderWin"
  ],
  "mc2ui.view.CcpInvoiceEditWin": [
    "widget.CcpInvoiceEditWin"
  ],
  "mc2ui.view.CcpInvoiceNewWin": [
    "widget.CcpInvoiceNewWin"
  ],
  "mc2ui.view.CcpInvoiceTransmissionWin": [
    "widget.CcpInvoiceTransmissionWin"
  ],
  "mc2ui.view.CcpLinkedAdditionalsPickerWin": [
    "widget.CcpLinkedAdditionalsPickerWin"
  ],
  "mc2ui.view.CcpLinkedAdditionalsWin": [
    "widget.CcpLinkedAdditionalsWin"
  ],
  "mc2ui.view.CcpLinkedPaymentsWin": [
    "widget.CcpLinkedPaymentsWin"
  ],
  "mc2ui.view.CcpMovementCopyWin": [
    "widget.CcpMovementCopyWin"
  ],
  "mc2ui.view.CcpMovementEditWin": [
    "widget.CcpMovementEditWin"
  ],
  "mc2ui.view.CcpPaymentEditWin": [
    "widget.CcpPaymentEditWin"
  ],
  "mc2ui.view.CcpPaymentMassiveEditWin": [
    "widget.CcpPaymentMassiveEditWin"
  ],
  "mc2ui.view.CcpPnl": [
    "widget.CcpPnl"
  ],
  "mc2ui.view.CcpPrintCategoriesWin": [
    "widget.CcpPrintCategoriesWin"
  ],
  "mc2ui.view.CcpPrintCategoryMovementTypeWin": [
    "widget.CcpPrintCategoryMovementTypeWin"
  ],
  "mc2ui.view.CcpPrintTypeWin": [
    "widget.CcpPrintTypeWin"
  ],
  "mc2ui.view.CcpPvrUploadWin": [
    "widget.CcpPvrUploadWin"
  ],
  "mc2ui.view.CcpReceiptEditMultiWin": [
    "widget.CcpReceiptEditMultiWin"
  ],
  "mc2ui.view.CcpReceiptEditWin": [
    "widget.CcpReceiptEditWin"
  ],
  "mc2ui.view.CcpReminderCustomWin": [
    "widget.CcpReminderCustomWin"
  ],
  "mc2ui.view.CcpResidualsWin": [
    "widget.CcpResidualsWin"
  ],
  "mc2ui.view.CcpServiziWin": [
    "widget.CcpServiziWin"
  ],
  "mc2ui.view.CcpSignLoginWin": [
    "widget.CcpSignLoginWin"
  ],
  "mc2ui.view.CcpStudentsMissing": [
    "widget.CcpStudentsMissing"
  ],
  "mc2ui.view.CcpTypeEditWin": [
    "widget.CcpTypeEditWin"
  ],
  "mc2ui.view.CcpTypesWin": [
    "widget.CcpTypesWin"
  ],
  "mc2ui.view.CcpYearsToSaveWin": [
    "widget.CcpYearsToSaveWin"
  ],
  "mc2ui.view.ContactAddWin": [
    "widget.ContactAddWin"
  ],
  "mc2ui.view.DispatcherView": [],
  "mc2ui.view.EasyImportWin": [
    "widget.EasyImportWin"
  ],
  "mc2ui.view.EmployeeAbsenceCopyWin": [
    "widget.EmployeeAbsenceCopyWin"
  ],
  "mc2ui.view.EmployeeAbsenceStackEditWin": [
    "widget.EmployeeAbsenceStackEditWin"
  ],
  "mc2ui.view.EmployeeAbsenceStackWin": [
    "widget.EmployeeAbsenceStackWin"
  ],
  "mc2ui.view.EmployeeAbsencesPrintWin": [
    "widget.EmployeeAbsencesPrintWin"
  ],
  "mc2ui.view.EmployeeDayPrintWin": [
    "widget.EmployeeDayPrintWin"
  ],
  "mc2ui.view.EmployeeEntriesExitsPrint_Win": [
    "widget.EmployeeEntriesExitsPrint_Win"
  ],
  "mc2ui.view.EmployeeHolidayCalendarWin": [
    "widget.EmployeeHolidayCalendarWin"
  ],
  "mc2ui.view.EmployeeLastLockedMonthPrintWin": [
    "widget.EmployeeLastLockedMonthPrintWin"
  ],
  "mc2ui.view.EmployeeParametersCopyWin": [
    "widget.EmployeeParametersCopyWin"
  ],
  "mc2ui.view.EmployeePnl": [
    "widget.EmployeePnl"
  ],
  "mc2ui.view.EmployeePresencesPrintWin": [
    "widget.EmployeePresencesPrintWin"
  ],
  "mc2ui.view.EmployeeProjectsEditWin": [
    "widget.EmployeeProjectsEditWin"
  ],
  "mc2ui.view.EmployeeProjectsHourTypeEditWin": [
    "widget.EmployeeProjectsHourTypeEditWin"
  ],
  "mc2ui.view.EmployeeProjectsHourTypesWin": [
    "widget.EmployeeProjectsHourTypesWin"
  ],
  "mc2ui.view.EmployeeProjectsProjectEditWin": [
    "widget.EmployeeProjectsProjectEditWin"
  ],
  "mc2ui.view.EmployeeResidualsPrint_Win": [
    "widget.EmployeeResidualsPrint_Win"
  ],
  "mc2ui.view.EmployeeStacksCopyWin": [
    "widget.EmployeeStacksCopyWin"
  ],
  "mc2ui.view.EmployeeTimetablePrintWin": [
    "widget.EmployeeTimetablePrintWin"
  ],
  "mc2ui.view.ExportEasyWin": [
    "widget.ExportEasyWin"
  ],
  "mc2ui.view.GroupEditWin": [
    "widget.GroupEditWin"
  ],
  "mc2ui.view.HomePnl": [
    "widget.HomePnl"
  ],
  "mc2ui.view.ImportPaymentsEasyWin": [
    "widget.ImportPaymentsEasyWin"
  ],
  "mc2ui.view.InstituteEditWin": [
    "widget.InstituteEditWin"
  ],
  "mc2ui.view.InstitutesWin": [
    "widget.InstitutesWin"
  ],
  "mc2ui.view.InvoiceEditWin": [
    "widget.InvoiceEditWin"
  ],
  "mc2ui.view.LoginPasswordExpired": [
    "widget.LoginPasswordExpired"
  ],
  "mc2ui.view.LoginView": [
    "widget.LoginView"
  ],
  "mc2ui.view.MailingListWin": [
    "widget.MailingListWin"
  ],
  "mc2ui.view.MainView": [
    "widget.MainView"
  ],
  "mc2ui.view.MpExtraTimeEditWin": [
    "widget.MpExtraTimeEditWin"
  ],
  "mc2ui.view.MyPanel27": [
    "widget.mypanel27"
  ],
  "mc2ui.view.MyPanel29": [
    "widget.mypanel29"
  ],
  "mc2ui.view.MyPanel3": [
    "widget.mypanel3"
  ],
  "mc2ui.view.MyPanel9": [
    "widget.mypanel9"
  ],
  "mc2ui.view.PaswdEditWin": [
    "widget.PaswdEditWin"
  ],
  "mc2ui.view.PresencesNewWin": [
    "widget.PresencesNewWin"
  ],
  "mc2ui.view.ProtocolActionsWin": [
    "widget.ProtocolActionsWin"
  ],
  "mc2ui.view.ProtocolCorrespondentEditOriginPickerWin": [
    "widget.ProtocolCorrespondentEditOriginPickerWin"
  ],
  "mc2ui.view.ProtocolCorrespondentEditWin": [
    "widget.ProtocolCorrespondentEditWin"
  ],
  "mc2ui.view.ProtocolCorrespondentsWin": [
    "widget.ProtocolCorrespondentsWin"
  ],
  "mc2ui.view.ProtocolLinkedCorrespondentsWin": [
    "widget.ProtocolLinkedCorrespondentsWin"
  ],
  "mc2ui.view.ProtocolLinkedDocumentsWin": [
    "widget.ProtocolLinkedDocumentsWin"
  ],
  "mc2ui.view.ProtocolLinkedProtocolsWin": [
    "widget.ProtocolLinkedProtocolsWin"
  ],
  "mc2ui.view.ProtocolPnl": [
    "widget.ProtocolPnl"
  ],
  "mc2ui.view.ProtocolPrintsBarcodeWin": [
    "widget.ProtocolPrintsBarcodeWin"
  ],
  "mc2ui.view.ProtocolProtocolHistoryWin": [
    "widget.ProtocolProtocolHistoryWin"
  ],
  "mc2ui.view.ProtocolProtocolNewLinkedCorrespondentsPickerWin": [
    "widget.ProtocolProtocolNewLinkedCorrespondentsPickerWin"
  ],
  "mc2ui.view.ProtocolProtocolNewLinkedDocumentsPickerWin": [
    "widget.ProtocolProtocolNewLinkedDocumentsPickerWin"
  ],
  "mc2ui.view.ProtocolProtocolNewLinkedProtocolsPickerWin": [
    "widget.ProtocolProtocolNewLinkedProtocolsPickerWin"
  ],
  "mc2ui.view.ProtocolProtocolNewWin": [
    "widget.ProtocolProtocolNewWin"
  ],
  "mc2ui.view.ProtocolSendMethodEditWin": [
    "widget.ProtocolSendMethodEditWin"
  ],
  "mc2ui.view.ProtocolSendMethodsWin": [
    "widget.ProtocolSendMethodsWin"
  ],
  "mc2ui.view.ProtocolSubjectKindEditWin": [
    "widget.ProtocolSubjectKindEditWin"
  ],
  "mc2ui.view.ProtocolSubjectKindsWin": [
    "widget.ProtocolSubjectKindsWin"
  ],
  "mc2ui.view.ProtocolTypeEditWin": [
    "widget.ProtocolTypeEditWin"
  ],
  "mc2ui.view.ProtocolTypesWin": [
    "widget.ProtocolTypesWin"
  ],
  "mc2ui.view.RaccoltaAssignWin": [
    "widget.RaccoltaAssignWin"
  ],
  "mc2ui.view.RegisterAddWin": [
    "widget.RegisterAddWin"
  ],
  "mc2ui.view.RegisterDetailWin": [
    "widget.RegisterDetailWin"
  ],
  "mc2ui.view.RegisterWin": [
    "widget.RegisterWin"
  ],
  "mc2ui.view.ReminderDetailWin": [
    "widget.ReminderDetailWin"
  ],
  "mc2ui.view.SettingsPanel": [
    "widget.SettingsPanel"
  ],
  "mc2ui.view.TimeTableCopyWeekWin": [
    "widget.TimeTableCopyWeekWin"
  ],
  "mc2ui.view.TimeTableDeleteWin": [
    "widget.TimeTableDeleteWin"
  ],
  "mc2ui.view.TimeTableEditWin": [
    "widget.TimeTableEditWin"
  ],
  "mc2ui.view.TrasparenzaLinkedDocumentsPickerWin": [
    "widget.TrasparenzaLinkedDocumentsPickerWin"
  ],
  "mc2ui.view.TrasparenzaLinkedDocumentsWin": [
    "widget.TrasparenzaLinkedDocumentsWin"
  ],
  "mc2ui.view.TrasparenzaPnl": [
    "widget.TrasparenzaPnl"
  ],
  "mc2ui.view.TrasparenzaVoiceEditWin": [
    "widget.TrasparenzaVoiceEditWin"
  ],
  "mc2ui.view.TrasparenzaVoicePickerWin": [
    "widget.TrasparenzaVoicePickerWin"
  ],
  "mc2ui.view.UploadDocumentReportWin": [
    "widget.UploadDocumentReportWin"
  ],
  "mc2ui.view.UserEditWin": [
    "widget.UserEditWin"
  ]
});

Ext.setVersion("ext-theme-base", "4.2.2");
Ext.setVersion("ext-theme-classic", "4.2.2");
Ext.setVersion("ext-theme-neutral", "4.2.2");

/**
 * This file is generated by Sencha Cmd and should NOT be edited.  It is
 * provided to support globbing requires, custom xtypes, and other
 * metadata-driven class system features
 */


/**
 * Sencha Blink - Development
 * <AUTHOR> Nguyen <<EMAIL>>
 */
(function() {
    var head = document.head || document.getElementsByTagName('head')[0];

    function write(content) {
        document.write(content);
    }

    function addMeta(name, content) {
        var meta = document.createElement('meta');

        meta.setAttribute('name', name);
        meta.setAttribute('content', content);
        head.appendChild(meta);
    }

    var xhr = new XMLHttpRequest();
    xhr.open('GET', 'bootstrap.json', false);
    xhr.send(null);

    var options = eval("(" + xhr.responseText + ")"),
        scripts = options.js || [],
        styleSheets = options.css || [],
        i, ln, path, platform, theme, exclude;

    if(options.platform && options.platforms && options.platforms[options.platform] && options.platforms[options.platform].js) {
        scripts = options.platforms[options.platform].js.concat(scripts);
    }

    if (navigator.userAgent.match(/IEMobile\/10\.0/)) {
        var msViewportStyle = document.createElement("style");
        msViewportStyle.appendChild(
            document.createTextNode(
                "@media screen and (orientation: portrait) {" +
                    "@-ms-viewport {width: 320px !important;}" +
                "}" +
                "@media screen and (orientation: landscape) {" +
                    "@-ms-viewport {width: 560px !important;}" +
                "}"
            )
        );
        document.getElementsByTagName("head")[0].appendChild(msViewportStyle);
    }

    addMeta('viewport', 'width=device-width, initial-scale=1.0, maximum-scale=1.0, minimum-scale=1.0, user-scalable=no');
    addMeta('apple-mobile-web-app-capable', 'yes');
    addMeta('apple-touch-fullscreen', 'yes');

    if (!window.Ext) {
        window.Ext = {};
    }
    Ext.microloaded = true;

    var filterPlatform = window.Ext.filterPlatform = function(platform) {
        var profileMatch = false,
            ua = navigator.userAgent,
            j, jln;

        platform = [].concat(platform);

        function isPhone(ua) {
            var isMobile = /Mobile(\/|\s)/.test(ua);

            // Either:
            // - iOS but not iPad
            // - Android 2
            // - Android with "Mobile" in the UA

            return /(iPhone|iPod)/.test(ua) ||
                      (!/(Silk)/.test(ua) && (/(Android)/.test(ua) && (/(Android 2)/.test(ua) || isMobile))) ||
                      (/(BlackBerry|BB)/.test(ua) && isMobile) ||
                      /(Windows Phone)/.test(ua);
        }

        function isTablet(ua) {
            return !isPhone(ua) && (/iPad/.test(ua) || /Android|Silk/.test(ua) || /(RIM Tablet OS)/.test(ua) ||
                (/MSIE 10/.test(ua) && /; Touch/.test(ua)));
        }

        // Check if the ?platform parameter is set in the URL
        var paramsString = window.location.search.substr(1),
            paramsArray = paramsString.split("&"),
            params = {},
            testPlatform, i;

        for (i = 0; i < paramsArray.length; i++) {
            var tmpArray = paramsArray[i].split("=");
            params[tmpArray[0]] = tmpArray[1];
        }

        testPlatform = params.platform;
        if (testPlatform) {
            return platform.indexOf(testPlatform) != -1;
        }

        for (j = 0, jln = platform.length; j < jln; j++) {
            switch (platform[j]) {
                case 'phone':
                    profileMatch = isPhone(ua);
                    break;
                case 'tablet':
                    profileMatch = isTablet(ua);
                    break;
                case 'desktop':
                    profileMatch = !isPhone(ua) && !isTablet(ua);
                    break;
                case 'ios':
                    profileMatch = /(iPad|iPhone|iPod)/.test(ua);
                    break;
                case 'android':
                    profileMatch = /(Android|Silk)/.test(ua);
                    break;
                case 'blackberry':
                    profileMatch = /(BlackBerry|BB)/.test(ua);
                    break;
                case 'safari':
                    profileMatch = /Safari/.test(ua) && !(/(BlackBerry|BB)/.test(ua));
                    break;
                case 'chrome':
                    profileMatch = /Chrome/.test(ua);
                    break;
                case 'ie10':
                    profileMatch = /MSIE 10/.test(ua);
                    break;
                case 'windows':
                    profileMatch = /MSIE 10/.test(ua) || /Trident/.test(ua);
                    break;
                case 'tizen':
                    profileMatch = /Tizen/.test(ua);
                    break;
                case 'firefox':
                    profileMatch = /Firefox/.test(ua);
            }
            if (profileMatch) {
                return true;
            }
        }
        return false;
    };


    for (i = 0,ln = styleSheets.length; i < ln; i++) {
        path = styleSheets[i];

        if (typeof path != 'string') {
            platform = path.platform;
            exclude = path.exclude;
            theme = path.theme;
            path = path.path;
        }

        if (platform) {
            if (!filterPlatform(platform) || filterPlatform(exclude)) {
                continue;
            }

            if(!Ext.theme) {
                Ext.theme = {};
            }
            if(!Ext.theme.name) {
                Ext.theme.name = theme || 'Default';
            }
        }

        write('<link rel="stylesheet" href="'+path+'">');
    }

    for (i = 0,ln = scripts.length; i < ln; i++) {
        path = scripts[i];

        if (typeof path != 'string') {
            platform = path.platform;
            exclude = path.exclude;
            path = path.path;
        }

        if (platform) {
            if (!filterPlatform(platform) || filterPlatform(exclude)) {
                continue;
            }
        }

        write('<script src="'+path+'"></'+'script>');
    }

})();
