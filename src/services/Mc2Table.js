import {TabulatorFull as Tabulator} from 'tabulator-tables';

class Mc2Table extends Tabulator {

    constructor(element, options) {

        options = {
            layout: "fitColumns",
            data: [],
            dataLoader: true,
            dataLoaderLoading: "Caricamento in corso",
            dataLoaderError: "Errore durante il caricamento",
            ...options
        }

        super(element, options);
    }

}

export default Mc2Table;
