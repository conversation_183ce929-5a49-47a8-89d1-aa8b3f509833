async function apiCall(url, method = 'GET', body = null, headers = {}) {
    try {
        const options = {
            method,
            headers: {
                'Content-Type': 'application/json',
                ...headers,
            },
        };

        if (body) {
            options.body = JSON.stringify(body);
        }

        const response = await fetch('/mc2-api' + url, options);

        if (!response.ok) {
            throw new Error(`HTTP error! status: ${response.status}`);
        }

        return await response.json();
    } catch (error) {
        console.error('API call failed:', error);
        throw error;
    }
}

export const get = async (url, headers = {}) => {
    return await apiCall(url, 'GET', null, headers);
};

export const post = async (url, body, headers = {}) => {
    return await apiCall(url, 'POST', body, headers);
};

export const update = async (url, body, headers = {}) => {
    return await apiCall(url, 'PUT', body, headers);
};

export const remove = async (url, headers = {}) => {
    return await apiCall(url, 'DELETE', null, headers);
};

