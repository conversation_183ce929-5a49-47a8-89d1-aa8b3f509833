


export default class SaldiView extends HTMLElement {

  constructor() {
    super();
  }

  render() {
    this.innerHTML = `
      <div class="filter-grid-layout">
          <saldi-filter class="filter-panel"></saldi-filter>
          <saldi-grid class="grid-panel"></saldi-grid>
      </div>

    `
  }

  connectedCallback() {
    this.render();

    const saldi = this.querySelector("saldi-filter")
    const grid = this.querySelector("saldi-grid")
    saldi.onFilterChange = (filter) => grid.updateSaldi(filter)

  }

}

customElements.define("saldi-panel", SaldiView)
