import FilterPanel from "../../components/FilterPanel.js";


export default class SaldiFilter extends HTMLElement {
  onFilterChange = () => { }

  constructor() {
    super()
  }

  render() {
    this.innerHTML = `
      <div class="title">Filtri</div>
      <form>
        <text-field label="Nome studente:" name="query" placeholder="Cerca ..."></text-field>
      </form>
    `
    //super.render();

    this.querySelector("form").addEventListener('submit', (event) => {

      const formData = new FormData(event.target);
      const formObject = Object.fromEntries(formData.entries());

      this.onFilterChange(formObject);
      event.preventDefault();
    });

  }

  connectedCallback() {
    this.render();
  }

}

customElements.define("saldi-filter", SaldiFilter)


