
export default class StudentiView extends HTMLElement {

    constructor() {
      super()
    }

    render() {
      this.innerHTML = `
        <div class="filter-grid-layout">
          <studenti-filter class="filter-panel"></studenti-filter>
          <studenti-tabs class="grid-panel"></studenti-tabs>
        </div>

        `
    }

    connectedCallback() {
      this.render();

      const studentiFilter = this.querySelector("studenti-filter")
      const studentiTabs = this.querySelector("studenti-tabs")
      studentiFilter.onFilterChange = (filter) => studentiTabs.updateStudente(filter)

    }

  }

  customElements.define("studenti-panel", StudentiView)


