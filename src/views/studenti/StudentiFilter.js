import { getAnniScolastici, getAnnoScolasticoCorrente } from "../../stores/AnniStore.js";
import Mc2Table from "../../services/Mc2Table.js";
import { getStudenti, getStatiStudenteCombo } from "../../stores/StudentiStore.js";

export default class StudentiFilter extends HTMLElement {

  onFilterChange = () => { }

  constructor() {
    super()

  }

  async render() {
    const anniScolastici = await getAnniScolastici();
    const getAnnoCorrente = await getAnnoScolasticoCorrente();
    const statiStudente = await getStatiStudenteCombo();

    this.innerHTML = `
      <div class="title">Elenco studenti</div>
      <form style="height: auto;">

        <text-field name="query" placeholder="Cerca ..."></text-field>

        <combo-box
          name="anno_scolastico"
          options='${JSON.stringify(anniScolastici)}'
          default='${getAnnoCorrente}'
          >
        </combo-box>

        <combo-box
          name="stato_studente"
          firstEmptyOption='Tutti'
          options='${JSON.stringify(statiStudente)}'>
        </combo-box>

      </form>
      <div id="table" style="flex:1"></div>
      `

    this.table = new Mc2Table(this.querySelector("#table"), {
      columnDefaults: {
        headerSort: false
      },
      columns: [
        {
          title: "Nome", formatter: function (cell, formatterParams, onRendered) {
            const row = cell.getRow();
            return `<studente-item
              cognome="${row.getData().surname}"
              nome="${row.getData().name}"
              classe="${row.getData().class + row.getData().section}"
              indirizzo="${row.getData().school_address_code}"
              parenti="${row.getData().genitori}"
            />`
          }
        },
      ],
    });
    this.table.on("tableBuilt", () => this.updateStudenti());

  }

  connectedCallback() {
    this.render();

  }

  async updateStudenti() {
    this.table.dataLoader.alertLoader();
    const form = this.querySelector("form");
    const formData = new FormData(form);
    const filter = Object.fromEntries(formData.entries());
    const studenti = await getStudenti(filter);
    this.table.setData(studenti.results);
    this.table.dataLoader.clearAlert();
  }

}

customElements.define("studenti-filter", StudentiFilter)

