export default class FilterGridLayout extends HTMLElement {

    html = '';

    constructor() {
        super();
    }

    connectedCallback() {
        this.render();

    }

    render() {
        const slot = this.innerHTML;
        this.innerHTML = `
        <style>
            .filter-grid-container {
                display: flex;
                flex-direction: row;
                gap: 5px;
                padding: 5px;
                height: 100%;
                width: 100%;
            }
        </style>
            <div class="mc2 filter-grid-container">
                 ${slot}
            </div>
        `;

    }
}

customElements.define('filter-grid-layout', FilterGridLayout);





