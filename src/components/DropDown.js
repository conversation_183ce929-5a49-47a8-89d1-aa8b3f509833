class DropDownMenu extends HTMLElement {
    constructor() {
      super();
    }

    connectedCallback() {
      // Evita di renderizzare di nuovo se è già stato fatto
      if (this.querySelector('.dropdown-container')) return;

      const title = this.getAttribute('title') || 'Menu';

      // Crea la struttura del componente e sposta i figli passati all'interno del menu
      const container = document.createElement('div');
      container.className = 'mc2 dropdown-container';

      const button = document.createElement('button');
      button.textContent = title;

      const menu = document.createElement('div');
      menu.className = 'mc2 dropdown-menu';
      menu.style.display = 'none';

      // Sposta tutti i contenuti interni (passati dall'utente) nel menu
      const fragment = document.createDocumentFragment();
      while (this.firstChild) {
        fragment.appendChild(this.firstChild);
      }
      menu.appendChild(fragment);

      button.addEventListener('click', (e) => {
        e.stopPropagation();
        menu.style.display = menu.style.display === 'block' ? 'none' : 'block';
      });

      // Chiude il menu se si fa clic fuori
      document.addEventListener('click', () => {
        menu.style.display = 'none';
      });

      container.appendChild(button);
      container.appendChild(menu);
      this.appendChild(container);
    }
  }

  customElements.define('dropdown-menu', DropDownMenu);