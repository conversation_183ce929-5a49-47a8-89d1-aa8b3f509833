import { Container } from "./Container.js";


export default class BaseForm extends Container {

  styles = {
    display: 'flex',
    flexDirection: 'column',
    width: '100%',
    height: '100%',
    'row-gap': '5px',
  }

  constructor() {
    super()
  }


  render() {
    const slot = this.innerHTML;


    this.innerHTML = `
      <form>
        ${slot}
      </form>
    `

  }

  connectedCallback() {
    super.connectedCallback();
    this.render();

    let form = this.querySelector("form");
    for (const [key, value] of Object.entries(this.styles)) {
      form.style[key] = value;
    }
  }

  applyStyles() {


  }



}

customElements.define("base-form", BaseForm)
