import { Container } from "./Container";

export default class FilterPanel extends HTMLElement {

    constructor() {
        super();
    }

    render() {
        const slot = this.innerHTML;
        const title = this.getAttribute('title') || 'Filtri';
        const header = title ? `<div class="title">${title}</div>` : '';

        this.innerHTML = `
            <style>
                .title {
                    font-weight: bold;
                    background-color: #a8c5e5;
                    margin: 0;
                    padding: 10px;
                    color: #333;
                    border-radius: 2px 2px 0 0;
                    align-items: center;
                }


            </style>

            <div class="filter-panel">
                ${header}
                ${slot}
            </div>


        `;

    }



}

