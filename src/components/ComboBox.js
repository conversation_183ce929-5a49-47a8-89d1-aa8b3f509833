export default class ComboBox extends HTMLElement {

    constructor() {
        super();
    }

    connectedCallback() {
        this.render();
    }

    render() {
        // Get attributes with default values
        const labelText = this.getAttribute('label') || '';
        const inputName = this.getAttribute('name') || 'query';
        const optionsData = JSON.parse(this.getAttribute('options') || '[]');
        const defaultValue = this.getAttribute('default') || null;
        const firstEmptyOption = this.getAttribute('firstEmptyOption') || '';


        let options = optionsData.map(item => {
            return `<option value="${item.value}" ${defaultValue === item.value ? 'selected' : ''}>${item.name}</option>`;
        }).join('');
        if (firstEmptyOption) {
            options = `<option value="">${firstEmptyOption}</option>` + options;
        }


        this.innerHTML = `
            <div>
                <label for="filter-input">${labelText}</label>
                <select name="${inputName}" >
                    ${options}
                </select>
            </div>
        `;

        const select = this.querySelector('select');
        select.addEventListener('change', this.updatePlaceholderClass);
    }

    updatePlaceholderClass = (a,b) => {
        console.log(a, b);
        const select = this.querySelector('select');
        if (select.value === '') {
            select.classList.add('placeholder');
        } else {
            select.classList.remove('placeholder');
        }
    };
}

customElements.define('combo-box', ComboBox);