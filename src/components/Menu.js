
export default class Menu extends HTMLElement {


    constructor() {
        super()
    }

    connectedCallback() {

        this.innerHTML = `
        <style>
            .menu-container {
            position: relative;
            display: inline-block;
            }

            .menu-icon {
            cursor: pointer;
            }

            .dropdown-menu {
            display: none;
            position: absolute;
            background-color: white;
            box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
            z-index: 1;
            min-width: 150px;
            padding: 10px;
            border-radius: 4px;
            }

            .dropdown-menu.show {
            display: block;
            }

            .dropdown-item {
            padding: 8px 12px;
            cursor: pointer;
            }

            .dropdown-item:hover {
            background-color: #f1f1f1;
            }
        </style>
        <div class="menu-container">
            <div class="menu-icon">☰</div>
            <div class="dropdown-menu">
            <div class="dropdown-item">Option 1</div>
            <div class="dropdown-item">Option 2</div>
            <div class="dropdown-item">Option 3</div>
            </div>
        </div>
        <script>
            const menuIcon = this.querySelector('.menu-icon');
            const dropdownMenu = this.querySelector('.dropdown-menu');

            menuIcon.addEventListener('click', () => {
            dropdownMenu.classList.toggle('show');
            });

            document.addEventListener('click', (event) => {
            if (!this.contains(event.target)) {
                dropdownMenu.classList.remove('show');
            }
            });
        </script>
        `

    }

}

customElements.define("base-menu", Menu)
