import { Container } from "./Container";

export default class BasePanel extends Container {

    styles = {
        display: 'flex',
        flexDirection: 'column',
        width: '100%',
        height: '100%',
        border: '1px solid #7a9bc5',
        'border-radius': '4px'
    }

    constructor() {
        super();
    }

    static get observedAttributes() {
        return ['title'];
    }

    render() {
        const title = this.getAttribute('title') || 'Panel';
        const header = title ? `<div class="title">${title}</div>` : '';
        const slot = this.innerHTML;
        this.innerHTML = `
            <style>
                .title {
                    font-weight: bold;
                    background-color: #a8c5e5;
                    margin: 0;
                    padding: 10px;
                    color: #333;
                    border-radius: 2px 2px 0 0;
                    align-items: center;
                }
            </style>


            ${header}
            <div>
                ${slot}
            </div>
        `;

    }
}

customElements.define('base-panel', BasePanel);
