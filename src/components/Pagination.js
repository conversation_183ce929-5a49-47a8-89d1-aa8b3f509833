
import { createIcons, ChevronFirst, ChevronLast, ChevronLeft, ChevronRight } from 'lucide';


export default class GridPagination extends HTMLElement {
    constructor() {
        super();
    }

    onPageChange = () => { }

    connectedCallback() {
        this.render();
    }

    getCurrentPage() {
        return parseInt(this.getAttribute('current-page')) || 1;
    }

    getTotalPages() {
        return Math.max(Math.ceil(this.getTotal() / this.getPageSize()), 1);
    }

    getTotal() {
        return parseInt(this.getAttribute('total')) || 0;
    }

    getPageSize() {
        return parseInt(this.getAttribute('page-size')) || 50;
    }

    static get observedAttributes() {
        return ['total', 'current-page'];
    }

    attributeChangedCallback(name, oldValue, newValue) {
        this.render();
    }

    prepareData(page) {
        return {
            page: page,
            start: (page - 1) * this.getPageSize(),
            limit: this.getPageSize(),
        };
    }

    getInitialData() {
        return {
            page: 1,
            start: 0,
            limit: this.getPageSize(),
        }
    }

    getMaxElementCurrentPage() {
        return Math.min(this.getCurrentPage() * this.getPageSize(), this.getTotal());
    }

    render() {
        this.innerHTML = `
            <div class="pagination-pages">
                <button id="first-page"> <i data-lucide="chevron-first"></i> </button>
                <button id="prev-page"> <i data-lucide="chevron-left"></i> </button>
                <div>
                    <input type="number" min="1" max="${this.getTotalPages()}" value="${this.getCurrentPage()}" />
                    <span>/ ${this.getTotalPages()}</span>
                </div>
                <button id="next-page"> <i data-lucide="chevron-right"></i> </button>
                <button id="last-page"> <i data-lucide="chevron-last"></i> </button>
            </div>
            <div>
                Elementi ${((this.getCurrentPage() - 1) * this.getPageSize()) + 1} - ${this.getMaxElementCurrentPage()} di ${this.getTotal()}
            </div>
        `;

        this.querySelector("#first-page").addEventListener("click", (event) => this.onPageChange(this.prepareData(1)));
        this.querySelector("#last-page").addEventListener("click", (event) => this.onPageChange(this.prepareData(this.getTotalPages())));
        this.querySelector("#prev-page").addEventListener("click", (event) => {
            if (this.getCurrentPage() > 1) this.onPageChange(this.prepareData(this.getCurrentPage() - 1));
        });
        this.querySelector("#next-page").addEventListener("click", (event) => {
            if (this.getCurrentPage() < this.getTotalPages()) this.onPageChange(this.prepareData(this.getCurrentPage() + 1));
        });

        createIcons({
            icons: {
                ChevronFirst,
                ChevronLast,
                ChevronLeft,
                ChevronRight
            }
        });
    }


}

customElements.define('grid-pagination', GridPagination);