class StudenteItem extends HTMLElement {
    constructor() {
      super();
    }

    connectedCallback() {
        this.render();
    }

    render() {
        const nome = this.getAttribute('nome');
        const cognome = this.getAttribute('cognome');
        const classe = this.getAttribute('classe') || '';
        const indirizzo = this.getAttribute('indirizzo') || null;
        const parenti = this.getAttribute('parenti') || null;

        this.innerHTML = `
            <style>
            .student-item {
                display: flex;
                flex-direction: column;
                row-gap: 2px;

                & .parenti {
                    font-style: italic;
                    color:#999;
                }
            }
            </style>

            <div class="student-item">
                ${nome} ${cognome} ${(classe && indirizzo) ? `(${classe} - ${indirizzo})` : ''}
                ${(parenti ? `<span class="parenti">${parenti}</span>` : '')}
            </div>
        `;
    }
  }

  customElements.define('studente-item', StudenteItem);