import { createIcons, Search } from 'lucide';


export default class TextField extends HTMLElement {
    constructor() {
        super();

    }

    connectedCallback() {
        this.render();
    }

    render() {
        // Get attributes with default values
        const labelText = this.getAttribute('label') || '';
        const inputName = this.getAttribute('name') || 'query';
        const placeholder = this.getAttribute('placeholder') || 'Cerca ...';

        this.innerHTML = `
            <div>
                <label for="filter-input">${labelText}</label>
                <div style="display: flex; align-items: center;flex-direction: row;">
                    <input type="text" id="filter-input" name="${inputName}" placeholder="${placeholder}" />
                    <button>
                        <i data-lucide="search"></i>
                    </button>
                </div>
            </div>
        `;

        createIcons({
            icons: {
                Search,
            }
        });
    }
}

customElements.define('text-field', TextField);
