// Widget.js
export class Container extends HTMLElement {

    styles = {}

    constructor() {
        super();
    }

    connectedCallback() {
        this.applyStyles();
        this.render();
    }

    render() {
        this.innerHTML = `<div style="color: red;">Sovrascrivi 'render()' in ${this.tagName.toLowerCase()}</div>`;
    }

    update() {
        this.applyStyles();
        this.render();
    }

    static get observedAttributes() {
        return [];
    }

    attributeChangedCallback(name, oldValue, newValue) {
        if (oldValue !== newValue) {
            this.update();
        }
    }

    applyStyles() {

        for (const [key, value] of Object.entries(this.styles)) {
            this.style[key] = value;
        }
    }
}

customElements.define('base-container', Container);
