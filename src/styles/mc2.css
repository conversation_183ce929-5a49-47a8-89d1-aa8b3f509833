@charset "UTF-8";
.mc2 button {
  background-color: #b8d0eb;
  /* Versione più chiara */
  color: #333;
  /* Testo scuro per miglior contrasto */
  border: none;
  padding: 5px 10px;
  border-radius: 4px;
  cursor: pointer;
}
.mc2 button:hover {
  background-color: #9bbae0;
  /* Leggermente più scuro per hover */
}
.mc2 input {
  width: 100%;
  padding: 3px;
  border: 1px solid #7a9bc5;
  background-color: white;
  box-sizing: border-box;
}

.dropdown-container {
  position: relative;
  display: inline-block;
}

.dropdown-menu {
  position: absolute;
  background-color: white;
  border: 1px solid #ccc;
  box-shadow: 0px 8px 16px rgba(0, 0, 0, 0.2);
  padding: 10px;
  z-index: 1000;
  min-width: 150px;
}

.dropdown-menu button {
  display: block;
  width: 100%;
  text-align: left;
  background: none;
  border: none;
  padding: 8px 5px;
  cursor: pointer;
}

.dropdown-menu button:hover {
  background-color: #f0f0f0;
}

/** *
    Stile per la paginazione delle tabelle
 */
grid-pagination {
  padding: 5px;
  display: flex;
  justify-content: space-between;
  align-items: center;
}
grid-pagination input {
  width: 45px;
}
grid-pagination button {
  padding: 0px 5px;
  border-radius: 4px;
  background-color: #eeeeee;
  border: 1px solid #cccccc;
}
grid-pagination button:hover {
  background-color: #cccccc;
  /* Leggermente più scuro per hover */
}
grid-pagination .pagination-pages {
  display: flex;
  align-items: center;
  gap: 5px;
}
grid-pagination .pagination-pages .chevron-right {
  width: 20px;
  height: 20px;
}
grid-pagination .pagination-pages .chevron-left {
  width: 20px;
  height: 20px;
}
grid-pagination .pagination-pages .chevron-fisrt {
  width: 20px;
  height: 20px;
}
grid-pagination .pagination-pages .chevron-last {
  width: 20px;
  height: 20px;
}

/** *
    Stile base per la classica visualizzazione mc2 a due colonne filtro + griglia
 */
.filter-grid-layout {
  display: flex;
  flex-direction: row;
  gap: 5px;
  padding: 5px;
  height: 100%;
}
.filter-grid-layout .filter-panel {
  width: 320px;
  height: 100%;
  background-color: #f9f9f9;
  border: 1px solid #99bce8;
  border-radius: 4px;
  display: flex;
  flex-direction: column;
}
.filter-grid-layout .filter-panel form {
  display: flex;
  flex-direction: column;
  padding: 5px;
  row-gap: 10px;
  height: 100%;
  width: 100%;
  overflow: auto;
}
.filter-grid-layout .filter-panel .title {
  font-size: 16px;
  font-weight: bold;
  margin-bottom: 10px;
  color: #ffffff;
  background-color: #99bce8;
  padding: 5px;
  border: 1px solid #99bce8;
  border-radius: 3px 3px 0px 0px;
}
.filter-grid-layout .grid-panel {
  display: flex;
  flex-direction: column;
  flex: 1;
  height: 100%;
  overflow: auto;
}

/**
 * Stile per i vari tipi di campi
 */
text-field {
  display: flex;
  flex-direction: column;
  row-gap: 2px;
}
text-field label {
  color: #15428b;
}
text-field input {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 2px 0px 0px 2px;
  padding: 5px;
  background-color: #fff;
}
text-field button {
  background: #eee;
  border: 1px solid #ccc;
  cursor: pointer;
  border-left: 0px;
  border-radius: 0px 2px 2px 0px;
}
text-field button:hover {
  background-color: #ccc;
}

combo-box {
  display: flex;
  flex-direction: column;
  row-gap: 2px;
}
combo-box label {
  color: #15428b;
}
combo-box select {
  width: 100%;
  border: 1px solid #ccc;
  border-radius: 2px;
  padding: 5px;
  background-color: #FFF;
}

/**
    * Icons
    */
svg.lucide {
  width: 20px;
  height: 20px;
}

/*# sourceMappingURL=mc2.css.map */
