// ====================
// Variabili personalizzabili
// ====================

// Base colors and sizing
$backgroundColor: #ffffff; // White background
$textSize: 12px; // Text size as you had it

// Header styling
$headerBackgroundColor: #f2f2f2; // Light gray header background
$headerTextColor: #000000; // Black text for headers
$headerBorderColor: #cccccc; // Light gray border for headers
$headerBorderWidth: 1px;
$headerFontWeight: bold;

// Row styling
$rowBorderColor: #e0e0e0; // Light gray border between rows
$rowBorderWidth: 1px;
$rowOddBackgroundColor: #f9f9f9; // Very light gray for odd rows
$rowEvenBackgroundColor: #ffffff; // White for even rows
$rowHoverBackground: #e5e5e5; // Darker gray hover effect
$rowTextColor: #000000; // Black text for rows
// Cell styling
$cellPadding: 6px; // Padding inside cells
$cellBorderColor: #e0e0e0; // Light gray border between cells
$cellBorderWidth: 1px;

// Number column alignment
$textAlignRight: right !default; // Align numbers to the right

// Table border
$tableBorderWidth: 1px;
$tableBorderColor: #cccccc;

// Then import the core SCSS
@import "tabulator-tables/src/scss/tabulator.scss";

// Additional custom styles after import
.tabulator {
  border: $tableBorderWidth solid $tableBorderColor;

  .tabulator-header {
    .tabulator-col {
      background-color: $headerBackgroundColor;
      border-right: $headerBorderWidth solid $headerBorderColor;

      .tabulator-col-content {
        padding: $cellPadding;

        .tabulator-col-title {
          font-weight: $headerFontWeight;
        }
      }
    }
  }

  .tabulator-row {
    border-bottom: $rowBorderWidth solid $rowBorderColor;

    &.tabulator-row-even {
      background-color: $rowEvenBackgroundColor;
    }

    &.tabulator-row-odd {
      background-color: $rowOddBackgroundColor;
    }

    // Explicitly add hover styles with !important to ensure they're applied
    &:hover {
      background-color: $rowHoverBackground;
      cursor: pointer;
    }

    .tabulator-cell {
      padding: $cellPadding;
      border-right: $cellBorderWidth solid $cellBorderColor;

      // Make numeric columns right-aligned
      &[data-type="number"],
      &:nth-child(n+2) { // Assuming all columns except first are numeric
        text-align: $textAlignRight;
      }
    }
  }
}